"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.autoLimit = void 0;

const vutils_1 = require("@visactor/vutils");

function autoLimit(labels, config) {
    const {limitLength: limitLength, verticalLimitLength: verticalLimitLength, ellipsis: ellipsis = "...", orient: orient} = config;
    !(0, vutils_1.isEmpty)(labels) && (0, vutils_1.isValidNumber)(limitLength) && labels.forEach((label => {
        if (("top" === orient || "bottom" === orient) && Math.floor(label.AABBBounds.height()) <= limitLength) return;
        const direction = label.attribute.direction;
        if (("left" === orient || "right" === orient) && ("vertical" === direction && Math.floor(label.AABBBounds.height()) <= verticalLimitLength || "vertical" !== direction && Math.floor(label.AABBBounds.width()) <= limitLength)) return;
        const angle = label.attribute.angle;
        let limitLabelLength = 0 === angle || (0, vutils_1.isNil)(angle) ? "top" === orient || "bottom" === orient ? null : "vertical" === direction ? verticalLimitLength : limitLength : Math.abs(limitLength / Math.sin(angle));
        (0, vutils_1.isValidNumber)(label.attribute.maxLineWidth) && (limitLabelLength = (0, 
        vutils_1.isValidNumber)(limitLabelLength) ? Math.min(label.attribute.maxLineWidth, limitLabelLength) : label.attribute.maxLineWidth), 
        label.setAttributes({
            maxLineWidth: limitLabelLength,
            ellipsis: label.attribute.ellipsis || ellipsis
        });
    }));
}

exports.autoLimit = autoLimit;
//# sourceMappingURL=auto-limit.js.map
