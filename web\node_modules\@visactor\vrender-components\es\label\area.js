import { merge } from "@visactor/vutils";

import { LabelBase } from "./base";

import { labelingLineOrArea } from "./util";

export class AreaLabel extends LabelBase {
    constructor(attributes) {
        super(merge({}, AreaLabel.defaultAttributes, attributes)), this.name = "line-label";
    }
    getGraphicBounds(graphic, point = {}, position = "end") {
        var _a;
        if ("area" !== graphic.type) return super.getGraphicBounds(graphic, point);
        const points = (null === (_a = null == graphic ? void 0 : graphic.attribute) || void 0 === _a ? void 0 : _a.points) || [ point ], index = "start" === position ? 0 : points.length - 1;
        return {
            x1: points[index].x,
            x2: points[index].x,
            y1: points[index].y,
            y2: points[index].y
        };
    }
    labeling(textBounds, graphicBounds, position = "end", offset = 0) {
        return labelingLineOrArea(textBounds, graphicBounds, position, offset);
    }
}

AreaLabel.defaultAttributes = {
    textStyle: {
        fontSize: 12,
        fill: "#000",
        textAlign: "center",
        textBaseline: "middle",
        boundsPadding: [ -1, 0, -1, 0 ]
    },
    position: "end",
    offset: 6,
    pickable: !1
};
//# sourceMappingURL=area.js.map