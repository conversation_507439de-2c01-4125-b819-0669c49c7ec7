{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,0BAA0B,EAAE,MAAM,QAAQ,CAAC;AAEpD,MAAM,CAAC,MAAM,gCAAgC,GAAG,GAAG,EAAE;IACnD,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5F,CAAC,CAAC", "file": "index.js", "sourcesContent": ["import { Factory } from '@visactor/vgrammar-core';\n\nimport { transform } from './wordcloud-shape';\n\nexport { WORDCLOUD_SHAPE_HOOK_EVENT } from './util';\n\nexport const registerWordCloudShapeTransforms = () => {\n  Factory.registerTransform('wordcloudShape', { transform, markPhase: 'beforeJoin' }, true);\n};\n"]}