"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.isEqual = void 0;

const vutils_1 = require("@visactor/vutils"), isEqual = (key, current, target, deep = !0) => {
    if (current === target) return !0;
    if ((0, vutils_1.isNil)(current) || (0, vutils_1.isNil)(target)) return (0, vutils_1.isNil)(current) && (0, 
    vutils_1.isNil)(target);
    if (!(0, vutils_1.isObjectLike)(current) && !(0, vutils_1.isObjectLike)(target)) return current === target;
    const c = (0, vutils_1.isArray)(current) ? current : current[key], t = (0, vutils_1.isArray)(target) ? target : target[key];
    return c === t || !1 !== deep && ((0, vutils_1.isArray)(t) ? !(!(0, vutils_1.isArray)(c) || t.length !== c.length || !t.every(((v, i) => v === c[i]))) : !!(0, 
    vutils_1.isObject)(t) && !(!(0, vutils_1.isObject)(c) || Object.keys(t).length !== Object.keys(c).length || !Object.keys(t).every((k => (0, 
    exports.isEqual)(k, t, c)))));
};

exports.isEqual = isEqual;
//# sourceMappingURL=isEqual.js.map