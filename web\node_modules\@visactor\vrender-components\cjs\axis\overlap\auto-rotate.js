"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.getYAxisLabelAlign = exports.getXAxisLabelAlign = exports.rotateXAxis = exports.rotateYAxis = exports.autoRotate = void 0;

const vutils_1 = require("@visactor/vutils"), util_1 = require("./util");

function autoRotate(items, rotateConfig) {
    if ((0, vutils_1.isEmpty)(items)) return;
    const {orient: orient, labelRotateAngle: labelRotateAngle = [ 0, 45, 90 ]} = rotateConfig;
    if (0 === labelRotateAngle.length || items.some((item => !!item.attribute.angle))) return;
    let i = 0, n = 0;
    for (labelRotateAngle && labelRotateAngle.length > 0 && (n = labelRotateAngle.length); i < n; ) {
        const angle = labelRotateAngle[i++];
        if (items.forEach((item => {
            item.attribute.angle = (0, vutils_1.degreeToRadian)(angle);
        })), tryRotate(orient, items), !hasIntersect(items)) break;
    }
}

function hasIntersect(items) {
    for (let i = 1; i < items.length; i++) if ((0, util_1.itemIntersect)(items[i - 1], items[i])) return !0;
    return !1;
}

function tryRotate(orient, items) {
    "bottom" !== orient && "top" !== orient || rotateXAxis(orient, items), "left" !== orient && "right" !== orient || rotateYAxis(orient, items), 
    (0, util_1.genRotateBounds)(items);
}

function clampAngle(angle = 0) {
    if (angle < 0) for (;angle < 0; ) angle += 2 * Math.PI;
    if (angle > 0) for (;angle >= 2 * Math.PI; ) angle -= 2 * Math.PI;
    return angle;
}

function rotateYAxis(orient, items) {
    items.forEach(((item, i) => {
        item.attribute.angle && item.setAttributes(Object.assign(Object.assign({}, getYAxisLabelAlign(orient, item.attribute.angle)), {
            angle: clampAngle(item.attribute.angle)
        }));
    }));
}

function rotateXAxis(orient, items) {
    items.forEach((item => {
        item.attribute.angle && item.setAttributes(Object.assign(Object.assign({}, getXAxisLabelAlign(orient, item.attribute.angle)), {
            angle: clampAngle(item.attribute.angle)
        }));
    }));
}

function getXAxisLabelAlign(orient, angle = 0) {
    let align = [ "center", "left", "left", "left", "center", "right", "right", "right", "left" ], baseline = [ "top", "top", "middle", "bottom", "bottom", "bottom", "middle", "top", "top" ];
    "top" === orient && (align = [ "center", "right", "right", "right", "center", "left", "left", "left", "right" ], 
    baseline = [ "bottom", "bottom", "middle", "top", "top", "top", "middle", "bottom", "bottom" ]);
    const step = (angle = clampAngle(angle)) / (.5 * Math.PI);
    let index;
    return index = step === Math.floor(step) ? 2 * Math.floor(step) : 2 * Math.floor(step) + 1, 
    {
        textAlign: align[index],
        textBaseline: baseline[index]
    };
}

function getYAxisLabelAlign(orient, angle = 0) {
    let align = [ "right", "right", "center", "left", "center", "left", "center", "right", "right" ], baseline = [ "middle", "middle", "top", "top", "middle", "middle", "bottom", "bottom", "middle" ];
    "right" === orient && (align = [ "left", "right", "right", "right", "left", "left", "left", "left", "right" ], 
    baseline = [ "middle", "bottom", "middle", "top", "top", "top", "middle", "bottom", "bottom" ]);
    const step = (angle = clampAngle(angle)) / (.5 * Math.PI);
    let index;
    return index = step === Math.floor(step) ? 2 * Math.floor(step) : 2 * Math.floor(step) + 1, 
    {
        textAlign: align[index],
        textBaseline: baseline[index]
    };
}

exports.autoRotate = autoRotate, exports.rotateYAxis = rotateYAxis, exports.rotateXAxis = rotateXAxis, 
exports.getXAxisLabelAlign = getXAxisLabelAlign, exports.getYAxisLabelAlign = getYAxisLabelAlign;
//# sourceMappingURL=auto-rotate.js.map
