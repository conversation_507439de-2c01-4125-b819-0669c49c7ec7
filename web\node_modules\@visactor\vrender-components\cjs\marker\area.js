"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.MarkArea = void 0;

const vrender_core_1 = require("@visactor/vrender-core"), vutils_1 = require("@visactor/vutils"), tag_1 = require("../tag"), base_1 = require("./base"), config_1 = require("./config"), limit_shape_1 = require("../util/limit-shape"), register_1 = require("./register");

(0, register_1.loadMarkAreaComponent)();

class MarkArea extends base_1.Marker {
    getArea() {
        return this._area;
    }
    getLabel() {
        return this._label;
    }
    constructor(attributes, options) {
        super((null == options ? void 0 : options.skipDefault) ? attributes : (0, vutils_1.merge)({}, MarkArea.defaultAttributes, attributes)), 
        this.name = "markArea";
    }
    _getPositionByDirection(area, direction) {
        const {x1: x1, x2: x2, y1: y1, y2: y2} = this._area.AABBBounds;
        return direction.includes("left") || direction.includes("Left") ? {
            x: x1,
            y: (y1 + y2) / 2
        } : direction.includes("right") || direction.includes("Right") ? {
            x: x2,
            y: (y1 + y2) / 2
        } : direction.includes("top") || direction.includes("Top") ? {
            x: (x1 + x2) / 2,
            y: y1
        } : direction.includes("bottom") || direction.includes("Bottom") ? {
            x: (x1 + x2) / 2,
            y: y2
        } : {
            x: (x1 + x2) / 2,
            y: (y1 + y2) / 2
        };
    }
    setLabelPos() {
        var _a;
        if (this._label && this._area) {
            const {label: label = {}} = this.attribute, labelPosition = null !== (_a = label.position) && void 0 !== _a ? _a : "middle", labelPoint = this._getPositionByDirection(this._area, labelPosition);
            if (this._label.setAttributes(Object.assign(Object.assign({}, labelPoint), {
                textStyle: Object.assign(Object.assign({}, config_1.DEFAULT_MARK_AREA_TEXT_STYLE_MAP[labelPosition]), label.textStyle)
            })), this.attribute.limitRect && label.confine) {
                const {x: x, y: y, width: width, height: height} = this.attribute.limitRect;
                (0, limit_shape_1.limitShapeInBounds)(this._label, {
                    x1: x,
                    y1: y,
                    x2: x + width,
                    y2: y + height
                });
            }
        }
    }
    initMarker(container) {
        const {points: points, label: label, areaStyle: areaStyle} = this.attribute, area = vrender_core_1.graphicCreator.polygon(Object.assign({
            points: points
        }, areaStyle));
        area.name = "mark-area-area", this._area = area, container.add(area);
        const markLabel = new tag_1.Tag(Object.assign({}, label));
        markLabel.name = "mark-area-label", this._label = markLabel, container.add(markLabel), 
        this.setLabelPos();
    }
    updateMarker() {
        const {points: points, label: label, areaStyle: areaStyle} = this.attribute;
        this._area && this._area.setAttributes(Object.assign({
            points: points
        }, areaStyle)), this._area && this._label.setAttributes(Object.assign({
            dx: 0,
            dy: 0
        }, label)), this.setLabelPos();
    }
    isValidPoints() {
        const {points: points} = this.attribute;
        if (!points || points.length < 3) return !1;
        let validFlag = !0;
        return points.forEach((point => {
            (0, vutils_1.isValidNumber)(point.x) && (0, vutils_1.isValidNumber)(point.y) || (validFlag = !1);
        })), validFlag;
    }
}

exports.MarkArea = MarkArea, MarkArea.defaultAttributes = config_1.DEFAULT_MARK_AREA_THEME;
//# sourceMappingURL=area.js.map
