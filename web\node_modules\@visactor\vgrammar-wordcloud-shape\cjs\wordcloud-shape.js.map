{"version": 3, "sources": ["../src/wordcloud-shape.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,6CAA4G;AAC5G,2DAAgD;AAWhD,yDAAiD;AACjD,iDAAkE;AAClE,6CAAwE;AACxE,8EAAyC;AACzC,iCAAwG;AAGxG,MAAM,MAAM,GAAG;IACb,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,UAAU,EAAE,YAAY;IACxB,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,YAAY;IACxB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,aAAa,EAAE,eAAe;IAC9B,KAAK,EAAE,OAAO;CACf,CAAC;AAEK,MAAM,SAAS,GAAG,CACvB,OA0EC,EACD,YAAmB,EACnB,UAAgB,EAChB,IAAY,EACZ,EAAE;;IAEF,IACE,CAAC,OAAO,CAAC,IAAI;QACb,IAAA,cAAK,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,IAAA,cAAK,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EACpB;QACA,MAAM,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAEhE,OAAO,EAAE,CAAC;KACX;IAED,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAExE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAClB,IAAA,qBAAK,EAAC,yCAAyC,CAAC,CAAC;KAClD;IACD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACjB,IAAA,qBAAK,EAAC,wCAAwC,CAAC,CAAC;KACjD;IAED,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,IAAI,CAAC,IAAI,CAAC,iCAA0B,CAAC,6BAA6B,CAAC,CAAC;IAGlF,MAAM,IAAI,GAAG,YAAY,CAAC;IAC1B,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,MAAM,CAAC;IAGhC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAO,EAAE,CAAC;KACX;IAGD,MAAM,iBAAiB,GAA0B;QAC/C,QAAQ,EAAE,OAAO,CAAC,KAAK;QACvB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG;QAC3B,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,KAAK;QACrD,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,MAAM,EAAE,KAAK;QACb,eAAe,EAAE,SAAS;KAC3B,CAAC;IAGF,MAAM,UAAU,GAAG,sBAAO,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7F,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5C,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC7B,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;IAChC,iBAAiB,CAAC,UAAU,GAAG,UAAU,CAAC;IAC1C,iBAAiB,CAAC,OAAO,GAAG,OAAO,CAAC;IAGpC,MAAM,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAClD,iBAAiB,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAGxD,IAAI,iBAAiB,CAAC,MAAM,EAAE;QAC5B,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC;KACjD;SAAM;QACL,iBAAiB,CAAC,eAAe,GAAG,IAAA,iBAAU,GAAE,CAAC;KAClD;IAED,MAAM,UAAU,GAAG,MAAM,IAAA,iCAAkB,EAAC,iBAAiB,CAAC,CAAC;IAE/D,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,EAAE,CAAC;KACX;IAGD,MAAM,kBAAkB,GAA2B,IAAA,2BAAY,EAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;IAG/F,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC;IACjD,MAAM,WAAW,GAAoB;QACnC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAC5B,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;QACpC,aAAa,EAAE,OAAO,CAAC,aAAa;QAGpC,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9E,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAEjD,SAAS,EACP,OAAO,CAAC,SAAS;YACjB,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAC9G,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC;QAGzC,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,YAAY,CAAC;QACxD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;QACrC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;QACvC,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC;QAClD,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC;QAIpD,cAAc,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;KAC3E,CAAC;IAEF,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;IAGzD,MAAM,YAAY,GAAqB;QAErC,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG;QAG3B,QAAQ,EAAE,OAAO,CAAC,KAAK;QACvB,MAAM,EAAE,OAAO,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;QACrE,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,CAAC;QAC7C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,KAAK;QACrD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,SAAS;QAC3C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,IAAI,GAAG;QACzD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC;QACnC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,IAAI,EAAE;QACpD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,GAAG;QAEjD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,IAAI,GAAG;QAG3D,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,GAAG;QACzC,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;QAIvC,YAAY,EAAE,OAAO,CAAC,iBAAiB;YACrC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC;QAC7B,YAAY,EAAE,OAAO,CAAC,iBAAiB;YACrC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC;QAC7B,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;QACtD,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;QAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,IAAI,GAAG;QAC3D,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,IAAI;QAGxD,oBAAoB,EAAE,KAAK,CAAC,OAAO,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACtE,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,gBAAgB,IAAI,QAAQ,CAAC;QAChE,oBAAoB,EAAE,KAAK,CAAC,OAAO,CAAC,iBAAiB,IAAI,QAAQ,CAAC;QAClE,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,cAAc,IAAI,GAAG,CAAC;QACvD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;QACvD,0BAA0B,EAAE,OAAO,CAAC,0BAA0B,IAAI,GAAG;QAGrE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC;QAGzD,aAAa,EAAE,KAAK;QAEpB,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,EAAE;QAC9C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;QACrC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,CAAC;KAChD,CAAC;IAEF,MAAM,aAAa,GAAG,IAAA,qBAAc,EAAC,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,gBAAgB,CAAC,CAAC;IAC3F,YAAY,CAAC,aAAa,GAAG,aAAa,CAAC;IAC3C,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAGzD,wBAAwB,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;IAO9E,MAAM,EACJ,OAAO,EACP,aAAa,EACb,YAAY,EACZ,aAAa,EACb,UAAU,EACV,QAAQ,EACR,eAAe,EACf,WAAW,EACX,aAAa,EACb,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,UAAU,EACX,GAAG,WAAW,CAAC;IAEhB,MAAM,KAAK,GAAoB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;;QAC9C,OAAO;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC;YAEpB,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC;YAChC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC;YAChC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;YAC9B,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/E,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,cAAc,CAAC,KAAK,CAAC;YAC9B,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC;YAC1B,KAAK,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO;YACpG,YAAY,EACV,eAAe,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,KAAK,OAAK,MAAA,OAAO,CAAC,iBAAiB,0CAAE,KAAK,CAAA,IAAI,CAAC,aAAa,CAAC;gBACnG,CAAC,CAAC,CAAC,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;oBACnC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;oBAChE,OAAO;gBACT,CAAC,CAAC,SAAS;YACf,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,KAAK;SAGjB,CAAC;IACJ,CAAC,CAAC,CAAC;IAGH,MAAM,gBAAgB,GAAG,IAAA,mBAAU,EAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC;IACxE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IAG1C,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,IAAA,4BAAK,EAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;IAGrG,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACrC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;QAChC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;QAC5B,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;QAC9B,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;QAChC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAA,uBAAc,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QACtB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACrB;IAED,MAAM,OAAO,GAAG,MAAA,MAAC,OAAO,CAAC,IAAoB,0CAAE,KAAK,mCAAI,SAAS,CAAC;IAClE,MAAM,YAAY,GAAG,MAAA,OAAO,CAAC,YAAY,mCAAI,qBAAqB,CAAC;IACnE,MAAM,gBAAgB,GAAU,EAAE,CAAC;IACnC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;;QACnC,MAAM,CAAC,GAAG,EAAE,CAAC;QACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAA,uBAAc,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe;YAC5B,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/G,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,KAAK,OAAK,MAAA,OAAO,CAAC,iBAAiB,0CAAE,KAAK,CAAA,IAAI,CAAC,aAAa;gBAClF,CAAC,CAAC,IAAI,CAAC,YAAY;gBACnB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QACf,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAGvB,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,eAAe,CAAC;QAGvD,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAGH,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,IAAI,CAAC,IAAI,CAAC,iCAA0B,CAAC,4BAA4B,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC;IAGlH,OAAO,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC9C,CAAC,CAAA,CAAC;AAzWW,QAAA,SAAS,aAyWpB;AAKF,MAAM,cAAc,GAAG,CAAC,IAAW,EAAE,WAA4B,EAAE,YAA8B,EAAE,OAAY,EAAE,EAAE;;IACjH,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,WAAW,CAAC;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,YAAY,CAAC;IACvC,IAAI,UAAU,CAAC;IACf,IAAI,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IACtC,IAAI,iBAAiB,CAAC;IACtB,IAAI,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,CAAC;IACrD,IAAI,SAAS,KAAK,SAAS,EAAE;QAE3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,UAAU,GAAG,CAAC,KAAU,EAAE,EAAE;YAC1B,OAAO,IAAI,qBAAY,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnF,CAAC,CAAC;QAEF,IAAI,eAAe,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,KAAK,OAAK,MAAA,OAAO,CAAC,iBAAiB,0CAAE,KAAK,CAAA,IAAI,CAAC,aAAa,CAAC,EAAE;YACzG,MAAM,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,iBAAiB,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,OAAO,IAAI,qBAAY,EAAE,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjG,CAAC,CAAC;SACH;KACF;SAAM;QAEL,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QAED,MAAM,UAAU,GAAG,IAAI,oBAAW,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAErF,UAAU,GAAG,CAAC,CAAM,EAAE,EAAE;YACtB,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC;QAEF,IAAI,eAAe,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,KAAK,OAAK,MAAA,OAAO,CAAC,iBAAiB,0CAAE,KAAK,CAAA,IAAI,CAAC,aAAa,CAAC,EAAE;YAGzG,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjC,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/D;YACD,MAAM,iBAAiB,GAAG,IAAI,oBAAW,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAE1G,iBAAiB,GAAG,CAAC,CAAM,EAAE,EAAE;gBAC7B,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC;SACH;KACF;IACD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAChE,CAAC,CAAC;AAKF,MAAM,iBAAiB,GAAG,CAAC,IAAW,EAAE,WAA4B,EAAE,kBAA0C,EAAE,EAAE;IAClH,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC;IAC3C,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;IAc7C,IAAI,aAAa,CAAC;IAClB,IAAI,CAAC,WAAW,EAAE;QAOhB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9B,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;SACpB,CAAC,CAAC,CAAC;QASJ,MAAM,CAAC,GAAG,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAI/D,aAAa,GAAG,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC;KAE5B;SAAM,IAAI,WAAW,IAAI,KAAK,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,kBAAS,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjF,aAAa,GAAG,CAAC,KAAU,EAAE,EAAE;YAC7B,OAAO,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;KACH;SAAM,IAAI,WAAW,IAAI,IAAA,mBAAU,EAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE;QAS3D,MAAM,CAAC,GAAG,GAAG,CAAC;QACd,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC/B,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC;YACpB,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;YAEzB,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;SACnE,CAAC,CAAC,CAAC;QAUJ,MAAM,CAAC,GAAG,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAE9D,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzB,MAAM,SAAS,GAAG,IAAI,kBAAS,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjF,aAAa,GAAG,CAAC,KAAU,EAAE,EAAE;YAC7B,OAAO,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;KAEH;IAGD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,KAAY,EAAE,kBAA0C,EAAE,MAAe,EAAE,EAAE;IASvG,MAAM,CAAC,GAAG,GAAG,CAAC;IACd,MAAM,CAAC,GAAG,GAAG,CAAC;IACd,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC;IAC/C,MAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC;IACvC,MAAM,OAAO,GAAG,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC;IAUxD,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;IAEtE,MAAM,QAAQ,GAEZ,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QAGzB,MAAM,UAAU,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,UAAU,GAAG,wBAAwB;YAC1C,CAAC,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,GAAG,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,IAAI,QAAQ,KAAK,CAAC,EAAE;QAElB,OAAO,EAAE,CAAC;KACX;IAED,IAAI,CAAC,GAAG,EAAE,CAAC;IACX,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QAExB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACrD;SAAM;QACL,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACnC,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACtC,MAAM,WAAW,GAAG,UAAU,GAAG,SAAS,CAAC;YAC3C,IAAI,WAAW,GAAG,GAAG,EAAE;gBACrB,SAAS;aACV;YAED,MAAM,cAAc,GAAG,WAAW,GAAG,CAAC,QAAQ,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC;YAE3D,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACd;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;SACvB;aAAM;YAEL,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAKF,SAAS,wBAAwB,CAC/B,IAAW,EACX,WAA4B,EAC5B,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;IAChC,IAAI,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,GAAG,YAAY,CAAC;IACpE,MAAM,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC;IAUtC,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAGxF,IAAI,CAAC,sBAAsB,IAAI,CAAC,oBAAoB,EAAE;QAKpD,MAAM,CAAC,GAAG,YAAY,GAAG,GAAG,CAAC;QAE7B,MAAM,aAAa,GACjB,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACxB,MAAM,MAAM,GAAG,IAAA,oBAAa,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5C,IAAI,MAAM,GAAG,wBAAwB,EAAE;gBACrC,OAAO,GAAG,CAAC;aACZ;YACD,OAAO,GAAG,GAAG,MAAM,CAAC;QACtB,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,IAAI,QAAQ,CAAC;QACb,IAAI,aAAa,KAAK,CAAC,EAAE;YAEvB,QAAQ,GAAG,CAAC,CAAC;SACd;aAAM;YACL,MAAM,IAAI,GAAG,kBAAkB,CAAC,SAAS,GAAG,GAAG,CAAC;YAChD,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC;SAClD;QAED,sBAAsB,GAAG,CAAC,CAAC,QAAQ,CAAC;QACpC,oBAAoB,GAAG,QAAQ,GAAG,YAAY,CAAC,0BAA0B,CAAC;QAE1E,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,sBAAsB;YACtB,oBAAoB;SACrB,CAAC,CAAC;KAKJ;AACH,CAAC;AAED,MAAM,MAAM,GAAG,CAAC,KAAU,EAAE,IAAW,EAAE,EAAE;IACzC,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;IACpB,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;IACpB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,IAAI,CAAM,CAAC;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAE1B,CAAC,GAAG,IAAA,iBAAQ,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,GAAG,GAAG,CAAC,CAAC;SACT;QACD,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,GAAG,GAAG,CAAC,CAAC;SACT;KACF;IAGD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE;QACpC,GAAG,IAAI,KAAK,CAAC;KACd;IAED,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,CAAC,CAAC;AAKF,MAAM,KAAK,GAAG,CAAI,MAAyC,EAAE,EAAE;IAC7D,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI,CAAC;KACb;IACD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC;KACrB;SAAM,IAAI,IAAA,mBAAU,EAAC,MAAM,CAAC,EAAE;QAC7B,OAAO,MAA2B,CAAC;KACpC;IACD,OAAO,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAE,MAAsB,CAAC,KAAK,CAAC,CAAC;AAC9D,CAAC,CAAC", "file": "wordcloud-shape.js", "sourcesContent": ["import { Logger, degreeToRadian, isFunction, isNil, isValid, maxInArray, toNumber } from '@visactor/vutils';\nimport { error } from '@visactor/vgrammar-util';\nimport type {\n  TagItemAttribute,\n  FieldOption,\n  AsType,\n  SegmentationInputType,\n  SegmentationOutputType,\n  LayoutConfigType,\n  wordsConfigType,\n  CloudWordType\n} from './interface';\nimport { vglobal } from '@visactor/vrender-core';\nimport { loadAndHandleImage, segmentation } from './segmentation';\nimport { LinearScale, OrdinalScale, SqrtScale } from '@visactor/vscale';\nimport cloud from './cloud-shape-layout';\nimport { calTextLength, colorListEqual, fakeRandom, functor, WORDCLOUD_SHAPE_HOOK_EVENT } from './util';\nimport type { IView } from '@visactor/vgrammar-core';\n\nconst OUTPUT = {\n  x: 'x',\n  y: 'y',\n  fontFamily: 'fontFamily',\n  fontSize: 'fontSize',\n  fontStyle: 'fontStyle',\n  fontWeight: 'fontWeight',\n  angle: 'angle',\n  opacity: 'opacity',\n  visible: 'visible',\n  isFillingWord: 'isFillingWord',\n  color: 'color'\n};\n\nexport const transform = async (\n  options: {\n    // data index key\n    dataIndexKey?: string;\n\n    // font value 相关\n    text: FieldOption | TagItemAttribute<string> | string;\n\n    // font style 相关\n    size?: [number, number];\n    fontFamily?: FieldOption | TagItemAttribute<string> | string;\n    fontStyle?: FieldOption | TagItemAttribute<string> | string;\n    fontOpacity?: FieldOption | TagItemAttribute<number> | number;\n    fontWeight?: FieldOption | TagItemAttribute<string> | string;\n    fontSize?: FieldOption | TagItemAttribute<number> | number;\n    fontSizeRange?: [number, number];\n    padding?: FieldOption | TagItemAttribute<number> | number;\n\n    // font color 相关\n    colorMode?: 'linear' | 'ordinal';\n    colorField?: FieldOption;\n    colorHexField?: FieldOption;\n    colorList?: string[];\n\n    // font rotate 相关\n    rotate?: FieldOption | TagItemAttribute<number> | number;\n    rotateList?: number[];\n\n    // layout 相关\n    shape: string;\n    random?: boolean;\n    textLayoutTimes?: number;\n    layoutMode?: 'default' | 'ensureMapping' | 'ensureMappingEnlarge';\n    ratio?: number;\n    removeWhiteBorder?: boolean;\n    fontSizeShrinkFactor?: number;\n    stepFactor?: number;\n    importantWordCount?: number;\n    globalShinkLimit?: number;\n    fontSizeEnlargeFactor?: number;\n\n    // fill 相关\n    fillingRatio?: number;\n    fillingTimes?: number;\n    fillingXRatioStep?: number;\n    fillingYRatioStep?: number;\n    fillingXStep?: number;\n    fillingYStep?: number;\n    fillingInitialFontSize?: number;\n    fillingDeltaFontSize?: number;\n    fillingInitialOpacity?: number;\n    fillingDeltaOpacity?: number;\n\n    // fill font style 相关\n    fillingFontFamily?: FieldOption | TagItemAttribute<string> | string;\n    fillingFontStyle?: FieldOption | TagItemAttribute<string> | string;\n    fillingFontWeight?: FieldOption | TagItemAttribute<string> | string;\n    fillingPadding?: FieldOption | TagItemAttribute<number> | number;\n    fillingDeltaFontSizeFactor?: number;\n\n    // fill color 相关\n    fillingColorList?: string[];\n    fillingColorField?: FieldOption;\n\n    // fill rotate 相关\n    fillingRotateList?: number[];\n\n    as?: AsType;\n\n    // 核心词最小初始布局字号\n    minInitFontSize?: number;\n    // 核心词最小布局字号\n    minFontSize?: number;\n    // 填充词词最小布局字号\n    minFillFoontSize?: number;\n  },\n  upstreamData: any[],\n  parameters?: any,\n  view?: IView\n) => {\n  /** options 配置错误提示 */\n  if (\n    !options.size ||\n    isNil(options.size[0]) ||\n    isNil(options.size[1]) ||\n    options.size[0] <= 0 ||\n    options.size[1] <= 0\n  ) {\n    const logger = Logger.getInstance();\n    logger.info('Wordcloud size dimensions must be greater than 0');\n    // size非法不报错，不进行布局，ChartSpace层会有用户初始化size为0的情况\n    return [];\n  }\n  /** size 处理, 如果是小数, segmentation 计算会有问题导致place陷入死循环 */\n  options.size = [Math.ceil(options.size[0]), Math.ceil(options.size[1])];\n\n  if (!options.shape) {\n    error('WordcloudShape shape must be specified.');\n  }\n  if (!options.text) {\n    error('WordcloudShape text must be specified.');\n  }\n\n  view?.emit && view.emit(WORDCLOUD_SHAPE_HOOK_EVENT.BEFORE_WORDCLOUD_SHAPE_LAYOUT);\n\n  /** 输入输出数据相关 */\n  const data = upstreamData;\n  const as = options.as || OUTPUT;\n\n  // 第一次数据流到这里data为空，如果不做判断，走到布局算法会报错\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  /** step1: 根据shapeUrl, 计算segmentation */\n  const segmentationInput: SegmentationInputType = {\n    shapeUrl: options.shape,\n    size: options.size,\n    ratio: options.ratio || 0.8,\n    tempCanvas: undefined,\n    tempCtx: undefined,\n    removeWhiteBorder: options.removeWhiteBorder || false,\n    boardSize: [0, 0],\n    random: false,\n    randomGenerator: undefined\n  };\n\n  // 全局共用的临时画板，此处需要对小程序的 canvas 进行兼容\n  const tempCanvas = vglobal.createCanvas({ width: options.size[0], height: options.size[1] });\n  const tempCtx = tempCanvas.getContext('2d');\n  tempCtx.textAlign = 'center';\n  tempCtx.textBaseline = 'middle';\n  segmentationInput.tempCanvas = tempCanvas;\n  segmentationInput.tempCtx = tempCtx;\n\n  // board 的宽必须为 32 的倍数\n  const boardW = ((options.size[0] + 31) >> 5) << 5;\n  segmentationInput.boardSize = [boardW, options.size[1]];\n\n  // 用于随机的随机数生成器\n  if (segmentationInput.random) {\n    segmentationInput.randomGenerator = Math.random;\n  } else {\n    segmentationInput.randomGenerator = fakeRandom();\n  }\n\n  const shapeImage = await loadAndHandleImage(segmentationInput);\n\n  if (!shapeImage) {\n    return [];\n  }\n\n  // 对用户输入的图形进行预处理\n  const segmentationOutput: SegmentationOutputType = segmentation(shapeImage, segmentationInput);\n\n  /** step2: 收集 wordsConfig, 并计算fontSizeScale */\n  const colorMode = options.colorMode || 'ordinal';\n  const wordsConfig: wordsConfigType = {\n    getText: field(options.text),\n    getFontSize: field(options.fontSize),\n    fontSizeRange: options.fontSizeRange,\n\n    // color 相关\n    colorMode: colorMode,\n    getColor: options.colorField ? field(options.colorField) : field(options.text),\n    getFillingColor: field(options.fillingColorField),\n    // 根据不同的 colorMode 赋值不同的默认值\n    colorList:\n      options.colorList ||\n      (colorMode === 'ordinal' ? ['#2E62F1'] : ['#537EF5', '#2E62F1', '#2358D8', '#184FBF', '#0C45A6', '#013B8E']),\n    getColorHex: field(options.colorHexField),\n\n    // 字体相关\n    getFontFamily: field(options.fontFamily || 'sans-serif'),\n    rotateList: options.rotateList || [0],\n    getPadding: field(options.padding || 1),\n    getFontStyle: field(options.fontStyle || 'normal'),\n    getFontWeight: field(options.fontWeight || 'normal'),\n    // fontField: options.fontFamily,\n    // fontWeightField: options.fontWeight,\n    // fontStyleField: options.fontStyle,\n    getFontOpacity: options.fontOpacity ? field(options.fontOpacity) : () => 1\n  };\n\n  initFontSizeScale(data, wordsConfig, segmentationOutput);\n\n  /** step3: 收集 layoutConfig, 初始化colorScale */\n  const layoutConfig: LayoutConfigType = {\n    // font style 相关\n    size: options.size,\n    ratio: options.ratio || 0.8,\n\n    // layout 相关\n    shapeUrl: options.shape,\n    random: typeof options.random === 'undefined' ? true : options.random,\n    textLayoutTimes: options.textLayoutTimes || 3,\n    removeWhiteBorder: options.removeWhiteBorder || false,\n    layoutMode: options.layoutMode || 'default',\n    fontSizeShrinkFactor: options.fontSizeShrinkFactor || 0.8,\n    stepFactor: options.stepFactor || 1,\n    importantWordCount: options.importantWordCount || 10,\n    globalShinkLimit: options.globalShinkLimit || 0.2,\n    // textLengthLimit: 10,\n    fontSizeEnlargeFactor: options.fontSizeEnlargeFactor || 1.5,\n\n    // fill 相关\n    fillingRatio: options.fillingRatio || 0.7,\n    fillingTimes: options.fillingTimes || 4,\n    // fillingXRatioStep: options.fillingXRatioStep || 0,\n    // fillingYRatioStep: options.fillingYRatioStep || 0,\n    // fillingRatioStep: 步长占长宽的比例，优先级高于fillingStep\n    fillingXStep: options.fillingXRatioStep\n      ? Math.max(Math.floor(options.size[0] * options.fillingXRatioStep), 1)\n      : options.fillingXStep || 4,\n    fillingYStep: options.fillingYRatioStep\n      ? Math.max(Math.floor(options.size[1] * options.fillingYRatioStep), 1)\n      : options.fillingYStep || 4,\n    fillingInitialFontSize: options.fillingInitialFontSize,\n    fillingDeltaFontSize: options.fillingDeltaFontSize,\n    fillingInitialOpacity: options.fillingInitialOpacity || 0.8,\n    fillingDeltaOpacity: options.fillingDeltaOpacity || 0.05,\n\n    // fill font style 相关\n    getFillingFontFamily: field(options.fillingFontFamily || 'sans-serif'),\n    getFillingFontStyle: field(options.fillingFontStyle || 'normal'),\n    getFillingFontWeight: field(options.fillingFontWeight || 'normal'),\n    getFillingPadding: field(options.fillingPadding || 0.4),\n    fillingRotateList: options.fillingRotateList || [0, 90],\n    fillingDeltaFontSizeFactor: options.fillingDeltaFontSizeFactor || 0.2,\n\n    // fill color 相关\n    fillingColorList: options.fillingColorList || ['#537EF5'],\n\n    // 经过计算，补充的内容\n    sameColorList: false,\n\n    minInitFontSize: options.minInitFontSize || 10,\n    minFontSize: options.minFontSize || 4,\n    minFillFoontSize: options.minFillFoontSize || 2\n  };\n  // 核心词与填充词colorList和colorField不一致时，会给填充词设置独立scale\n  const sameColorList = colorListEqual(wordsConfig.colorList, layoutConfig.fillingColorList);\n  layoutConfig.sameColorList = sameColorList;\n  initColorScale(data, wordsConfig, layoutConfig, options);\n\n  /** step4: 初始化填充次fontSize */\n  initFillingWordsFontSize(data, wordsConfig, layoutConfig, segmentationOutput);\n\n  // 过滤掉上游 source 中的填充词，上游数据相关，待去除 @chensiji\n  // data = data.filter((d) => !d.isFillingWord || d.isFillingWord !== true)\n\n  /** step5: 初始化words信息，并执行layout算法 */\n  // 初始化单词信息, 用个代码块避免变量污染外面的变量环境\n  const {\n    getText,\n    getFontFamily,\n    getFontStyle,\n    getFontWeight,\n    getPadding,\n    getColor,\n    getFillingColor,\n    getColorHex,\n    fontSizeScale,\n    colorScale,\n    fillingColorScale,\n    getFontOpacity,\n    rotateList\n  } = wordsConfig;\n\n  const words: CloudWordType[] = data.map(datum => {\n    return {\n      x: 0,\n      y: 0,\n      weight: 0,\n      text: getText(datum),\n      // text: addEllipsis(text(word), config.textLengthLimit),\n      fontFamily: getFontFamily(datum),\n      fontWeight: getFontWeight(datum),\n      fontStyle: getFontStyle(datum),\n      rotate: rotateList[~~(segmentationInput.randomGenerator() * rotateList.length)],\n      fontSize: Math.max(layoutConfig.minInitFontSize, ~~fontSizeScale(datum)),\n      opacity: getFontOpacity(datum),\n      padding: getPadding(datum),\n      color: (getColorHex && getColorHex(datum)) || (colorScale && colorScale(getColor(datum))) || 'black',\n      fillingColor:\n        getFillingColor && (options.colorField?.field !== options.fillingColorField?.field || !sameColorList)\n          ? (getColorHex && getColorHex(datum)) ||\n            (fillingColorScale && fillingColorScale(getFillingColor(datum))) ||\n            'black'\n          : undefined,\n      datum: datum,\n      visible: true,\n      hasPlaced: false\n      // 上游数据相关，待去除 @chensiji\n      // isInAdd: add.indexOf(word) !== -1,\n    };\n  });\n\n  // 计算所有单词的权重 weight，用于后续的布局\n  const wordsMaxFontSize = maxInArray(words.map(word => word.fontSize));\n  words.forEach(word => (word.weight = word.fontSize / wordsMaxFontSize));\n  words.sort((a, b) => b.weight - a.weight);\n\n  // 进行布局\n  const { fillingWords, successedWords, failedWords } = cloud(words, layoutConfig, segmentationOutput);\n\n  /** step5: 将单词信息转换为输出 */\n  let w;\n  let t;\n  const modKeywords = [];\n  for (let i = 0; i < words.length; ++i) {\n    w = words[i];\n    t = w.datum;\n    t[as.x] = w.x;\n    t[as.y] = w.y;\n    t[as.fontFamily] = w.fontFamily;\n    t[as.fontSize] = w.fontSize;\n    t[as.fontStyle] = w.fontStyle;\n    t[as.fontWeight] = w.fontWeight;\n    t[as.angle] = degreeToRadian(w.rotate);\n    t[as.opacity] = w.opacity;\n    t[as.visible] = w.visible;\n    t[as.isFillingWord] = false;\n    t[as.color] = w.color;\n    modKeywords.push(t);\n  }\n\n  const textKey = (options.text as FieldOption)?.field ?? 'textKey'; // 记录用户是用什么 key 存储 text 信息\n  const dataIndexKey = options.dataIndexKey ?? 'defaultDataIndexKey';\n  const fillingWordsData: any[] = [];\n  fillingWords.forEach((word, index) => {\n    const t = {};\n    t[as.x] = word.x;\n    t[as.y] = word.y;\n    t[as.fontFamily] = word.fontFamily;\n    t[as.fontSize] = word.fontSize;\n    t[as.fontStyle] = word.fontStyle;\n    t[as.fontWeight] = word.fontWeight;\n    t[as.angle] = degreeToRadian(word.rotate);\n    t[as.opacity] = word.opacity;\n    t[as.visible] = word.visible;\n    t[as.isFillingWord] = true;\n    t[as.color] = !getFillingColor\n      ? layoutConfig.fillingColorList[~~(segmentationInput.randomGenerator() * layoutConfig.fillingColorList.length)]\n      : options.colorField?.field !== options.fillingColorField?.field || !sameColorList\n      ? word.fillingColor\n      : word.color;\n    t[textKey] = word.text;\n\n    // 保证绘制时，mark的唯一性\n    t[dataIndexKey] = `${word.text}_${index}_fillingWords`;\n\n    // updateid(t)\n    fillingWordsData.push(t);\n  });\n\n  // 抛出事件\n  view?.emit && view.emit(WORDCLOUD_SHAPE_HOOK_EVENT.AFTER_WORDCLOUD_SHAPE_LAYOUT, { successedWords, failedWords });\n\n  // 最后将核心词和填充词合并返回\n  return modKeywords.concat(fillingWordsData);\n};\n\n/**\n * 根据用户输入的参数初始化 colorScale\n */\nconst initColorScale = (data: any[], wordsConfig: wordsConfigType, layoutConfig: LayoutConfigType, options: any) => {\n  const { colorMode, getColor, getFillingColor } = wordsConfig;\n  const { sameColorList } = layoutConfig;\n  let colorScale;\n  let colorList = wordsConfig.colorList;\n  let fillingColorScale;\n  let fillingColorList = layoutConfig.fillingColorList;\n  if (colorMode === 'ordinal') {\n    // 序数着色模式下\n    const uniqueColorField = data.map(word => getColor(word));\n    colorScale = (datum: any) => {\n      return new OrdinalScale().domain(uniqueColorField).range(colorList).scale(datum);\n    };\n\n    if (getFillingColor && (options.colorField?.field !== options.fillingColorField?.field || !sameColorList)) {\n      const uniquefillingColorField = data.map(datum => getFillingColor(datum));\n      fillingColorScale = (datum: any) => {\n        return new OrdinalScale().domain(uniquefillingColorField).range(fillingColorList).scale(datum);\n      };\n    }\n  } else {\n    // 如果用户只输入了一个 color，无法构成 colorRange，则进行兜底\n    if (colorList.length === 1) {\n      colorList = [colorList[0], colorList[0]];\n    }\n    // 线性着色模式下\n    const valueScale = new LinearScale().domain(extent(getColor, data)).range(colorList);\n\n    colorScale = (i: any) => {\n      return valueScale.scale(i);\n    };\n\n    if (getFillingColor && (options.colorField?.field !== options.fillingColorField?.field || !sameColorList)) {\n      // 线性着色模式下\n      // 如果用户只输入了一个 color，无法构成 colorRange，则进行兜底\n      if (fillingColorList.length === 1) {\n        fillingColorList = [fillingColorList[0], fillingColorList[0]];\n      }\n      const fillingValueScale = new LinearScale().domain(extent(getFillingColor, data)).range(fillingColorList);\n\n      fillingColorScale = (i: any) => {\n        return fillingValueScale.scale(i);\n      };\n    }\n  }\n  Object.assign(wordsConfig, { colorScale, fillingColorScale });\n};\n\n/**\n * 根据用户输入参数初始化 fontSizeScale\n */\nconst initFontSizeScale = (data: any[], wordsConfig: wordsConfigType, segmentationOutput: SegmentationOutputType) => {\n  let { fontSizeRange: range } = wordsConfig;\n  const { getFontSize, getText } = wordsConfig;\n  // const { shapeArea, ratio } = segmentationOutput\n\n  /*\n   * 为避免考虑超长词将字号范围计算的非常小，并且超长词同时无法正确布局的情况\n   * 需要在计算字号范围时排除超长词，超长词确定标准：\n   * textLength * 12 > sqrt(shapeArea)\n   * （字号为12px时，该词长度大于预期长宽的两倍，经验参数）\n   * 超长词不参与字号的自适应计算，但是任然会参与布局\n   * 如果用户遇到边界情况需要布局超长词，需要手动指定fontSizeRange\n   */\n  // const shapeSizeLimitTextLength = Math.ceil(Math.sqrt(shapeArea) / 12);\n\n  // 生成 fontSize 的 scale\n  let fontSizeScale;\n  if (!getFontSize) {\n    // 如果用户没有提供 fontSize 映射的 field, 自动计算 一个固定的 fontSize\n    /**\n     * 单词 字长*(fontSize)**2 与真实的单词面积的大概比例为 b\n     * 目的为 求 x，从而得到最适合的 fontSizeRange\n     * 更详细的算法解析看文档\n     */\n    const words = data.map(word => ({\n      text: getText(word)\n    }));\n    // const wordArea =\n    //   b *\n    //   words.reduce((acc, word) => {\n    //     const textLength = calTextLength(word.text)\n    //     return textLength < shapeSizeLimitTextLength ? acc + textLength : acc\n    //   }, 0)\n    // const x0 = Math.sqrt(ratio * (shapeArea / wordArea))\n\n    const x = getInitialFontSize(words, segmentationOutput, false);\n\n    // fontSize = x\n    // 有了 fontSize 后求解 fontSizeScale\n    fontSizeScale = functor(x);\n    // console.log('自动计算的 fontSize', fontSize)\n  } else if (getFontSize && range) {\n    // fontSize 和 range 都提供了\n    const sizeScale = new SqrtScale().domain(extent(getFontSize, data)).range(range);\n    fontSizeScale = (datum: any) => {\n      return sizeScale.scale(getFontSize(datum));\n    };\n  } else if (getFontSize && isFunction(getFontSize) && !range) {\n    // 提供了 fontSize 的取值的 key，没提供 range，自动计算 range\n    /**\n     * 定义 fontSizeRange 为 [ax, x]\n     * 期望单词占图形面积的比例为 ratio\n     * 单词 字长*(权重映射后的fontSize)**2 与真实的单词面积的大概比例为 b\n     * 目的为 求 x，从而得到最适合的 fontSizeRange\n     * 更详细的算法解析看文档\n     */\n    const a = 0.5;\n    const [min, max] = extent(getFontSize, data);\n    const words = data.map(datum => ({\n      text: getText(datum),\n      value: getFontSize(datum),\n      // weight: (fontSize(word) - min) / (max - min),\n      weight: max === min ? 1 : (getFontSize(datum) - min) / (max - min)\n    }));\n    // const wordArea =\n    //   b *\n    //   words.reduce((acc, word) => {\n    //     const textLength = calTextLength(word.text)\n    //     if (textLength > shapeSizeLimitTextLength) return acc;\n    //     return acc + textLength * (a + (1 - a) * word.weight) ** 2\n    //   }, 0)\n    // const x0 = Math.sqrt(ratio * (shapeArea / wordArea))\n\n    const x = getInitialFontSize(words, segmentationOutput, true);\n\n    range = [~~(a * x), ~~x];\n    // 有了 range 后求解 fontSizeScale\n    const sizeScale = new SqrtScale().domain(extent(getFontSize, data)).range(range);\n    fontSizeScale = (datum: any) => {\n      return sizeScale.scale(getFontSize(datum)); // 最小核心词初始字号10px\n    };\n    // console.log('自动计算的 range', range)\n  }\n\n  // 将相关配置更新到 wordsConfig 上\n  Object.assign(wordsConfig, { getFontSize, fontSizeRange: range, fontSizeScale });\n};\n\nconst getInitialFontSize = (words: any[], segmentationOutput: SegmentationOutputType, weight: boolean) => {\n  /**\n   * 定义 fontSizeRange 为 [ax, x]\n   * 期望单词占图形面积的比例为 ratio\n   * 单词 字长*(权重映射后的fontSize)**2 与真实的单词面积的大概比例为 b\n   * 目的为 求 x，从而得到最适合的 fontSizeRange\n   * 更详细的算法解析看文档\n   */\n\n  const a = 0.5;\n  const b = 1.7;\n  const shapeArea = segmentationOutput.shapeArea;\n  const ratio = segmentationOutput.ratio;\n  const regions = segmentationOutput.segmentation.regions;\n\n  /*\n   * 为避免考虑超长词将字号范围计算的非常小，并且超长词同时无法正确布局的情况\n   * 需要在计算字号范围时排除超长词，超长词确定标准：\n   * textLength * 12 > sqrt(shapeArea)\n   * （字号为12px时，该词长度大于预期长宽的两倍，经验参数）\n   * 超长词不参与字号的自适应计算，但是任然会参与布局\n   * 如果用户遇到边界情况需要布局超长词，需要手动指定fontSizeRange\n   */\n  const shapeSizeLimitTextLength = Math.ceil(Math.sqrt(shapeArea) / 12);\n\n  const wordArea =\n    // b *\n    words.reduce((acc, word) => {\n      // 旧版 VGrammar 逻辑\n      // const textLength = calTextLength(word.text, segmentationOutput.textLengthLimit)\n      const textLength = calTextLength(word.text);\n      return textLength < shapeSizeLimitTextLength\n        ? acc + textLength * (weight ? (a + (1 - a) * word.weight) ** 2 : 1)\n        : acc;\n    }, 0);\n  if (wordArea === 0) {\n    // 只有一个超长词，以12px字号开始初始布局\n    return 12;\n  }\n\n  let x = 20;\n  if (regions.length === 1) {\n    // 单一区域\n    x = Math.sqrt(ratio * (shapeArea / (wordArea * b)));\n  } else {\n    const xArr = [];\n    for (let i = 0; i < regions.length; i++) {\n      const regionArea = regions[i].area;\n      const regionAspect = regions[i].ratio;\n      const regionRatio = regionArea / shapeArea;\n      if (regionRatio < 0.1) {\n        continue;\n      }\n      // 考虑区域长宽比对文字面积的影响(2.7 - regionAspect)，经验参数\n      const regionWordArea = regionRatio * (wordArea * (regionAspect < 1 ? 2.7 - regionAspect : b));\n      const x = Math.sqrt(ratio * (regionArea / regionWordArea));\n\n      xArr.push(x);\n    }\n\n    if (xArr.length) {\n      x = Math.min(...xArr);\n    } else {\n      // 特殊情况当做单一区域处理\n      x = Math.sqrt(ratio * (shapeArea / (wordArea * b)));\n    }\n  }\n  return x;\n};\n\n/**\n * 自动计算 fillingWords 相关的 fontSize\n */\nfunction initFillingWordsFontSize(\n  data: any[],\n  wordsConfig: wordsConfigType,\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const { getText } = wordsConfig;\n  let { fillingInitialFontSize, fillingDeltaFontSize } = layoutConfig;\n  const { fillingRatio } = layoutConfig;\n\n  /*\n   * 为避免考虑超长词将字号范围计算的非常小，并且超长词同时无法正确布局的情况\n   * 需要在计算字号范围时排除超长词，超长词确定标准：\n   * textLength * 4 > sqrt(shapeArea)\n   * （字号为4px时，该词长度大于预期长宽的两倍，经验参数）\n   * 超长词不参与字号的自适应计算，但是任然会参与布局\n   * 如果用户遇到边界情况需要布局超长词，需要手动指定fontSizeRange\n   */\n  const shapeSizeLimitTextLength = Math.ceil(Math.sqrt(segmentationOutput.shapeArea) / 4);\n\n  // 两个值中有一个每天写则自动计算\n  if (!fillingInitialFontSize || !fillingDeltaFontSize) {\n    /**\n     * 自动计算的依据是 填充面积应该与 单词平均长度 * fontSize**2 成一个固定比例 a\n     */\n\n    const a = fillingRatio / 100;\n\n    const averageLength =\n      data.reduce((acc, word) => {\n        const length = calTextLength(getText(word));\n        if (length > shapeSizeLimitTextLength) {\n          return acc;\n        }\n        return acc + length;\n      }, 0) / data.length;\n    let fontSize;\n    if (averageLength === 0) {\n      // 只有一个超长词，以8px字号开始初始布局\n      fontSize = 8;\n    } else {\n      const area = segmentationOutput.shapeArea * 0.2;\n      fontSize = Math.sqrt(a * (area / averageLength));\n    }\n\n    fillingInitialFontSize = ~~fontSize;\n    fillingDeltaFontSize = fontSize * layoutConfig.fillingDeltaFontSizeFactor;\n\n    Object.assign(layoutConfig, {\n      fillingInitialFontSize,\n      fillingDeltaFontSize\n    });\n    // console.log('自动计算的 filling', [\n    //   fillingInitialFontSize,\n    //   fillingDeltaFontSize,\n    // ])\n  }\n}\n\nconst extent = (field: any, data: any[]) => {\n  let min = +Infinity;\n  let max = -Infinity;\n  const n = data.length;\n  let v: any;\n\n  for (let i = 0; i < n; ++i) {\n    // 字符串类型转换\n    v = toNumber(field(data[i]));\n    if (v < min) {\n      min = v;\n    }\n    if (v > max) {\n      max = v;\n    }\n  }\n\n  // 如果单条数据，匹配最大字号\n  if (data.length === 1 && min === max) {\n    min -= 10000;\n  }\n\n  return [min, max];\n};\n\n/**\n * 取数逻辑\n */\nconst field = <T>(option: FieldOption | TagItemAttribute<T>) => {\n  if (!option) {\n    return null;\n  }\n  if (typeof option === 'string' || typeof option === 'number') {\n    return () => option;\n  } else if (isFunction(option)) {\n    return option as (datum: any) => T;\n  }\n  return (datum: any) => datum[(option as FieldOption).field];\n};\n"]}