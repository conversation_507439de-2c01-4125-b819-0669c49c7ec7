"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.Marker = void 0;

const vrender_core_1 = require("@visactor/vrender-core"), base_1 = require("../core/base");

class Marker extends base_1.AbstractComponent {
    constructor() {
        super(...arguments), this.name = "marker";
    }
    setAttribute(key, value, forceUpdateTag) {
        super.setAttribute(key, value, forceUpdateTag), "visible" === key && this.render();
    }
    _initContainer() {
        var _a, _b;
        const {limitRect: limitRect = {}, clipInRange: clipInRange} = this.attribute;
        let group;
        if (clipInRange) {
            const groupClip = vrender_core_1.graphicCreator.group(Object.assign(Object.assign({}, limitRect), {
                clip: !0,
                pickable: !1
            }));
            group = vrender_core_1.graphicCreator.group({
                x: -(null !== (_a = limitRect.x) && void 0 !== _a ? _a : 0),
                y: -(null !== (_b = limitRect.y) && void 0 !== _b ? _b : 0),
                pickable: !1
            }), groupClip.add(group), this._containerClip = groupClip, this.add(groupClip);
        } else group = vrender_core_1.graphicCreator.group({
            x: 0,
            y: 0,
            pickable: !1
        }), this.add(group);
        group.name = "marker-container", this._container = group;
    }
    _updateContainer() {
        var _a, _b;
        const {limitRect: limitRect = {}, clipInRange: clipInRange} = this.attribute;
        this._containerClip && this._containerClip.setAttributes(Object.assign({}, limitRect)), 
        this._container.setAttributes({
            x: clipInRange ? -(null !== (_a = limitRect.x) && void 0 !== _a ? _a : 0) : 0,
            y: clipInRange ? -(null !== (_b = limitRect.y) && void 0 !== _b ? _b : 0) : 0
        });
    }
    render() {
        var _a;
        this.setAttribute("pickable", !1);
        const markerVisible = null === (_a = this.attribute.visible) || void 0 === _a || _a;
        !1 === this.attribute.interactive && this.setAttribute("childrenPickable", !1), 
        markerVisible && this.isValidPoints() ? this._container ? (this._updateContainer(), 
        this.updateMarker()) : (this._initContainer(), this.initMarker(this._container)) : (this._container = null, 
        this.removeAllChild());
    }
}

exports.Marker = Marker;
//# sourceMappingURL=base.js.map
