# Linux DO 到 Nodeloc OAuth 迁移完成报告

## 迁移概述

已成功将整个项目中的 Linux DO OAuth 功能完全替换为 Nodeloc OAuth 登录。由于两者都基于相同的 Discourse 论坛系统，OAuth 流程保持兼容。

## 已完成的更改

### 后端更改 ✅

1. **控制器文件**
   - 删除：`controller/linuxdo.go`
   - 新增：`controller/nodeloc.go`
   - 更新了所有函数名和结构体

2. **常量定义** (`common/constants.go`)
   - `LinuxDOOAuthEnabled` → `NodelocOAuthEnabled`
   - `LinuxDOClientId` → `NodelocClientId`
   - `LinuxDOClientSecret` → `NodelocClientSecret`

3. **用户模型** (`model/user.go`)
   - 字段：`LinuxDOId` → `NodelocId`
   - 函数：`IsLinuxDOIdAlreadyTaken` → `IsNodelocIdAlreadyTaken`
   - 函数：`FillUserByLinuxDOId` → `FillUserByNodelocId`

4. **配置管理** (`model/option.go`)
   - 更新了所有配置项的键名
   - 更新了配置验证逻辑

5. **API路由** (`router/api-router.go`)
   - `/api/oauth/linuxdo` → `/api/oauth/nodeloc`

6. **状态接口** (`controller/misc.go`)
   - 更新了状态返回的字段名

7. **选项验证** (`controller/option.go`)
   - 更新了OAuth启用验证逻辑

### 前端更改 ✅

1. **API函数** (`web/src/helpers/api.js`)
   - `onLinuxDOOAuthClicked` → `onNodelocOAuthClicked`
   - 更新了OAuth授权URL

2. **图标组件**
   - 删除：`web/src/components/common/logo/LinuxDoIcon.js`
   - 新增：`web/src/components/common/logo/NodelocIcon.js`

3. **登录表单** (`web/src/components/auth/LoginForm.js`)
   - 更新了所有引用和状态变量
   - 更新了按钮文本和图标

4. **注册表单** (`web/src/components/auth/RegisterForm.js`)
   - 更新了所有引用和状态变量
   - 更新了按钮文本和图标

5. **个人设置** (`web/src/components/settings/PersonalSetting.js`)
   - 更新了绑定界面
   - 更新了用户字段引用

6. **系统设置** (`web/src/components/settings/SystemSetting.js`)
   - 更新了OAuth配置界面
   - 更新了回调URL显示

7. **路由配置** (`web/src/App.js`)
   - `/oauth/linuxdo` → `/oauth/nodeloc`

### 国际化文件 ✅

1. **中文** (`i18n/zh-cn.json`)
   - "使用 LinuxDO 继续" → "使用 Nodeloc 继续"

2. **英文** (`web/src/i18n/locales/en.json`)
   - "Continue with LinuxDO" → "Continue with Nodeloc"

### 文档更新 ✅

1. **API文档** (`docs/api/web_api.md`)
   - 更新了OAuth接口说明

2. **迁移文档**
   - 新增：`NODELOC_MIGRATION.md`
   - 新增：`bin/migration_linuxdo_to_nodeloc.sql`

## OAuth 端点变更

### 旧的 Linux DO 端点
```
授权: https://connect.linux.do/oauth2/authorize
令牌: https://connect.linux.do/oauth2/token
用户: https://connect.linux.do/api/user
```

### 新的 Nodeloc 端点
```
授权: https://conn.nodeloc.cc/oauth2/auth
令牌: https://conn.nodeloc.cc/oauth2/token
用户: https://conn.nodeloc.cc/oauth2/userinfo
```

## 数据库迁移

已提供完整的SQL迁移脚本：`bin/migration_linuxdo_to_nodeloc.sql`

### 迁移内容
1. 重命名用户表字段：`linux_do_id` → `nodeloc_id`
2. 更新配置选项键名
3. 可选：更新用户名前缀

## 下一步操作

### 1. 数据库迁移
```bash
# 备份数据库
mysqldump -u username -p database_name > backup.sql

# 执行迁移
mysql -u username -p database_name < bin/migration_linuxdo_to_nodeloc.sql
```

### 2. OAuth 应用配置
1. 在 [https://conn.nodeloc.cc/apps](https://conn.nodeloc.cc/apps) 创建OAuth应用
2. **重要**：设置回调地址为：`https://yourdomain.com/oauth/nodeloc`
   - 注意：与 oauth2 文件夹中的 PHP SDK 回调地址不同
   - 我们的项目使用统一的 `/oauth/nodeloc` 路径
3. 在系统设置中配置Client ID和Secret

### 3. 测试验证
- [ ] Nodeloc OAuth 登录
- [ ] Nodeloc 账户绑定
- [ ] 用户信息正确显示
- [ ] 现有绑定用户正常登录

## 兼容性说明

- ✅ 现有用户数据完全兼容
- ✅ OAuth 流程保持一致
- ✅ 安全机制不变
- ✅ 用户体验无缝切换

## 回滚方案

如需回滚：
1. 恢复数据库备份
2. 使用 git 回滚代码更改
3. 重新配置 Linux DO OAuth 应用

迁移已完成，可以开始使用 Nodeloc OAuth 功能！
