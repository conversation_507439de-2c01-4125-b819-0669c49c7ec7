'use client';

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var _excluded = ["size", "style"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
import { memo } from 'react';
import { TITLE } from "../style";
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var Icon = /*#__PURE__*/memo(function (_ref) {
  var _ref$size = _ref.size,
    size = _ref$size === void 0 ? '1em' : _ref$size,
    style = _ref.style,
    rest = _objectWithoutProperties(_ref, _excluded);
  return /*#__PURE__*/_jsxs("svg", _objectSpread(_objectSpread({
    fill: "currentColor",
    fillRule: "evenodd",
    height: size,
    style: _objectSpread({
      flex: 'none',
      lineHeight: 1
    }, style),
    viewBox: "0 0 24 24",
    width: size,
    xmlns: "http://www.w3.org/2000/svg"
  }, rest), {}, {
    children: [/*#__PURE__*/_jsx("title", {
      children: TITLE
    }), /*#__PURE__*/_jsx("path", {
      d: "M11.88 21.5a4.49 4.49 0 01-2.772-.959 4.516 4.516 0 01-1.71-3.024 4.513 4.513 0 01-.007-1.086 4.46 4.46 0 01-.859.078A4.537 4.537 0 012 11.975c0-2.5 2.036-4.54 4.532-4.54.356 0 .7.041 1.034.12A4.543 4.543 0 0112.07 2.5c2.497 0 4.525 2.04 4.525 4.54 0 .145-.005.286-.02.43a4.596 4.596 0 011.125-.056 4.542 4.542 0 014.18 4.864 4.507 4.507 0 01-1.562 3.103 4.484 4.484 0 01-3.287 1.085 4.54 4.54 0 01-.647-.091c0 .01.007.019.007.028a4.522 4.522 0 01-.922 3.349 4.496 4.496 0 01-3.019 1.713 4.53 4.53 0 01-.57.035zm-2.512-5.993a2.893 2.893 0 00-.366 1.812 2.906 2.906 0 003.244 2.538 2.899 2.899 0 001.943-1.1 2.89 2.89 0 00.59-2.15 2.905 2.905 0 00-.562-1.396 4.516 4.516 0 01-.542-.641.807.807 0 01.19-1.128.805.805 0 011.126.19c.061.085.122.163.19.24a.846.846 0 01.155.14c.028.034.05.067.077.1.474.429 1.08.692 1.731.74 1.605.12 3-1.09 3.118-2.693a2.913 2.913 0 00-2.681-3.124 2.884 2.884 0 00-1.739.423.804.804 0 01-.9.085.82.82 0 01-.324-1.107c.234-.425.359-.905.359-1.396a2.92 2.92 0 00-2.914-2.918A2.919 2.919 0 009.15 7.04c0 .576.168 1.13.485 1.608.016.024.03.053.043.077a4.52 4.52 0 011.379 3.25c0 1.426-.66 2.707-1.689 3.54v-.008zm-2.843-6.45a2.914 2.914 0 00-2.906 2.918c0 1.61 1.3 2.92 2.906 2.92a2.92 2.92 0 000-5.838z"
    })]
  }));
});
export default Icon;