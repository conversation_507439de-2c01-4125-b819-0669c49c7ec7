"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.DEFAULT_HANDLER_ATTR_MAP = exports.DEFAULT_DATA_ZOOM_ATTRIBUTES = exports.DEFAULT_HANDLER_PATH = void 0, 
exports.DEFAULT_HANDLER_PATH = "M -0.0544 0.25 C -0.0742 0.25 -0.0901 0.234 -0.0901 0.2143 L -0.0901 -0.1786 C -0.0901 -0.1983 -0.0742 -0.2143 -0.0544 -0.2143 L -0.0187 -0.2143 L -0.0187 -0.5 L 0.017 -0.5 L 0.017 -0.2143 L 0.0527 -0.2143 C 0.0724 -0.2143 0.0884 -0.1983 0.0884 -0.1786 L 0.0884 0.2143 C 0.0884 0.234 0.0724 0.25 0.0527 0.25 L 0.017 0.25 L 0.017 0.5 L -0.0187 0.5 L -0.0187 0.25 L -0.0544 0.25 Z M -0.0187 -0.1429 L -0.0544 -0.1429 L -0.0544 0.1786 L -0.0187 0.1786 L -0.0187 -0.1429 Z M 0.0527 -0.1429 L 0.017 -0.1429 L 0.017 0.1786 L 0.0527 0.1786 L 0.0527 -0.1429 Z", 
exports.DEFAULT_DATA_ZOOM_ATTRIBUTES = {
    orient: "bottom",
    showDetail: "auto",
    brushSelect: !0,
    zoomLock: !1,
    minSpan: 0,
    maxSpan: 1,
    delayType: "throttle",
    delayTime: 0,
    realTime: !0,
    backgroundStyle: {
        fill: "white",
        stroke: "#D1DBEE",
        lineWidth: 1,
        cornerRadius: 2
    },
    dragMaskStyle: {
        fill: "#B0C8F9",
        fillOpacity: .2
    },
    backgroundChartStyle: {
        area: {
            visible: !0,
            stroke: "#D1DBEE",
            lineWidth: 1,
            fill: "#F6F8FC"
        },
        line: {
            visible: !0,
            stroke: "#D1DBEE",
            lineWidth: 1
        }
    },
    selectedBackgroundStyle: {
        fill: "#B0C8F9",
        fillOpacity: .5
    },
    selectedBackgroundChartStyle: {
        area: {
            visible: !0,
            stroke: "#B0C8F9",
            lineWidth: 1,
            fill: "#fbb934"
        },
        line: {
            visible: !0,
            stroke: "#fbb934",
            lineWidth: 1
        }
    },
    middleHandlerStyle: {
        visible: !0,
        background: {
            size: 8,
            style: {
                fill: "white",
                stroke: "#B0C8F9",
                cornerRadius: 2
            }
        },
        icon: {
            size: 6,
            fill: "white",
            stroke: "#B0C8F9",
            symbolType: "M 0.3 -0.5 C 0.41 -0.5 0.5 -0.41 0.5 -0.3 C 0.5 -0.3 0.5 0.3 0.5 0.3 C 0.5 0.41 0.41 0.5 0.3 0.5 C 0.3 0.5 -0.3 0.5 -0.3 0.5 C -0.41 0.5 -0.5 0.41 -0.5 0.3 C -0.5 0.3 -0.5 -0.3 -0.5 -0.3 C -0.5 -0.41 -0.41 -0.5 -0.3 -0.5 C -0.3 -0.5 0.3 -0.5 0.3 -0.5 Z",
            lineWidth: .5
        }
    },
    startHandlerStyle: {
        visible: !0,
        triggerMinSize: 0,
        symbolType: exports.DEFAULT_HANDLER_PATH,
        fill: "white",
        stroke: "#B0C8F9",
        lineWidth: .5
    },
    endHandlerStyle: {
        visible: !0,
        triggerMinSize: 0,
        symbolType: exports.DEFAULT_HANDLER_PATH,
        fill: "white",
        stroke: "#B0C8F9",
        lineWidth: .5
    },
    startTextStyle: {
        padding: 4,
        textStyle: {
            fontSize: 10,
            fill: "#6F6F6F"
        }
    },
    endTextStyle: {
        padding: 4,
        textStyle: {
            fontSize: 10,
            fill: "#6F6F6F"
        }
    }
}, exports.DEFAULT_HANDLER_ATTR_MAP = {
    horizontal: {
        angle: 0,
        strokeBoundsBuffer: 0,
        boundsPadding: 2,
        pickMode: "imprecise",
        cursor: "ew-resize"
    },
    vertical: {
        angle: Math.PI / 180 * 90,
        cursor: "ns-resize",
        boundsPadding: 2,
        pickMode: "imprecise",
        strokeBoundsBuffer: 0
    }
};