{"version": 3, "sources": ["../src/accessor.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,EAAO,EAAE,MAAiB,EAAE,IAAa,EAAE,EAAE;IACpE,EAAE,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IACzB,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC;IAChB,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,UAAU,YAAY,CAAC,EAAO;IAClC,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,EAAE,CAAC,KAAgB,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,EAAO;IACpC,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC;AACtC,CAAC", "file": "accessor.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-util/src/accessor.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\nimport { isNil } from '@visactor/vutils';\n\nexport const accessor = (fn: any, fields?: string[], name?: string) => {\n  fn.fields = fields || [];\n  fn.fname = name;\n  return fn;\n};\n\nexport function accessorName(fn: any) {\n  return isNil(fn) ? null : (fn.fname as string);\n}\n\nexport function accessorFields(fn: any) {\n  return isNil(fn) ? null : fn.fields;\n}\n"]}