@echo off
chcp 65001 >nul

echo === Nodeloc OAuth 调试工具 ===
echo.

if "%1"=="" (
    echo 用法: %0 ^<你的域名^>
    echo 例如: %0 http://localhost:3000
    echo 或者: %0 https://yourdomain.com
    exit /b 1
)

set DOMAIN=%1
echo 测试域名: %DOMAIN%
echo.

echo 1. 检查OAuth配置...
echo ================================
curl -s "%DOMAIN%/api/debug/oauth/config"
echo.
echo.

echo 2. 生成OAuth调试URL...
echo ================================
curl -s "%DOMAIN%/api/debug/oauth/nodeloc" > temp_response.json
type temp_response.json
echo.
echo.

echo 3. 请手动复制上面JSON中的oauth_url字段的值
echo    然后在浏览器中打开该URL进行测试
echo.

echo 如果URL正常，应该会跳转到Nodeloc授权页面
echo 如果出现错误，请检查以下配置：
echo - Nodeloc应用管理中的Client ID
echo - 回调地址设置
echo - 权限范围设置
echo.

del temp_response.json >nul 2>&1

echo === 调试完成 ===
pause
