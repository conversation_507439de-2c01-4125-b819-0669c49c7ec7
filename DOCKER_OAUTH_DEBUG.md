# Docker环境下的Nodeloc OAuth调试指南

## 快速调试步骤

### 1. 重新构建并启动Docker容器
```bash
# 停止当前容器
docker-compose down

# 重新构建（包含新的调试端点）
docker-compose build

# 启动容器
docker-compose up -d
```

### 2. 使用API调试端点

#### 方法A: 使用curl命令

**检查OAuth配置：**
```bash
curl http://localhost:3000/api/debug/oauth/config
```

**生成OAuth调试URL：**
```bash
curl http://localhost:3000/api/debug/oauth/nodeloc
```

#### 方法B: 使用提供的脚本

**Linux/Mac：**
```bash
chmod +x debug_oauth.sh
./debug_oauth.sh http://localhost:3000
```

**Windows：**
```cmd
debug_oauth.bat http://localhost:3000
```

#### 方法C: 在浏览器中直接访问

打开浏览器访问：
- 配置检查：`http://localhost:3000/api/debug/oauth/config`
- OAuth URL生成：`http://localhost:3000/api/debug/oauth/nodeloc`

### 3. 分析调试输出

#### 配置检查输出示例：
```json
{
  "success": true,
  "data": {
    "nodeloc_oauth_enabled": true,
    "nodeloc_client_id": "your_client_id",
    "nodeloc_client_secret": "已配置 (长度: 32)",
    "redirect_uri": "http://localhost:3000/oauth/nodeloc",
    "current_host": "localhost:3000",
    "current_scheme": "http",
    "oauth_endpoints": {
      "authorization": "https://conn.nodeloc.cc/oauth2/auth",
      "token": "https://conn.nodeloc.cc/oauth2/token",
      "userinfo": "https://conn.nodeloc.cc/oauth2/userinfo"
    },
    "required_scope": "openid profile",
    "checklist": [
      "✓ 检查 nodeloc_oauth_enabled 是否为 true",
      "✓ 检查 nodeloc_client_id 是否已配置",
      "✓ 检查 nodeloc_client_secret 是否已配置",
      "✓ 在Nodeloc应用管理中设置回调地址为: http://localhost:3000/oauth/nodeloc",
      "✓ 确保权限范围包含: openid profile"
    ]
  }
}
```

#### OAuth URL生成输出示例：
```json
{
  "success": true,
  "data": {
    "oauth_url": "https://conn.nodeloc.cc/oauth2/auth?client_id=your_client_id&nonce=test_nonce_67890&redirect_uri=http%3A//localhost%3A3000/oauth/nodeloc&response_type=code&scope=openid+profile&state=test_state_12345",
    "client_id": "your_client_id",
    "redirect_uri": "http://localhost:3000/oauth/nodeloc",
    "state": "test_state_12345",
    "nonce": "test_nonce_67890",
    "parameters": {
      "response_type": "code",
      "client_id": "your_client_id",
      "redirect_uri": "http://localhost:3000/oauth/nodeloc",
      "scope": "openid profile",
      "state": "test_state_12345",
      "nonce": "test_nonce_67890"
    },
    "instructions": [
      "1. 复制上面的 oauth_url",
      "2. 在浏览器中打开该URL",
      "3. 检查是否正确跳转到Nodeloc授权页面",
      "4. 如果出现错误，请检查Nodeloc应用配置中的回调地址是否为: http://localhost:3000/oauth/nodeloc"
    ]
  }
}
```

### 4. 测试OAuth流程

1. **复制生成的oauth_url**
2. **在浏览器中打开该URL**
3. **观察结果：**
   - ✅ **成功**：跳转到Nodeloc授权页面
   - ❌ **失败**：显示错误信息

### 5. 常见问题排查

#### 问题1: "nodeloc_oauth_enabled": false
**解决方案：**
1. 登录管理后台
2. 进入系统设置
3. 启用"允许通过 Nodeloc 账户登录 & 注册"

#### 问题2: "nodeloc_client_id": ""
**解决方案：**
1. 在Nodeloc应用管理中获取Client ID
2. 在系统设置中填入Client ID

#### 问题3: "nodeloc_client_secret": "未配置"
**解决方案：**
1. 在Nodeloc应用管理中获取Client Secret
2. 在系统设置中填入Client Secret

#### 问题4: OAuth URL访问出现invalid_request错误
**解决方案：**
1. 检查Nodeloc应用管理中的回调地址设置
2. 确保回调地址与调试输出中的redirect_uri完全一致
3. 检查权限范围是否包含"openid profile"

### 6. 生产环境配置

如果你的应用部署在生产环境，需要：

1. **更新域名：**
   ```bash
   curl https://yourdomain.com/api/debug/oauth/config
   ```

2. **检查HTTPS：**
   确保生产环境使用HTTPS

3. **更新Nodeloc应用配置：**
   - 回调地址：`https://yourdomain.com/oauth/nodeloc`
   - 确保域名匹配

### 7. 清理调试端点

**重要：** 在生产环境中，建议移除调试端点以确保安全：

```go
// 在 router/api-router.go 中注释或删除这些行：
// apiRouter.GET("/debug/oauth/nodeloc", controller.DebugNodelocOAuth)
// apiRouter.GET("/debug/oauth/config", controller.DebugOAuthConfig)
```

## 下一步

1. 执行上述调试步骤
2. 记录调试输出结果
3. 根据输出结果进行相应配置
4. 测试OAuth流程是否正常

如果仍有问题，请提供调试输出的完整内容。
