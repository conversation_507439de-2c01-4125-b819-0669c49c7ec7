{"version": 3, "sources": ["../src/segmentation.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,iCAAmC;AAEnC,SAAsB,kBAAkB,CAAC,iBAAwC;;QAC/E,MAAM,UAAU,GAAG,CAAC,MAAM,IAAA,gBAAS,EAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAsB,CAAC;QAEtF,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,UAAU,EAAE;YACrD,OAAO,YAAY,CAAC,UAAU,EAAE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;SAC1F;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;CAAA;AARD,gDAQC;AAOD,SAAgB,YAAY,CAAC,UAA6B,EAAE,iBAAwC;IAClG,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,iBAAiB,CAAC;IAC7D,MAAM,WAAW,GAAG,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAG1D,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3B,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACzD,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;IAC/F,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3D,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEpD,IAAI,QAAQ,GAAG,CAAC,CAAC;IAEjB,MAAM,MAAM,GAAG;QACb,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACR,CAAC;IAEF,IAAI,KAAK,GAAG,EAAE,CAAC;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAEhC,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAClE,SAAS;aACV;YAED,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;YAEnC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAGnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC1B,IAAI,GAAG,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,IAAI,GAAG,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAG7C,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;oBACvD,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;oBAGvD,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;wBAC3E,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;wBACvC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;qBACxB;iBACF;aACF;YAGD,QAAQ,EAAE,CAAC;YAEX,KAAK,GAAG,EAAE,CAAC;SACZ;KACF;IAQD,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,MAAM,WAAW,GAAG;QAClB,EAAE,EAAE,QAAQ;QACZ,EAAE,EAAE,CAAC,QAAQ;QACb,EAAE,EAAE,QAAQ;QACZ,EAAE,EAAE,CAAC,QAAQ;QACb,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;KACV,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtC,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,SAAS;aACV;YAGD,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBACzB,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBAC5C,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAE/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;oBACrB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;iBAC/D;gBACD,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC3B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACzB;gBACD,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC3B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACzB;gBACD,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC3B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACzB;gBACD,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC3B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACzB;gBAGD,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE;oBACtB,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;iBACpB;gBACD,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE;oBACtB,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;iBACpB;gBACD,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE;oBACtB,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;iBACpB;gBACD,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE;oBACtB,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;iBACpB;aACF;YAGD,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;SAChB;KACF;IAGD,MAAM,aAAa,GAAG,EAAE,CAAC;IAGzB,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;QAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QAEnC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzF,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzF,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,aAAa,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAEhC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAElD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAC5C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAC5C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAC5C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAC7C,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;KAC/C;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;QACtD,KAAK,EAAE,GAAG,GAAG,CAAC;QACd,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC;QACzB,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;QAChB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;QACpB,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC;QACxB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;QACf,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;KACnB,CAAC,CAAC,CAAC;IAGJ,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;IACxD,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;IAEzD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACzF,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAEzF,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAChE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAChE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAChE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CACjE,CAAC;IACF,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;IAC1D,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAElF,MAAM,YAAY,GAAG;QACnB,OAAO;QACP,MAAM;QACN,WAAW,EAAE,QAAQ,GAAG,CAAC;KAC1B,CAAC;IACF,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE;QACtC,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,UAAU;QACV,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACnB,SAAS;KACV,CAAC,CAAC;IAOH,SAAS,eAAe,CAAC,CAAS,EAAE,CAAS;QAE3C,MAAM,MAAM,GAAG;YACb,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACR,CAAC;QAGF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YAChE,OAAO,IAAI,CAAC;SACb;QAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAG3B,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACvD,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAEvD,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;gBACrC,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAvOD,oCAuOC;AAQD,SAAS,YAAY,CAAC,SAAoB,EAAE,CAAS,EAAE,CAAS;IAC9D,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAC9B,OAAO,CACL,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QAC/C,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;YAChD,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;YACjD,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CACrD,CAAC;AACJ,CAAC;AAKD,SAAS,YAAY,CAAC,KAAU,EAAE,MAA+B,EAAE,GAAoC;IACrG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC3B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC7B,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACjD,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAC3B,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACtE,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IAC9B,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAE5B,OAAO,GAAG,GAAG,MAAM,IAAI,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE;QACtD,EAAE,GAAG,CAAC;KACP;IACD,OAAO,MAAM,GAAG,CAAC,GAAG,GAAG,IAAI,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE;QACjE,EAAE,MAAM,CAAC;KACV;IACD,OAAO,IAAI,GAAG,KAAK,IAAI,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE;QACvE,EAAE,IAAI,CAAC;KACR;IACD,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,IAAI,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE;QAChF,EAAE,KAAK,CAAC;KACT;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC;IACxE,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/B,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACjD,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEhC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,QAAQ,CAAC,SAAoB,EAAE,KAAa,EAAE,CAAS;IAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;QAC9B,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,SAAoB,EAAE,KAAa,EAAE,CAAS,EAAE,GAAW,EAAE,MAAc;IAC9F,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;QACjC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAMD,SAAS,mBAAmB,CAAC,KAAU,EAAE,IAAsB;IAC7D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC5B,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;QAC5B,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;KAC1B;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;IAE7C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IACnC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAEpC,OAAO;QACL,CAAC;QACD,CAAC;QACD,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,SAAS;QACjB,KAAK;KACN,CAAC;AACJ,CAAC;AAQD,SAAgB,aAAa,CAAC,KAAsB,EAAE,kBAA0C;IAC9F,MAAM,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,EAC1B,GAAG,kBAAkB,CAAC;IACvB,IAAI,OAAO,GAAG,CAAC,QAAQ,CAAC;IACxB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,YAAY,GAAG,CAAC,CAAC;IAGrB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,KAAa,EAAE,EAAE;QAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,IAAI,IAAI,GAAG,OAAO,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC;YACf,YAAY,GAAG,KAAK,CAAC;SACtB;QACD,SAAS,IAAI,IAAI,CAAC;IACpB,CAAC,CAAC,CAAC;IAGH,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;QAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,IAAI,GAAG,OAAO,CAAC;QAEpC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;QACnC,QAAQ,IAAI,SAAS,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE;QAC3B,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;KAC3D;IAGD,IAAI,SAAS,GAAG,YAAY,CAAC;IAC7B,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAClE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;QAE1B,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAEtB,GAAG;YACD,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE;gBAC/E,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;gBAC7B,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxB,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC7C,MAAM;aACP;YACD,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YAE7C,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAEhB,IAAI,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBAE9B,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;gBAC9B,CAAC,CAAC,CAAC;gBACH,aAAa,GAAG,CAAC,CAAC;aACnB;SACF,QAAQ,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QAGtE,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;YAChC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;SAC5B;IACH,CAAC,CAAC,CAAC;IAGH,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;QAC9B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAgB,EAAE,CAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;AACL,CAAC;AA/ED,sCA+EC", "file": "segmentation.js", "sourcesContent": ["import type { CloudWordType, SegmentationInputType, SegmentationOutputType } from './interface';\nimport { loadImage } from './util';\n\nexport async function loadAndHandleImage(segmentationInput: SegmentationInputType): Promise<CanvasImageSource> {\n  const shapeImage = (await loadImage(segmentationInput.shapeUrl)) as CanvasImageSource;\n\n  if (segmentationInput.removeWhiteBorder && shapeImage) {\n    return removeBorder(shapeImage, segmentationInput.tempCanvas, segmentationInput.tempCtx);\n  }\n\n  return shapeImage;\n}\n\n/**\n * 求图像连通区域的个数、面积、边界、中心点\n * @param {*} shape 图像 base64\n * @param {*} size 画布大小\n */\nexport function segmentation(shapeImage: CanvasImageSource, segmentationInput: SegmentationInputType) {\n  const { size, tempCanvas, tempCtx: ctx } = segmentationInput;\n  const shapeConfig = scaleAndMiddleShape(shapeImage, size);\n  //   config.shapeConfig = shapeConfig\n\n  tempCanvas.width = size[0];\n  tempCanvas.height = size[1];\n  ctx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);\n  ctx.drawImage(shapeImage, shapeConfig.x, shapeConfig.y, shapeConfig.width, shapeConfig.height);\n  const imageData = ctx.getImageData(0, 0, size[0], size[1]);\n  // 保存分组标签，0 是背景(像素为白色或透明度为 0)，>1 的分组\n  const labels = new Array(size[0] * size[1]).fill(0);\n  // 当前的种子标签\n  let curLabel = 1;\n  // 四连通位置偏移\n  const offset = [\n    [0, 1],\n    [1, 0],\n    [-1, 0],\n    [0, -1]\n  ];\n  // 当前连通域中的单位域队列\n  let queue = [];\n  // 注意此处，i 为行数即 y，j为x，下同\n  for (let i = 0; i < size[1]; i++) {\n    for (let j = 0; j < size[0]; j++) {\n      // 当前单位域已被标记或者属于背景区域, 则跳过\n      if (labels[i * size[0] + j] !== 0 || isEmptyPixel(imageData, i, j)) {\n        continue;\n      }\n\n      labels[i * size[0] + j] = curLabel;\n      // 加入当前域队列\n      queue.push([i, j]);\n\n      // 遍历当前域队列\n      for (let k = 0; k < queue.length; k++) {\n        // 四连通范围内检查未标记的前景单位域\n        for (let m = 0; m < 4; m++) {\n          let row: number = queue[k][0] + offset[m][0];\n          let col: number = queue[k][1] + offset[m][1];\n\n          // 防止坐标溢出图像边界\n          row = row < 0 ? 0 : row >= size[1] ? size[1] - 1 : row;\n          col = col < 0 ? 0 : col >= size[0] ? size[0] - 1 : col;\n\n          // 邻近单位域未标记并且属于前景区域, 标记并加入队列\n          if (labels[row * size[0] + col] === 0 && !isEmptyPixel(imageData, row, col)) {\n            labels[row * size[0] + col] = curLabel;\n            queue.push([row, col]);\n          }\n        }\n      }\n\n      // 一个完整连通域查找完毕，标签更新\n      curLabel++;\n      // 清空队列\n      queue = [];\n    }\n  }\n\n  /**\n   * 使用一次扫描线算法，识别出连通域的边界、面积、最大的边界点以求的最大半径\n   * 边界：二值图像发生突变的地方\n   * 面积：连通域中的像素个数\n   * ratio: 连通区域的大致宽高比\n   */\n  const boundaries = {};\n  const areas = {};\n  const centers = {};\n  const maxPoints = {}; // 存储顺序为 iMin, iMax, jMin, jMax\n  const maxR = {};\n  const ratios = {};\n  // 存储形状的范围\n  const shapeBounds = {\n    x1: Infinity,\n    x2: -Infinity,\n    y1: Infinity,\n    y2: -Infinity,\n    width: 0,\n    height: 0\n  };\n\n  for (let i = 0; i < size[1]; i++) {\n    for (let j = 0; j < size[0]; j++) {\n      const label = labels[i * size[0] + j];\n      if (label === 0) {\n        continue;\n      }\n\n      // 当前像素为边界\n      if (isBoundaryPixel(i, j)) {\n        boundaries[label] = boundaries[label] || [];\n        boundaries[label].push([j, i]);\n\n        if (!maxPoints[label]) {\n          maxPoints[label] = [Infinity, -Infinity, Infinity, -Infinity];\n        }\n        if (i < maxPoints[label][0]) {\n          maxPoints[label][0] = i;\n        }\n        if (i > maxPoints[label][1]) {\n          maxPoints[label][1] = i;\n        }\n        if (j < maxPoints[label][2]) {\n          maxPoints[label][2] = j;\n        }\n        if (j > maxPoints[label][3]) {\n          maxPoints[label][3] = j;\n        }\n\n        // 更新 bounds\n        if (j < shapeBounds.x1) {\n          shapeBounds.x1 = j;\n        }\n        if (j > shapeBounds.x2) {\n          shapeBounds.x2 = j;\n        }\n        if (i < shapeBounds.y1) {\n          shapeBounds.y1 = i;\n        }\n        if (i > shapeBounds.y2) {\n          shapeBounds.y2 = i;\n        }\n      }\n\n      // 计算面积\n      areas[label] = areas[label] || 0;\n      areas[label]++;\n    }\n  }\n\n  // 用于计算整个 shape 的中心点\n  const allBoundaries = [];\n\n  // 计算中心点\n  for (const label in boundaries) {\n    const boundary = boundaries[label];\n    // 计算多边形重心\n    const x = ~~(boundary.reduce((acc: any, cur: any) => acc + cur[0], 0) / boundary.length);\n    const y = ~~(boundary.reduce((acc: any, cur: any) => acc + cur[1], 0) / boundary.length);\n    centers[label] = [x, y];\n    allBoundaries.push(...boundary);\n\n    const [yMin, yMax, xMin, xMax] = maxPoints[label];\n\n    maxR[label] = ~~Math.max(\n      Math.sqrt((x - xMin) ** 2 + (y - yMin) ** 2),\n      Math.sqrt((x - xMax) ** 2 + (y - yMax) ** 2),\n      Math.sqrt((x - xMin) ** 2 + (y - yMax) ** 2),\n      Math.sqrt((x - xMax) ** 2 + (y - yMin) ** 2)\n    );\n\n    ratios[label] = (xMax - xMin) / (yMax - yMin);\n  }\n\n  const regions = Object.keys(centers).map((key: any) => ({\n    label: key - 1,\n    boundary: boundaries[key],\n    area: areas[key],\n    center: centers[key],\n    maxPoint: maxPoints[key],\n    maxR: maxR[key],\n    ratio: ratios[key]\n  }));\n\n  // 计算整个 shape 的一些属性\n  shapeBounds.width = shapeBounds.x2 - shapeBounds.x1 + 1;\n  shapeBounds.height = shapeBounds.y2 - shapeBounds.y1 + 1;\n\n  const x = ~~(allBoundaries.reduce((acc, cur) => acc + cur[0], 0) / allBoundaries.length);\n  const y = ~~(allBoundaries.reduce((acc, cur) => acc + cur[1], 0) / allBoundaries.length);\n\n  const shapeMaxR = ~~Math.max(\n    Math.sqrt((x - shapeBounds.x1) ** 2 + (y - shapeBounds.y1) ** 2),\n    Math.sqrt((x - shapeBounds.x2) ** 2 + (y - shapeBounds.y2) ** 2),\n    Math.sqrt((x - shapeBounds.x1) ** 2 + (y - shapeBounds.y2) ** 2),\n    Math.sqrt((x - shapeBounds.x2) ** 2 + (y - shapeBounds.y1) ** 2)\n  );\n  const shapeRatio = shapeBounds.width / shapeBounds.height;\n  const shapeArea = Object.keys(areas).reduce((acc, key) => (acc += areas[key]), 0);\n  // 输出到 config 上\n  const segmentation = {\n    regions,\n    labels,\n    labelNumber: curLabel - 1\n  };\n  return Object.assign(segmentationInput, {\n    segmentation,\n    shapeConfig,\n    shapeBounds,\n    shapeMaxR,\n    shapeRatio,\n    shapeCenter: [x, y],\n    shapeArea\n  });\n\n  /**\n   * 用四联通去判断是否是边缘像素\n   * @param {*} i\n   * @param {*} j\n   */\n  function isBoundaryPixel(i: number, j: number) {\n    // 四连通位置偏移\n    const offset = [\n      [0, 1],\n      [1, 0],\n      [-1, 0],\n      [0, -1]\n    ];\n\n    // 当 i,j 非背景，且是画布边缘时，则为 boundary\n    if (i === 0 || j === 0 || i === size[1] - 1 || j === size[0] - 1) {\n      return true;\n    }\n\n    // 其他情况用四连通去判断\n    for (let k = 0; k < 4; k++) {\n      let row = i + offset[k][0];\n      let col = j + offset[k][1];\n\n      // 防止坐标溢出图像边界\n      row = row < 0 ? 0 : row >= size[1] ? size[1] - 1 : row;\n      col = col < 0 ? 0 : col >= size[0] ? size[0] - 1 : col;\n\n      if (labels[row * size[0] + col] === 0) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\n/**\n * 判断一个像素是否是前景\n * 即 白色像素 or 透明度为 0\n * @param {*} i\n * @param {*} j\n */\nfunction isEmptyPixel(imageData: ImageData, i: number, j: number) {\n  const width = imageData.width;\n  return (\n    imageData.data[i * width * 4 + j * 4 + 3] === 0 ||\n    (imageData.data[i * width * 4 + j * 4 + 0] === 255 &&\n      imageData.data[i * width * 4 + j * 4 + 1] === 255 &&\n      imageData.data[i * width * 4 + j * 4 + 2] === 255)\n  );\n}\n\n/**\n * 移除图像中的白边\n */\nfunction removeBorder(image: any, canvas: HTMLCanvasElement | any, ctx: CanvasRenderingContext2D | null) {\n  canvas.width = image.width;\n  canvas.height = image.height;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.drawImage(image, 0, 0);\n  const width = canvas.width;\n  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n  let top = 0;\n  let bottom = imageData.height;\n  let left = 0;\n  let right = imageData.width;\n\n  while (top < bottom && rowBlank(imageData, width, top)) {\n    ++top;\n  }\n  while (bottom - 1 > top && rowBlank(imageData, width, bottom - 1)) {\n    --bottom;\n  }\n  while (left < right && columnBlank(imageData, width, left, top, bottom)) {\n    ++left;\n  }\n  while (right - 1 > left && columnBlank(imageData, width, right - 1, top, bottom)) {\n    --right;\n  }\n\n  const trimmed = ctx.getImageData(left, top, right - left, bottom - top);\n  canvas.width = trimmed.width;\n  canvas.height = trimmed.height;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.putImageData(trimmed, 0, 0);\n\n  return canvas;\n}\n\nfunction rowBlank(imageData: ImageData, width: number, y: number) {\n  for (let x = 0; x < width; ++x) {\n    if (!isEmptyPixel(imageData, y, x)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction columnBlank(imageData: ImageData, width: number, x: number, top: number, bottom: number) {\n  for (let y = top; y < bottom; ++y) {\n    if (!isEmptyPixel(imageData, y, x)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * 调整图像大小和位置，将图像按照长边缩放到适应画布大小，并且居中\n * 此处让图片占满画布，padding 不是这个 transform 需要考虑的\n */\nfunction scaleAndMiddleShape(image: any, size: [number, number]) {\n  const width = image.width;\n  const height = image.height;\n  let scale = size[0] / width;\n  if (height * scale > size[1]) {\n    scale = size[1] / height;\n  }\n\n  const newWidth = Math.floor(scale * width);\n  const newHeight = Math.floor(scale * height);\n  // 图片绘制时的坐标\n  const x = (size[0] - newWidth) / 2;\n  const y = (size[1] - newHeight) / 2;\n\n  return {\n    x,\n    y,\n    width: newWidth,\n    height: newHeight,\n    scale\n  };\n}\n/**\n * 将单词分配到不同的区域内\n *\n * 先使用相对简单的分配逻辑，即根据区域面积来决定分配到的单词的 权重大小和数量\n * @param {*} words\n * @param {*} config\n */\nexport function allocateWords(words: CloudWordType[], segmentationOutput: SegmentationOutputType) {\n  const {\n    segmentation: { regions }\n  } = segmentationOutput;\n  let areaMax = -Infinity;\n  let totalArea = 0;\n  let areaMaxIndex = 0;\n\n  // 查找最大面积的区域，同时计算总的面积\n  regions.forEach((region: any, index: number) => {\n    const area = region.area;\n    if (area > areaMax) {\n      areaMax = area;\n      areaMaxIndex = index;\n    }\n    totalArea += area;\n  });\n\n  // 根据每个区域的面积大小给每个区域分配单词数量和权重限制\n  let wordsSum = 0;\n  regions.forEach((region: any) => {\n    const area = region.area;\n    const regionNum = Math.ceil((area / totalArea) * words.length);\n    const regionWeight = area / areaMax;\n\n    region.words = [];\n    region.regionNum = regionNum;\n    region.regionWeight = regionWeight;\n    wordsSum += regionNum;\n  });\n  // 如果有未分配的单词， 则分配到面积最大区域\n  if (wordsSum < words.length) {\n    regions[areaMaxIndex].wordsNum += words.length - wordsSum;\n  }\n\n  // 对单词进行分配，先分配面积最大的区域\n  let currIndex = areaMaxIndex;\n  const regionNums = regions.map((region: any) => region.regionNum);\n  words.forEach((word: any) => {\n    // 记录总的失败次数\n    let failCounter = 0;\n    // 记录失败次数，超过区域的数量，则更新一下所有区域的权重上限\n    let updateCounter = 0;\n    word.regionIndex = -1;\n\n    do {\n      if (regionNums[currIndex] > 0 && word.weight <= regions[currIndex].regionWeight) {\n        word.regionIndex = currIndex;\n        regions[currIndex].words.push(word);\n        regionNums[currIndex]--;\n        currIndex = (currIndex + 1) % regions.length;\n        break;\n      }\n      currIndex = (currIndex + 1) % regions.length;\n\n      failCounter++;\n      updateCounter++;\n      // 如果没有找到合适的区域，则更新所有区域的权重\n      if (updateCounter > regions.length + 1) {\n        regions.forEach((region: any) => {\n          // 这里 0.15 是经验值，可以后续根据业务场景调整\n          region.regionWeight += 0.15;\n        });\n        updateCounter = 0;\n      }\n    } while (word.regionIndex === -1 && failCounter < regions.length * 3);\n\n    // 未分配则分配为 area 最大的区域\n    if (word.regionIndex === -1) {\n      word.regionIndex = areaMaxIndex;\n      regions[areaMaxIndex].words.push(word);\n      regionNums[areaMaxIndex]--;\n    }\n  });\n\n  // 对每个区域里的单词根据权重进行排序\n  regions.forEach((region: any) => {\n    region.words.sort((a: CloudWordType, b: CloudWordType) => b.weight - a.weight);\n  });\n}\n"]}