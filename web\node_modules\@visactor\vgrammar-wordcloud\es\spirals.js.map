{"version": 3, "sources": ["../src/spirals.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,OAAO,GAAG;IACrB,WAAW,EAAE,iBAAiB;IAC9B,WAAW,EAAE,iBAAiB;CAC/B,CAAC;AAEF,SAAS,iBAAiB,CAAC,IAAsB;IAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAqB,CAAC;IAC7E,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAY;IACrC,MAAM,EAAE,GAAG,CAAC,CAAC;IACb,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;YAChD,KAAK,CAAC;gBACJ,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM;YACR,KAAK,CAAC;gBACJ,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM;YACR,KAAK,CAAC;gBACJ,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM;YACR;gBACE,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM;SACT;QACD,OAAO,CAAC,CAAC,EAAE,CAAC,CAAqB,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC", "file": "spirals.js", "sourcesContent": ["export const spirals = {\n  archimedean: archimedeanSpiral,\n  rectangular: rectangularSpiral\n};\n\nfunction archimedeanSpiral(size: [number, number]) {\n  const e = size[0] / size[1];\n  return (t: number) => {\n    return [e * (t *= 0.1) * Math.cos(t), t * Math.sin(t)] as [number, number];\n  };\n}\n\nfunction rectangularSpiral(size: number) {\n  const dy = 4;\n  const dx = (dy * size[0]) / size[1];\n  let x = 0;\n  let y = 0;\n  return (t: number) => {\n    const sign = t < 0 ? -1 : 1;\n    // See triangular numbers: T_n = n * (n + 1) / 2.\n    switch ((Math.sqrt(1 + 4 * sign * t) - sign) & 3) {\n      case 0:\n        x += dx;\n        break;\n      case 1:\n        y += dy;\n        break;\n      case 2:\n        x -= dx;\n        break;\n      default:\n        y -= dy;\n        break;\n    }\n    return [x, y] as [number, number];\n  };\n}\n"]}