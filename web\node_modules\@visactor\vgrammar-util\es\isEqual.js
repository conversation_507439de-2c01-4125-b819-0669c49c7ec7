import { isObjectLike, isArray, isNil, isObject } from "@visactor/vutils";

export const isEqual = (key, current, target, deep = !0) => {
    if (current === target) return !0;
    if (isNil(current) || isNil(target)) return isNil(current) && isNil(target);
    if (!isObjectLike(current) && !isObjectLike(target)) return current === target;
    const c = isArray(current) ? current : current[key], t = isArray(target) ? target : target[key];
    return c === t || !1 !== deep && (isArray(t) ? !(!isArray(c) || t.length !== c.length || !t.every(((v, i) => v === c[i]))) : !!isObject(t) && !(!isObject(c) || Object.keys(t).length !== Object.keys(c).length || !Object.keys(t).every((k => isEqual(k, t, c)))));
};
//# sourceMappingURL=isEqual.js.map