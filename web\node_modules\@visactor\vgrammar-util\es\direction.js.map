{"version": 3, "sources": ["../src/direction.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,YAAY,CAAC,SAAiB;IAC5C,OAAO,SAAS,KAAK,YAAY,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,SAAiB;IAC1C,OAAO,SAAS,KAAK,UAAU,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,SAAiB;IAChD,OAAO,SAAS,KAAK,UAAU,IAAI,SAAS,KAAK,YAAY,CAAC;AAChE,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,QAAgB;IAC9C,OAAO,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,CAAC;AACpG,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,QAAgB;IACnD,OAAO,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,QAAQ,CAAC;AACrD,CAAC", "file": "direction.js", "sourcesContent": ["export function isHorizontal(direction: string) {\n  return direction === 'horizontal';\n}\n\nexport function isVertical(direction: string) {\n  return direction === 'vertical';\n}\n\nexport function isValidDirection(direction: string) {\n  return direction === 'vertical' || direction === 'horizontal';\n}\n\nexport function isValidPosition(position: string) {\n  return position === 'top' || position === 'bottom' || position === 'left' || position === 'right';\n}\n\nexport function isHorizontalPosition(position: string) {\n  return position === 'top' || position === 'bottom';\n}\n"]}