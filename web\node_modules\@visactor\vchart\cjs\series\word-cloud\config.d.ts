export declare const DEFAULT_ROTATE_ANGLES: number[];
export declare const DEFAULT_DRAW_OUT_OF_BOUND = "hidden";
export declare const DEFAULT_MIN_FONT_SIZE = 20;
export declare const DEFAULT_FONTSIZE_RANGE: [number, number];
export declare const DEFAULT_FONT_WEIGHT_RANGE: [number, number];
export declare const DEFAULT_MASK_SHAPE = "circle";
export declare const DEFAULT_ZOOM_TO_FIT: {
    shrink: boolean;
    enlarge: boolean;
    fontSizeLimitMin: number;
};
export declare const DEFAULT_RANDOM = true;
export declare const DEFAULT_FONT_PADDING = 1;
export declare const DEFAULT_TEXT_ALIGN = "center";
export declare const DEFAULT_TEXT_BASE_LINE = "alphabetic";
export declare const SHAPE_TYPE: string[];
