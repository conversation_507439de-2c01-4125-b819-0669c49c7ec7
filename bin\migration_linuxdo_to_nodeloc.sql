-- 数据库迁移脚本：将 Linux DO 相关字段重命名为 Nodeloc
-- 执行前请备份数据库！

-- 1. 重命名用户表中的 linux_do_id 字段为 nodeloc_id
ALTER TABLE users RENAME COLUMN linux_do_id TO nodeloc_id;

-- 2. 更新选项表中的配置项名称
UPDATE options SET `key` = 'NodelocOAuthEnabled' WHERE `key` = 'LinuxDOOAuthEnabled';
UPDATE options SET `key` = 'NodelocClientId' WHERE `key` = 'LinuxDOClientId';
UPDATE options SET `key` = 'NodelocClientSecret' WHERE `key` = 'LinuxDOClientSecret';

-- 3. 更新用户名前缀（可选，如果需要保持现有用户名不变可以跳过这步）
-- UPDATE users SET username = REPLACE(username, 'linuxdo_', 'nodeloc_') WHERE username LIKE 'linuxdo_%';

-- 验证迁移结果
SELECT 'Migration completed successfully' as status;
SELECT COUNT(*) as nodeloc_users_count FROM users WHERE nodeloc_id IS NOT NULL AND nodeloc_id != '';
SELECT `key`, `value` FROM options WHERE `key` IN ('NodelocOAuthEnabled', 'NodelocClientId', 'NodelocClientSecret');
