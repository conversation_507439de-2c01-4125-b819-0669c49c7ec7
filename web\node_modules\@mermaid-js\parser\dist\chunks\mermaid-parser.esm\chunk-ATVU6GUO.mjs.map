{"version": 3, "sources": ["../../../src/language/treemap/tokenBuilder.ts", "../../../src/language/treemap/valueConverter.ts", "../../../src/language/treemap/treemap-validator.ts", "../../../src/language/treemap/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class TreemapTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['treemap']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\n// Regular expression to extract className and styleText from a classDef terminal\nconst classDefRegex = /classDef\\s+([A-Z_a-z]\\w+)(?:\\s+([^\\n\\r;]*))?;?/;\n\nexport class TreemapValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name === 'NUMBER2') {\n      // Convert to a number by removing any commas and converting to float\n      return parseFloat(input.replace(/,/g, ''));\n    } else if (rule.name === 'SEPARATOR') {\n      // Remove quotes\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === 'STRING2') {\n      // Remove quotes\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === 'INDENTATION') {\n      return input.length;\n    } else if (rule.name === 'ClassDef') {\n      // Handle both CLASS_DEF terminal and ClassDef rule\n      if (typeof input !== 'string') {\n        // If we're dealing with an already processed object, return it as is\n        return input;\n      }\n\n      // Extract className and styleText from classDef statement\n      const match = classDefRegex.exec(input);\n      if (match) {\n        // Use any type to avoid type issues\n        return {\n          $type: 'ClassDefStatement',\n          className: match[1],\n          styleText: match[2] || undefined,\n        } as any;\n      }\n    }\n    return undefined;\n  }\n}\n", "import type { ValidationAcceptor, ValidationChecks } from 'langium';\nimport type { MermaidAstType, Treemap } from '../generated/ast.js';\nimport type { TreemapServices } from './module.js';\n\n/**\n * Register custom validation checks.\n */\nexport function registerValidationChecks(services: TreemapServices) {\n  const validator = services.validation.TreemapValidator;\n  const registry = services.validation.ValidationRegistry;\n  if (registry) {\n    // Use any to bypass type checking since we know Treemap is part of the AST\n    // but the type system is having trouble with it\n    const checks: ValidationChecks<MermaidAstType> = {\n      Treemap: validator.checkSingleRoot.bind(validator),\n      // Remove unused validation for TreemapRow\n    };\n    registry.register(checks, validator);\n  }\n}\n\n/**\n * Implementation of custom validations.\n */\nexport class TreemapValidator {\n  /**\n   * Validates that a treemap has only one root node.\n   * A root node is defined as a node that has no indentation.\n   */\n  checkSingleRoot(doc: Treemap, accept: ValidationAcceptor): void {\n    let rootNodeIndentation;\n\n    for (const row of doc.TreemapRows) {\n      // Skip non-node items or items without a type\n      if (!row.item) {\n        continue;\n      }\n\n      if (\n        rootNodeIndentation === undefined && // Check if this is a root node (no indentation)\n        row.indent === undefined\n      ) {\n        rootNodeIndentation = 0;\n      } else if (row.indent === undefined) {\n        // If we've already found a root node, report an error\n        accept('error', 'Multiple root nodes are not allowed in a treemap.', {\n          node: row,\n          property: 'item',\n        });\n      } else if (\n        rootNodeIndentation !== undefined &&\n        rootNodeIndentation >= parseInt(row.indent, 10)\n      ) {\n        accept('error', 'Multiple root nodes are not allowed in a treemap.', {\n          node: row,\n          property: 'item',\n        });\n      }\n    }\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, TreemapGeneratedModule } from '../generated/module.js';\nimport { TreemapTokenBuilder } from './tokenBuilder.js';\nimport { TreemapValueConverter } from './valueConverter.js';\nimport { TreemapValidator, registerValidationChecks } from './treemap-validator.js';\n\n/**\n * Declaration of `Treemap` services.\n */\ninterface TreemapAddedServices {\n  parser: {\n    TokenBuilder: TreemapTokenBuilder;\n    ValueConverter: TreemapValueConverter;\n  };\n  validation: {\n    TreemapValidator: TreemapValidator;\n  };\n}\n\n/**\n * Union of Langium default services and `Treemap` services.\n */\nexport type TreemapServices = LangiumCoreServices & TreemapAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Treemap` services.\n */\nexport const TreemapModule: Module<\n  TreemapServices,\n  PartialLangiumCoreServices & TreemapAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new TreemapTokenBuilder(),\n    ValueConverter: () => new TreemapValueConverter(),\n  },\n  validation: {\n    TreemapValidator: () => new TreemapValidator(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createTreemapServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Treemap: TreemapServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Treemap: TreemapServices = inject(\n    createDefaultCoreModule({ shared }),\n    TreemapGeneratedModule,\n    TreemapModule\n  );\n  shared.ServiceRegistry.register(Treemap);\n\n  // Register validation checks\n  registerValidationChecks(Treemap);\n\n  return { shared, Treemap };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,sBAAN,cAAkC,4BAA4B;AAAA,EAFrE,OAEqE;AAAA;AAAA;AAAA,EAC5D,cAAc;AACnB,UAAM,CAAC,SAAS,CAAC;AAAA,EACnB;AACF;;;ACFA,IAAM,gBAAgB;AAEf,IAAM,wBAAN,cAAoC,8BAA8B;AAAA,EANzE,OAMyE;AAAA;AAAA;AAAA,EAC7D,mBACR,MACA,OACA,UACuB;AACvB,QAAI,KAAK,SAAS,WAAW;AAE3B,aAAO,WAAW,MAAM,QAAQ,MAAM,EAAE,CAAC;AAAA,IAC3C,WAAW,KAAK,SAAS,aAAa;AAEpC,aAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,IAC5C,WAAW,KAAK,SAAS,WAAW;AAElC,aAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,IAC5C,WAAW,KAAK,SAAS,eAAe;AACtC,aAAO,MAAM;AAAA,IACf,WAAW,KAAK,SAAS,YAAY;AAEnC,UAAI,OAAO,UAAU,UAAU;AAE7B,eAAO;AAAA,MACT;AAGA,YAAM,QAAQ,cAAc,KAAK,KAAK;AACtC,UAAI,OAAO;AAET,eAAO;AAAA,UACL,OAAO;AAAA,UACP,WAAW,MAAM,CAAC;AAAA,UAClB,WAAW,MAAM,CAAC,KAAK;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;ACpCO,SAAS,yBAAyB,UAA2B;AAClE,QAAM,YAAY,SAAS,WAAW;AACtC,QAAM,WAAW,SAAS,WAAW;AACrC,MAAI,UAAU;AAGZ,UAAM,SAA2C;AAAA,MAC/C,SAAS,UAAU,gBAAgB,KAAK,SAAS;AAAA;AAAA,IAEnD;AACA,aAAS,SAAS,QAAQ,SAAS;AAAA,EACrC;AACF;AAZgB;AAiBT,IAAM,mBAAN,MAAuB;AAAA,EAxB9B,OAwB8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,gBAAgB,KAAc,QAAkC;AAC9D,QAAI;AAEJ,eAAW,OAAO,IAAI,aAAa;AAEjC,UAAI,CAAC,IAAI,MAAM;AACb;AAAA,MACF;AAEA,UACE,wBAAwB;AAAA,MACxB,IAAI,WAAW,QACf;AACA,8BAAsB;AAAA,MACxB,WAAW,IAAI,WAAW,QAAW;AAEnC,eAAO,SAAS,qDAAqD;AAAA,UACnE,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,WACE,wBAAwB,UACxB,uBAAuB,SAAS,IAAI,QAAQ,EAAE,GAC9C;AACA,eAAO,SAAS,qDAAqD;AAAA,UACnE,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;ACnBO,IAAM,gBAGT;AAAA,EACF,QAAQ;AAAA,IACN,cAAc,6BAAM,IAAI,oBAAoB,GAA9B;AAAA,IACd,gBAAgB,6BAAM,IAAI,sBAAsB,GAAhC;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,IACV,kBAAkB,6BAAM,IAAI,iBAAiB,GAA3B;AAAA,EACpB;AACF;AAgBO,SAAS,sBAAsB,UAA0C,iBAG9E;AACA,QAAM,SAAoC;AAAA,IACxC,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,UAA2B;AAAA,IAC/B,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,OAAO;AAGvC,2BAAyB,OAAO;AAEhC,SAAO,EAAE,QAAQ,QAAQ;AAC3B;AAnBgB;", "names": []}