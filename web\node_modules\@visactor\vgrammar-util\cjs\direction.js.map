{"version": 3, "sources": ["../src/direction.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,YAAY,CAAC,SAAiB;IAC5C,OAAO,SAAS,KAAK,YAAY,CAAC;AACpC,CAAC;AAFD,oCAEC;AAED,SAAgB,UAAU,CAAC,SAAiB;IAC1C,OAAO,SAAS,KAAK,UAAU,CAAC;AAClC,CAAC;AAFD,gCAEC;AAED,SAAgB,gBAAgB,CAAC,SAAiB;IAChD,OAAO,SAAS,KAAK,UAAU,IAAI,SAAS,KAAK,YAAY,CAAC;AAChE,CAAC;AAFD,4CAEC;AAED,SAAgB,eAAe,CAAC,QAAgB;IAC9C,OAAO,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,CAAC;AACpG,CAAC;AAFD,0CAEC;AAED,SAAgB,oBAAoB,CAAC,QAAgB;IACnD,OAAO,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,QAAQ,CAAC;AACrD,CAAC;AAFD,oDAEC", "file": "direction.js", "sourcesContent": ["export function isHorizontal(direction: string) {\n  return direction === 'horizontal';\n}\n\nexport function isVertical(direction: string) {\n  return direction === 'vertical';\n}\n\nexport function isValidDirection(direction: string) {\n  return direction === 'vertical' || direction === 'horizontal';\n}\n\nexport function isValidPosition(position: string) {\n  return position === 'top' || position === 'bottom' || position === 'left' || position === 'right';\n}\n\nexport function isHorizontalPosition(position: string) {\n  return position === 'top' || position === 'bottom';\n}\n"]}