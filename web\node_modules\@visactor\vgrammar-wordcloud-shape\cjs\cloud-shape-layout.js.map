{"version": 3, "sources": ["../src/cloud-shape-layout.ts"], "names": [], "mappings": ";;AAAA,iDAA+C;AAC/C,qCAAyE;AACzE,uCAAoC;AAGpC,mBAAyB,KAAU,EAAE,YAA8B,EAAE,kBAA0C;IAE7G,IAAA,4BAAa,EAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;IACzC,IAAI,YAAY,CAAC,UAAU,KAAK,eAAe,EAAE;QAC/C,IAAA,2BAAkB,EAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;KAC7D;SAAM,IAAI,YAAY,CAAC,UAAU,KAAK,sBAAsB,EAAE;QAC7D,IAAA,0BAAiB,EAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;KAC5D;SAAM;QACL,IAAA,eAAM,EAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;KACjD;IAED,MAAM,YAAY,GAAG,IAAA,iBAAO,EAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;IAItE,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/B;aAAM;YACL,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;KACF;IACD,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;IAWpD,OAAO;QACL,YAAY;QACZ,cAAc;QACd,WAAW;KACZ,CAAC;AACJ,CAAC;AAxCD,4BAwCC", "file": "cloud-shape-layout.js", "sourcesContent": ["import { allocateWords } from './segmentation';\nimport { layout, layoutGlobalShrink, layoutSelfEnlarge } from './wordle';\nimport { filling } from './filling';\nimport type { LayoutConfigType, SegmentationOutputType } from './interface';\n\nexport default function (words: any, layoutConfig: LayoutConfigType, segmentationOutput: SegmentationOutputType) {\n  // 将单词分配到各个连通区域中\n  allocateWords(words, segmentationOutput);\n  if (layoutConfig.layoutMode === 'ensureMapping') {\n    layoutGlobalShrink(words, layoutConfig, segmentationOutput);\n  } else if (layoutConfig.layoutMode === 'ensureMappingEnlarge') {\n    layoutSelfEnlarge(words, layoutConfig, segmentationOutput);\n  } else {\n    layout(words, layoutConfig, segmentationOutput);\n  }\n  // const fillingWords = []\n  const fillingWords = filling(words, layoutConfig, segmentationOutput);\n\n  // 处理布局失败的单词, 设置 visible 为 false\n  // const failedWords = words.filter((word) => !word.hasPlaced)\n  const failedWords = [];\n  const successedWords = [];\n  for (let i = 0; i < words.length; i++) {\n    if (words[i].hasPlaced) {\n      successedWords.push(words[i]);\n    } else {\n      failedWords.push(words[i]);\n    }\n  }\n  failedWords.forEach(word => (word.visible = false));\n\n  // debug 常用代码\n  // const { board, boardSize } = layoutConfig\n  // paint(board, boardSize)\n  // paintLabels(layoutConfig)\n  // draw(words, fillingWords, layoutConfig, 1)\n  // console.log(`核心词数量:${words.length}  填充词数量:${fillingWords.length}`)\n  // console.log('放置失败的单词', failedWords)\n  // console.log(layoutConfig, words, fillingWords)\n\n  return {\n    fillingWords,\n    successedWords,\n    failedWords\n  };\n}\n"]}