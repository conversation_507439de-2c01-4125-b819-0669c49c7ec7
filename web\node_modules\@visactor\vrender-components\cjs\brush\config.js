"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.DEFAULT_SIZE_THRESHOLD = exports.DEFAULT_BRUSH_ATTRIBUTES = void 0, 
exports.DEFAULT_BRUSH_ATTRIBUTES = {
    trigger: "pointerdown",
    updateTrigger: "pointermove",
    endTrigger: "pointerup",
    resetTrigger: "pointerupoutside",
    hasMask: !0,
    brushMode: "single",
    brushType: "rect",
    brushStyle: {
        fill: "#B0C8F9",
        fillOpacity: .2,
        stroke: "#B0C8F9",
        strokeWidth: 2
    },
    brushMoved: !0,
    removeOnClick: !0,
    delayType: "throttle",
    delayTime: 10,
    interactiveRange: {
        y1: -1 / 0,
        y2: 1 / 0,
        x1: -1 / 0,
        x2: 1 / 0
    }
}, exports.DEFAULT_SIZE_THRESHOLD = 5;
//# sourceMappingURL=config.js.map