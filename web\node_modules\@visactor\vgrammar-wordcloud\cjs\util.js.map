{"version": 3, "sources": ["../src/util.ts"], "names": [], "mappings": ";;;AAAA,6CAA8C;AAE9C,SAAgB,WAAW;IAEzB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;QACjB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA1BD,kCA0BC;AAED,SAAgB,mBAAmB;IAEjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAG9D,IAAI,IAAI,GAAG,EAAE,CAAC;IAGd,IAAI,QAAQ,CAAC;IACb,IAAI,MAAM,CAAC;IAEX,OAAO,IAAI,EAAE;QACX,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC;QAC/C,IAAI,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,MAAM,EAAE;YACzF,OAAO,IAAI,GAAG,CAAC,CAAC;SACjB;QAED,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;QAC3C,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QAEpC,IAAI,EAAE,CAAC;KACR;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAxBD,kDAwBC;AAEM,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE;IACzD,OAAO,CACL,MAAM;QACN,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE;QAC/B,GAAG;QACH,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE;QACnC,IAAI;QACJ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE;QAC7C,IAAI,CACL,CAAC;AACJ,CAAC,CAAC;AAVW,QAAA,cAAc,kBAUzB;AAEF,SAAgB,OAAO,CAAC,CAAM;IAC5B,OAAO,IAAA,mBAAU,EAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,CAAC,CAAC;YACE,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;AACR,CAAC;AAND,0BAMC", "file": "util.js", "sourcesContent": ["import { isFunction } from '@visactor/vutils';\n\nexport function isSupported() {\n  // eslint-disable-next-line no-undef\n  const canvas = document.createElement('canvas');\n  if (!canvas || !canvas.getContext) {\n    return false;\n  }\n\n  const ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return false;\n  }\n  if (!ctx.getImageData) {\n    return false;\n  }\n  if (!ctx.fillText) {\n    return false;\n  }\n\n  if (!Array.prototype.some) {\n    return false;\n  }\n  if (!Array.prototype.push) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function getMinFontSizeOfEnv() {\n  // eslint-disable-next-line no-undef\n  const ctx = document.createElement('canvas').getContext('2d');\n\n  // start from 20\n  let size = 20;\n\n  // two sizes to measure\n  let hanWidth;\n  let mWidth;\n\n  while (size) {\n    ctx.font = size.toString(10) + 'px sans-serif';\n    if (ctx.measureText('\\uFF37').width === hanWidth && ctx.measureText('m').width === mWidth) {\n      return size + 1;\n    }\n\n    hanWidth = ctx.measureText('\\uFF37').width;\n    mWidth = ctx.measureText('m').width;\n\n    size--;\n  }\n\n  return 12;\n}\n\nexport const randomHslColor = (min: number, max: number) => {\n  return (\n    'hsl(' +\n    (Math.random() * 360).toFixed() +\n    ',' +\n    (Math.random() * 30 + 70).toFixed() +\n    '%,' +\n    (Math.random() * (max - min) + min).toFixed() +\n    '%)'\n  );\n};\n\nexport function functor(d: any) {\n  return isFunction(d)\n    ? d\n    : function () {\n        return d;\n      };\n}\n"]}