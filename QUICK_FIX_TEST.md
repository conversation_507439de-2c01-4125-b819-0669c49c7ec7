# Nodeloc OAuth 快速修复测试指南

## 问题分析

我发现了问题的根本原因：**前端使用的是localStorage中缓存的旧状态数据，而不是从API获取的最新配置**。

## 已完成的修复

### 1. 前端状态获取修复
- ✅ 修改 `LoginForm.js` 使用 `StatusContext` 而不是 `localStorage`
- ✅ 修改 `RegisterForm.js` 使用 `StatusContext` 而不是 `localStorage`
- ✅ 添加了详细的调试日志

### 2. 后端调试端点
- ✅ 添加了 `/api/debug/oauth/config` - 检查OAuth配置
- ✅ 添加了 `/api/debug/oauth/nodeloc` - 生成OAuth调试URL

## 立即测试步骤

### 1. 重新构建Docker容器
```bash
docker-compose down
docker-compose build
docker-compose up -d
```

### 2. 清除浏览器缓存
**重要**：清除浏览器的localStorage缓存
- 按F12打开开发者工具
- 进入Application/Storage标签
- 清除localStorage中的所有数据
- 或者使用无痕模式测试

### 3. 检查OAuth配置
访问：`http://localhost:3000/api/debug/oauth/config`

**期望输出**：
```json
{
  "success": true,
  "data": {
    "nodeloc_oauth_enabled": true,
    "nodeloc_client_id": "your_actual_client_id",
    "nodeloc_client_secret": "已配置 (长度: XX)",
    "redirect_uri": "http://localhost:3000/oauth/nodeloc"
  }
}
```

### 4. 测试前端OAuth按钮
1. 访问登录页面：`http://localhost:3000/login`
2. 打开浏览器开发者工具（F12）
3. 点击"使用 Nodeloc 继续"按钮
4. 查看控制台输出

**期望的控制台输出**：
```
Nodeloc Login Debug:
status object: {nodeloc_oauth: true, nodeloc_client_id: "your_client_id", ...}
nodeloc_oauth: true
nodeloc_client_id: your_actual_client_id
Nodeloc OAuth Debug Info:
Client ID: your_actual_client_id
Redirect URI: http://localhost:3000/oauth/nodeloc
State: random_state_value
Nonce: random_nonce_value
Complete OAuth URL: https://conn.nodeloc.cc/oauth2/auth?...
URL Parameters:
  response_type: code
  client_id: your_actual_client_id
  redirect_uri: http://localhost:3000/oauth/nodeloc
  scope: openid profile
  state: random_state_value
  nonce: random_nonce_value
```

### 5. 验证OAuth URL
如果控制台显示了完整的OAuth URL，复制该URL到浏览器中测试，应该能正确跳转到Nodeloc授权页面。

## 可能的问题和解决方案

### 问题1：nodeloc_client_id 仍然为空
**原因**：后台配置未保存或StatusContext未更新
**解决方案**：
1. 重新在系统设置中保存Nodeloc配置
2. 刷新页面确保StatusContext更新

### 问题2：OAuth按钮不显示
**原因**：nodeloc_oauth为false
**解决方案**：
1. 在系统设置中启用"允许通过 Nodeloc 账户登录 & 注册"
2. 确保Client ID和Secret都已配置

### 问题3：仍然出现invalid_request错误
**原因**：Nodeloc应用配置问题
**解决方案**：
1. 检查Nodeloc应用管理中的回调地址是否为：`http://localhost:3000/oauth/nodeloc`
2. 确保权限范围包含：`openid profile`

## 成功标志

✅ **配置检查API返回正确的Client ID**
✅ **前端控制台显示正确的OAuth URL**
✅ **点击OAuth按钮能跳转到Nodeloc授权页面**
✅ **授权后能正确回调到应用**

## 如果仍有问题

请提供以下信息：
1. `/api/debug/oauth/config` 的完整输出
2. 浏览器控制台的完整日志
3. 点击OAuth按钮后的具体错误信息

这次修复应该能解决Client ID传递的问题！
