"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.autoHide = void 0;

const vutils_1 = require("@visactor/vutils"), util_1 = require("./util");

function itemIntersect(item1, item2) {
    return (0, vutils_1.isRectIntersect)(item1.AABBBounds, item2.AABBBounds, !1) && (!item1.rotatedBounds || !item2.rotatedBounds || (0, 
    vutils_1.isRotateAABBIntersect)(item1.rotatedBounds, item2.rotatedBounds, !0));
}

const methods = {
    parity: function(items) {
        return items.filter(((item, i) => i % 2 ? item.setAttribute("opacity", 0) : 1));
    },
    greedy: function(items, sep) {
        let a;
        return items.filter(((b, i) => i && intersect(a, b, sep) ? b.setAttribute("opacity", 0) : (a = b, 
        1)));
    }
};

function intersect(textA, textB, sep) {
    const a = textA.AABBBounds, b = textB.AABBBounds;
    return sep > Math.max(b.x1 - a.x2, a.x1 - b.x2, b.y1 - a.y2, a.y1 - b.y2) && (!textA.rotatedBounds || !textB.rotatedBounds || sep > Math.max(textB.rotatedBounds.x1 - textA.rotatedBounds.x2, textA.rotatedBounds.x1 - textB.rotatedBounds.x2, textB.rotatedBounds.y1 - textA.rotatedBounds.y2, textA.rotatedBounds.y1 - textB.rotatedBounds.y2));
}

function hasOverlap(items, pad) {
    for (let b, i = 1, n = items.length, a = items[0]; i < n; a = b, ++i) if (intersect(a, b = items[i], pad)) return !0;
}

function hasBounds(item) {
    const b = item.AABBBounds;
    return b.width() > 1 && b.height() > 1;
}

function reset(items) {
    return items.forEach((item => item.setAttribute("opacity", 1))), items;
}

function autoHide(labels, config) {
    if ((0, vutils_1.isEmpty)(labels)) return;
    const source = labels.filter(hasBounds);
    if ((0, vutils_1.isEmpty)(source)) return;
    let items;
    items = reset(source), (0, util_1.genRotateBounds)(items);
    const {method: method = "parity", separation: sep = 0} = config, reduce = (0, vutils_1.isFunction)(method) ? method : methods[method] || methods.parity;
    if (items.length >= 3 && hasOverlap(items, sep)) {
        do {
            items = reduce(items, sep);
        } while (items.length >= 3 && hasOverlap(items, sep));
        if (items.length < 3 || config.lastVisible) {
            const lastSourceItem = (0, vutils_1.last)(source);
            if (!lastSourceItem.attribute.opacity) {
                const remainLength = items.length;
                if (remainLength > 1) {
                    lastSourceItem.setAttribute("opacity", 1);
                    for (let i = remainLength - 1; i >= 0 && intersect(items[i], lastSourceItem, sep); i--) items[i].setAttribute("opacity", 0);
                }
            }
        }
    }
    source.forEach((item => {
        item.setAttribute("visible", !!item.attribute.opacity);
    }));
}

exports.autoHide = autoHide;
//# sourceMappingURL=auto-hide.js.map
