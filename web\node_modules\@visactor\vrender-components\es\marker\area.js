import { graphicCreator } from "@visactor/vrender-core";

import { isValidNumber, merge } from "@visactor/vutils";

import { Tag } from "../tag";

import { Marker } from "./base";

import { DEFAULT_MARK_AREA_TEXT_STYLE_MAP, DEFAULT_MARK_AREA_THEME } from "./config";

import { limitShapeInBounds } from "../util/limit-shape";

import { loadMarkAreaComponent } from "./register";

loadMarkAreaComponent();

export class MarkArea extends Marker {
    getArea() {
        return this._area;
    }
    getLabel() {
        return this._label;
    }
    constructor(attributes, options) {
        super((null == options ? void 0 : options.skipDefault) ? attributes : merge({}, MarkArea.defaultAttributes, attributes)), 
        this.name = "markArea";
    }
    _getPositionByDirection(area, direction) {
        const {x1: x1, x2: x2, y1: y1, y2: y2} = this._area.AABBBounds;
        return direction.includes("left") || direction.includes("Left") ? {
            x: x1,
            y: (y1 + y2) / 2
        } : direction.includes("right") || direction.includes("Right") ? {
            x: x2,
            y: (y1 + y2) / 2
        } : direction.includes("top") || direction.includes("Top") ? {
            x: (x1 + x2) / 2,
            y: y1
        } : direction.includes("bottom") || direction.includes("Bottom") ? {
            x: (x1 + x2) / 2,
            y: y2
        } : {
            x: (x1 + x2) / 2,
            y: (y1 + y2) / 2
        };
    }
    setLabelPos() {
        var _a;
        if (this._label && this._area) {
            const {label: label = {}} = this.attribute, labelPosition = null !== (_a = label.position) && void 0 !== _a ? _a : "middle", labelPoint = this._getPositionByDirection(this._area, labelPosition);
            if (this._label.setAttributes(Object.assign(Object.assign({}, labelPoint), {
                textStyle: Object.assign(Object.assign({}, DEFAULT_MARK_AREA_TEXT_STYLE_MAP[labelPosition]), label.textStyle)
            })), this.attribute.limitRect && label.confine) {
                const {x: x, y: y, width: width, height: height} = this.attribute.limitRect;
                limitShapeInBounds(this._label, {
                    x1: x,
                    y1: y,
                    x2: x + width,
                    y2: y + height
                });
            }
        }
    }
    initMarker(container) {
        const {points: points, label: label, areaStyle: areaStyle} = this.attribute, area = graphicCreator.polygon(Object.assign({
            points: points
        }, areaStyle));
        area.name = "mark-area-area", this._area = area, container.add(area);
        const markLabel = new Tag(Object.assign({}, label));
        markLabel.name = "mark-area-label", this._label = markLabel, container.add(markLabel), 
        this.setLabelPos();
    }
    updateMarker() {
        const {points: points, label: label, areaStyle: areaStyle} = this.attribute;
        this._area && this._area.setAttributes(Object.assign({
            points: points
        }, areaStyle)), this._area && this._label.setAttributes(Object.assign({
            dx: 0,
            dy: 0
        }, label)), this.setLabelPos();
    }
    isValidPoints() {
        const {points: points} = this.attribute;
        if (!points || points.length < 3) return !1;
        let validFlag = !0;
        return points.forEach((point => {
            isValidNumber(point.x) && isValidNumber(point.y) || (validFlag = !1);
        })), validFlag;
    }
}

MarkArea.defaultAttributes = DEFAULT_MARK_AREA_THEME;
//# sourceMappingURL=area.js.map
