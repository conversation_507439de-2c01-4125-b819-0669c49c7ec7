import React from 'react';
import { Icon } from '@douyinfe/semi-ui';

const NodelocIcon = (props) => {
  function CustomIcon() {
    return (
      <svg
        className='icon'
        viewBox='0 0 16 16'
        version='1.1'
        xmlns='http://www.w3.org/2000/svg'
        width='1em'
        height='1em'
        {...props}
      >
        <g id='nodeloc_icon' data-name='nodeloc_icon'>
          <path
            d='m1.27,4.67h13.45c-.94-1.89-2.51-3.21-4.51-3.88-1.99-.59-3.96-.37-5.8,.57-1.25,.7-2.67,1.9-3.14,3.3Z'
            fill='#4A90E2'
          />
          <path
            d='m12.54,14.01c.87-.7,1.82-1.59,2.18-2.68H1.27c.87,1.74,2.33,3.13,4.2,3.78,2.44,.79,5,.47,7.07-1.1Z'
            fill='#357ABD'
          />
          <path
            d='m1.27,11.33h13.45c-.94,1.89-2.51,3.21-4.51,3.88-1.99.59-3.96.37-5.8-.57-1.25-.7-2.67-1.9-3.14-3.3Z'
            fill='#2C5AA0'
          />
          <circle
            cx='8'
            cy='8'
            r='2'
            fill='#FFFFFF'
          />
        </g>
      </svg>
    );
  }

  return <Icon svg={<CustomIcon />} />;
};

export default NodelocIcon;
