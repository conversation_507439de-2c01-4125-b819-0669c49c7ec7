{"version": 3, "sources": ["../src/extent.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAO/D,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,KAAY,EAAE,IAA2B,EAAE,EAAE;IAClE,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC;IAChE,IAAI,GAAW,CAAC;IAChB,IAAI,GAAW,CAAC;IAEhB,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;QACzB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAGvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAC7B,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACvE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;oBACd,GAAG,GAAG,KAAK,CAAC;oBACZ,GAAG,GAAG,KAAK,CAAC;iBACb;qBAAM;oBACL,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC3B,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC5B;aACF;SACF;QAED,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KACnB;IAED,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,CAAC,CAAC", "file": "extent.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-util/src/extent.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\nimport { isFunction, isNumber, isNil } from '@visactor/vutils';\nimport type { ReturnNumberFunction } from './types';\n\n/**\n * Return an array with minimum and maximum values, in the\n * form [min, max]. Ignores null, undefined, and NaN values.\n */\nexport const extent = (array: any[], func?: ReturnNumberFunction) => {\n  const valueGetter = isFunction(func) ? func : (val: any) => val;\n  let min: number;\n  let max: number;\n\n  if (array && array.length) {\n    const n = array.length;\n\n    // find first valid value\n    for (let i = 0; i < n; i += 1) {\n      let value = valueGetter(array[i]);\n      if (!isNil(value) && isNumber((value = +value)) && !Number.isNaN(value)) {\n        if (isNil(min)) {\n          min = value;\n          max = value;\n        } else {\n          min = Math.min(min, value);\n          max = Math.max(max, value);\n        }\n      }\n    }\n\n    return [min, max];\n  }\n\n  return [min, max];\n};\n"]}