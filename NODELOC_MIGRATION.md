# Linux DO 到 Nodeloc OAuth 迁移指南

本项目已将所有 Linux DO OAuth 功能替换为 Nodeloc OAuth 登录。由于两者都基于相同的 Discourse 论坛系统，OAuth 流程基本相同。

## 主要变更

### 后端变更
1. **控制器文件**：`controller/linuxdo.go` → `controller/nodeloc.go`
2. **常量定义**：
   - `LinuxDOOAuthEnabled` → `NodelocOAuthEnabled`
   - `LinuxDOClientId` → `NodelocClientId`
   - `LinuxDOClientSecret` → `NodelocClientSecret`
3. **用户模型字段**：`LinuxDOId` → `NodelocId`
4. **API路由**：`/api/oauth/linuxdo` → `/api/oauth/nodeloc`
5. **OAuth端点**：
   - 授权端点：`https://conn.nodeloc.cc/oauth2/auth`
   - 令牌端点：`https://conn.nodeloc.cc/oauth2/token`
   - 用户信息端点：`https://conn.nodeloc.cc/oauth2/userinfo`

### 前端变更
1. **组件文件**：`LinuxDoIcon.js` → `NodelocIcon.js`
2. **API函数**：`onLinuxDOOAuthClicked` → `onNodelocOAuthClicked`
3. **路由**：`/oauth/linuxdo` → `/oauth/nodeloc`
4. **状态字段**：`linuxdo_oauth` → `nodeloc_oauth`
5. **用户字段**：`linux_do_id` → `nodeloc_id`

## 数据库迁移

### 自动迁移
执行提供的 SQL 迁移脚本：
```bash
mysql -u your_username -p your_database < bin/migration_linuxdo_to_nodeloc.sql
```

### 手动迁移步骤
1. **备份数据库**（重要！）
2. **重命名用户表字段**：
   ```sql
   ALTER TABLE users RENAME COLUMN linux_do_id TO nodeloc_id;
   ```
3. **更新配置选项**：
   ```sql
   UPDATE options SET `key` = 'NodelocOAuthEnabled' WHERE `key` = 'LinuxDOOAuthEnabled';
   UPDATE options SET `key` = 'NodelocClientId' WHERE `key` = 'LinuxDOClientId';
   UPDATE options SET `key` = 'NodelocClientSecret' WHERE `key` = 'LinuxDOClientSecret';
   ```

## OAuth 应用配置

### 在 Nodeloc 创建 OAuth 应用
1. 访问 [https://conn.nodeloc.cc/apps](https://conn.nodeloc.cc/apps)
2. 创建新的 OAuth2 应用
3. **重要**：设置回调地址为：`https://yourdomain.com/oauth/nodeloc`
   - 注意：这与 PHP SDK 的回调地址 `/oauth2/return.php` 不同
   - 我们的项目使用 `/oauth/nodeloc` 作为统一的回调路径
4. 获取 Client ID 和 Client Secret

### 系统配置
在管理后台的系统设置中：
1. 启用 "允许通过 Nodeloc 账户登录 & 注册"
2. 填入 Nodeloc Client ID
3. 填入 Nodeloc Client Secret
4. 保存设置

## OAuth 流程对比

### Linux DO (旧)
```
授权: https://connect.linux.do/oauth2/authorize
令牌: https://connect.linux.do/oauth2/token
用户: https://connect.linux.do/api/user
```

### Nodeloc (新)
```
授权: https://conn.nodeloc.cc/oauth2/auth
令牌: https://conn.nodeloc.cc/oauth2/token
用户: https://conn.nodeloc.cc/oauth2/userinfo
```

## 用户数据结构

### 旧的 Linux DO 用户数据
```json
{
  "id": 123,
  "username": "user123",
  "name": "User Name",
  "active": true,
  "trust_level": 1,
  "silenced": false
}
```

### 新的 Nodeloc 用户数据
```json
{
  "sub": "123",
  "preferred_username": "user123",
  "name": "User Name",
  "email": "<EMAIL>",
  "email_verified": true,
  "picture": "https://avatar.url",
  "groups": ["users"]
}
```

## 注意事项

1. **现有用户**：已绑定 Linux DO 的用户在迁移后会自动转换为 Nodeloc 绑定
2. **用户名前缀**：新注册用户的用户名前缀从 `linuxdo_` 改为 `nodeloc_`
3. **兼容性**：由于都基于 Discourse 系统，OAuth 流程完全兼容
4. **安全性**：保持了相同的 CSRF 防护和状态验证机制

## 测试验证

迁移完成后，请测试以下功能：
1. Nodeloc OAuth 登录
2. Nodeloc 账户绑定
3. 用户信息正确显示
4. 现有绑定用户正常登录

## 回滚方案

如需回滚到 Linux DO，请：
1. 恢复数据库备份
2. 使用 git 回滚代码更改
3. 重新配置 Linux DO OAuth 应用
