{"version": 3, "sources": ["../src/accessors.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,MAAM,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAE9B,MAAM,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAC9B,UAAU,CAAM;IACd,OAAO,CAAC,CAAC;AACX,CAAC,EACD,EAAE,EACF,UAAU,CACX,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAG,QAAQ,CAC1B;IACE,OAAO,CAAC,CAAC;AACX,CAAC,EACD,EAAE,EACF,MAAM,CACP,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAAG,QAAQ,CACzB;IACE,OAAO,CAAC,CAAC;AACX,CAAC,EACD,EAAE,EACF,KAAK,CACN,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAC5B;IACE,OAAO,IAAI,CAAC;AACd,CAAC,EACD,EAAE,EACF,MAAM,CACP,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAG,QAAQ,CAC3B;IACE,OAAO,KAAK,CAAC;AACf,CAAC,EACD,EAAE,EACF,OAAO,CACR,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,QAAQ,CACjC;IACE,OAAO,EAAE,CAAC;AACZ,CAAC,EACD,EAAE,EACF,aAAa,CACd,CAAC", "file": "accessors.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-util/src/accessors.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\nimport { accessor } from './accessor';\nimport { field } from './field';\n\nexport const id = field('id');\n\nexport const identity = accessor(\n  function (_: any) {\n    return _;\n  },\n  [],\n  'identity'\n);\n\nexport const zero = accessor(\n  function () {\n    return 0;\n  },\n  [],\n  'zero'\n);\n\nexport const one = accessor(\n  function () {\n    return 1;\n  },\n  [],\n  'one'\n);\n\nexport const truthy = accessor(\n  function () {\n    return true;\n  },\n  [],\n  'true'\n);\n\nexport const falsy = accessor(\n  function () {\n    return false;\n  },\n  [],\n  'false'\n);\n\nexport const emptyObject = accessor(\n  function () {\n    return {};\n  },\n  [],\n  'emptyObject'\n);\n"]}