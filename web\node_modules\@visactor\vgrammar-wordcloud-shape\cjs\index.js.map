{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,2DAAkD;AAElD,uDAA8C;AAE9C,+BAAoD;AAA3C,kHAAA,0BAA0B,OAAA;AAE5B,MAAM,gCAAgC,GAAG,GAAG,EAAE;IACnD,uBAAO,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAT,2BAAS,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5F,CAAC,CAAC;AAFW,QAAA,gCAAgC,oCAE3C", "file": "index.js", "sourcesContent": ["import { Factory } from '@visactor/vgrammar-core';\n\nimport { transform } from './wordcloud-shape';\n\nexport { WORDCLOUD_SHAPE_HOOK_EVENT } from './util';\n\nexport const registerWordCloudShapeTransforms = () => {\n  Factory.registerTransform('wordcloudShape', { transform, markPhase: 'beforeJoin' }, true);\n};\n"]}