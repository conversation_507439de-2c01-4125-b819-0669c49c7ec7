{"version": 3, "sources": ["../src/wordle.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,MAAM,CACpB,KAAsB,EACtB,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;IAC1C,MAAM,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,EACzB,OAAO,EAAE,GAAG,EACZ,UAAU,EAAE,MAAM,EAClB,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACX,GAAG,kBAAkB,CAAC;IACvB,MAAM,KAAK,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IAGrD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAE3C,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEnB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC/F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAID,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;QACrD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAmB,EAAE,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,oBAAoB,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;aAC3G;YAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,MAAM;SACP;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC1F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAED,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,KAAoB,EACpB,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;IAC1C,MAAM,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,EACzB,OAAO,EAAE,GAAG,EACZ,UAAU,EAAE,MAAM,EAClB,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACX,GAAG,kBAAkB,CAAC;IACvB,MAAM,KAAK,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IAGrD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QAC3D,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAE3C,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEnB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC/F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;iBAAM;gBAEL,UAAU,GAAG,UAAU,GAAG,YAAY,CAAC,oBAAoB,CAAC;gBAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;iBACpF;gBACD,CAAC,EAAE,CAAC;aACL;SACF;KACF;IA+BD,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,KAAsB,EACtB,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC;IAC1E,MAAM,EACJ,IAAI,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,EACzB,OAAO,EAAE,GAAG,EACZ,UAAU,EAAE,MAAM,EAClB,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACX,GAAG,kBAAkB,CAAC;IACvB,MAAM,WAAW,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,YAAY,CAAC,oBAAoB,CAAC;IAGrD,IAAI,EAAE,GAAG,IAAI,CAAC;IACd,IAAI,cAAc,GAAG,CAAC,CAAC;IAEvB,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAE1B,MAAM,cAAc,GAAG,kBAAkB,CAAC;IAC1C,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC,MAAM,GAAG,cAAc,EAAE;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;KAChD;IAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QAC3D,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAE3C,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEnB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC7G,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;iBAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,IAAI,iBAAiB,GAAG,gBAAgB,EAAE;gBACxF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;gBACrD,IAAI,MAAM,KAAK,EAAE,EAAE;oBACjB,EAAE,GAAG,MAAM,CAAC;oBACZ,cAAc,GAAG,iBAAiB,CAAC;iBACpC;gBAED,iBAAiB,GAAG,iBAAiB,GAAG,UAAU,CAAC;gBACnD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC7C,CAAC,CAAC,CAAC;gBAGH,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7B,UAAU,GAAG,IAAI,CAAC;gBAClB,MAAM;aACP;iBAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,EAAE;gBACrE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC;gBACpD,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,iBAAiB,GAAG,cAAc,CAAC;gBACnC,EAAE,GAAG,IAAI,CAAC;gBAGV,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7B,UAAU,GAAG,IAAI,CAAC;gBAClB,MAAM;aACP;SACF;QACD,IAAI,UAAU,EAAE;YAGd,CAAC,GAAG,CAAC,CAAC,CAAC;YACP,SAAS;SACV;KACF;IAID,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;QACrD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,oBAAoB,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;aAC3G;YAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,MAAM;SACP;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC1F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAED,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,KAAsB,EACtB,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,kBAAkB,EAAE,GAAG,YAAY,CAAC;IAC9D,MAAM,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,EACzB,OAAO,EAAE,GAAG,EACZ,UAAU,EAAE,MAAM,EAClB,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACX,GAAG,kBAAkB,CAAC;IACvB,MAAM,WAAW,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,YAAY,CAAC,qBAAqB,CAAC;IAItD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAClE,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC,MAAM,GAAG,cAAc,EAAE;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,SAAS,GAAG,WAAW,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;KACpD;IAGD,IAAI,yBAAyB,GAAG,CAAC,CAAC;IAElC,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAE5B,IAAI,YAAY,GAAG,KAAK,CAAC;IAGzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QAC3D,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAE3C,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEnB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC/F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE;oBAC5B,yBAAyB,EAAE,CAAC;iBAC7B;gBACD,IAAI,yBAAyB,IAAI,cAAc,IAAI,CAAC,YAAY,EAAE;oBAGhE,mBAAmB,GAAG,mBAAmB,GAAG,UAAU,CAAC;oBAGvD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;wBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;oBAC7C,CAAC,CAAC,CAAC;oBAGH,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAE7B,UAAU,GAAG,IAAI,CAAC;oBAClB,yBAAyB,GAAG,CAAC,CAAC;oBAE9B,MAAM;iBACP;aACF;iBAAM,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,mBAAmB,GAAG,CAAC,EAAE;gBAE9D,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC7C,CAAC,CAAC,CAAC;gBAGH,mBAAmB,GAAG,mBAAmB,GAAG,UAAU,CAAC;gBACvD,YAAY,GAAG,IAAI,CAAC;gBAGpB,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7B,UAAU,GAAG,IAAI,CAAC;gBAElB,MAAM;aACP;iBAAM,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE;gBAGnC,OAAO,kBAAkB,CAAC,KAAK,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;aACpE;SACF;QACD,IAAI,UAAU,EAAE;YAGd,CAAC,GAAG,CAAC,CAAC,CAAC;YACP,SAAS;SACV;KACF;IAID,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;QACrD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,oBAAoB,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;aAC3G;YAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,MAAM;SACP;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC1F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAED,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,CAAC;AAKD,SAAS,KAAK,CACZ,KAAe,EACf,IAAmB,EACnB,IAAY,EACZ,KAAa,EACb,IAAsB,EACtB,SAA2B,EAC3B,UAAkB;IAElB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;IACtB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;IACtB,MAAM,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAExC,MAAM,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC;IAC1B,IAAI,IAAI,CAAC;IACT,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAEZ,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QACjC,MAAM,EACJ,QAAQ,EACR,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EACzC,GAAG,IAAI,CAAC;QACT,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAGb,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;YAChD,MAAM;SACP;QACD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAGzB,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;YACtG,SAAS;SACV;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;YAC/C,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAEzC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAMD,MAAM,UAAU,gBAAgB,CAAC,IAAmB,EAAE,KAAe,EAAE,SAA2B;IAChG,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAE1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC;IAET,IAAI,EAAE,KAAK,CAAC,EAAE;QAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aACnC;YACD,CAAC,IAAI,EAAE,CAAC;SACT;KACF;SAAM;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,GAAG,CAAC,CAAC;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjF;YACD,CAAC,IAAI,EAAE,CAAC;SACT;KACF;AACH,CAAC;AAOD,MAAM,UAAU,kBAAkB,CAAC,IAAmB,EAAE,KAAe,EAAE,SAA2B;IAClG,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAElC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,IAAI,CAAC;IACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAGvD,IAAI,EAAE,KAAK,CAAC,EAAE;QAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;oBACpC,OAAO,IAAI,CAAC;iBACb;aACF;YACD,CAAC,IAAI,EAAE,CAAC;SACT;KACF;SAAM;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,GAAG,CAAC,CAAC;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;oBACpF,OAAO,IAAI,CAAC;iBACb;aACF;YACD,CAAC,IAAI,EAAE,CAAC;SACT;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAa;IAEtC,OAAO,UAAU,CAAS;QACxB,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC;AAMD,MAAM,UAAU,aAAa,CAC3B,MAA+B,EAC/B,GAAoC,EACpC,KAA4B,EAC5B,EAAU;IAEV,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,KAAK,CAAC,EAAE;QAChD,OAAO;KACR;IAED,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;IAC9B,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;IAEvB,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC5B,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC;IAEzB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,CAAC;IACV,IAAI,IAAI,GAAG,CAAC,CAAC;IAEb,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,EAAE,EAAE,CAAC;IACL,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE;QACf,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5C,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAG7F,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QAClE,KAAK,GAAG,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;YACvB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3D,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;SAC9D;QACD,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACjC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAGzB,IAAI,KAAK,GAAG,SAAS,EAAE;YACrB,SAAS,GAAG,KAAK,CAAC;SACnB;QAGD,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,EAAE;YACnB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,IAAI,SAAS,CAAC;YACf,SAAS,GAAG,KAAK,CAAC;SACnB;QAGD,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACX,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,SAAS;aACV;iBAAM;gBACL,MAAM;aACP;SACF;QAED,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE;YACrB,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;SAClB;QAED,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;SACnC;QACD,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACjC;QACD,GAAG,CAAC,OAAO,EAAE,CAAC;QAGd,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAG/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAGpB,CAAC,IAAI,KAAK,CAAC;KACZ;IAED,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,OAAO;KACR;IACD,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;IAErD,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IAGN,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE;QACrB,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,MAAM,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,CAAC,QAAQ;gBAClB,KAAK,EAAE,QAAQ;gBACf,MAAM,EAAE,CAAC,QAAQ;aAClB,CAAC;YACF,MAAM;SACP;QAED,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QACvC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAGpD,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEhF,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,IAAS,CAAC;YACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAEhC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;oBAChD,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBAC/B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAEf,IAAI,CAAC,GAAG,KAAK,EAAE;wBACb,KAAK,GAAG,CAAC,CAAC;qBACX;oBACD,IAAI,CAAC,GAAG,MAAM,EAAE;wBACd,MAAM,GAAG,CAAC,CAAC;qBACZ;oBACD,IAAI,IAAI,CAAC,CAAC;iBACX;aACF;YACD,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,GAAG,IAAI,EAAE;oBACZ,IAAI,GAAG,CAAC,CAAC;iBACV;gBACD,IAAI,CAAC,GAAG,OAAO,EAAE;oBACf,OAAO,GAAG,CAAC,CAAC;iBACb;aACF;SACF;QAGD,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;YAC/B,OAAO,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACrC,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;YACjC,MAAM,EAAE,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACpC,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,OAAO,IAAI,CAAC,EAAE,CAAC;KAChB;AA8BH,CAAC;AAKD,SAAS,kBAAkB,CAAC,kBAA0C;IACpE,MAAM,EACJ,YAAY,EAAE,EAAE,MAAM,EAAE,EACxB,SAAS,EACT,IAAI,EACL,GAAG,kBAAkB,CAAC;IAEvB,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtC,IAAI,KAAK,KAAK,CAAC,EAAE;gBAEf,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE7B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC/B,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACf;SACF;KACF;IAGD,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;QAC1B,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAC5B,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACf;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "file": "wordle.js", "sourcesContent": ["import type { CloudWordType, LayoutConfigType, SegmentationOutputType } from './interface';\n\nexport function layout(\n  words: CloudWordType[],\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const { size, stepFactor } = layoutConfig;\n  const {\n    segmentation: { regions },\n    tempCtx: ctx,\n    tempCanvas: canvas,\n    boardSize,\n    shapeCenter,\n    shapeMaxR,\n    shapeRatio\n  } = segmentationOutput;\n  const board = initBoardWithShape(segmentationOutput);\n\n  // 对每个区域开始进行布局\n  for (const region of regions) {\n    const { words: regionWords, center, maxR, ratio } = region;\n\n    for (let i = 0; i < regionWords.length; i++) {\n      // 批量测量单词的 bounds\n      measureSprite(canvas, ctx, words, i);\n      const word = regionWords[i];\n      word.x = center[0];\n      word.y = center[1];\n\n      if (word.hasText && word.sprite && place(board, word, maxR, ratio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      }\n    }\n  }\n\n  // 对于放置失败的单词，缩小文字大小, 以 shapeCenter 为中心进行布局\n  // 最多尝试尝试3次，如果还是失败，则认为该单词不能放置\n  for (let _ = 0; _ < layoutConfig.textLayoutTimes; _++) {\n    const failedWords = words.filter((word: CloudWordType) => {\n      if (!word.hasPlaced) {\n        word.hasText = false;\n        word.sprite = null;\n        word.fontSize = Math.max(~~(word.fontSize * layoutConfig.fontSizeShrinkFactor), layoutConfig.minFontSize);\n      }\n\n      return !word.hasPlaced;\n    });\n\n    if (failedWords.length === 0) {\n      break;\n    }\n\n    for (let i = 0; i < failedWords.length; i++) {\n      const word = failedWords[i];\n      measureSprite(canvas, ctx, failedWords, i);\n      word.x = shapeCenter[0];\n      word.y = shapeCenter[1];\n      if (word.hasText && place(board, word, shapeMaxR, shapeRatio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      }\n    }\n  }\n\n  layoutConfig.board = board;\n}\n\nexport function layoutSelfShrink(\n  words: CloudWordType,\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const { size, stepFactor } = layoutConfig;\n  const {\n    segmentation: { regions },\n    tempCtx: ctx,\n    tempCanvas: canvas,\n    boardSize,\n    shapeCenter,\n    shapeMaxR,\n    shapeRatio\n  } = segmentationOutput;\n  const board = initBoardWithShape(segmentationOutput);\n\n  // 对每个区域开始进行布局\n  for (const region of regions) {\n    const { words: regionWords, center, maxR, ratio } = region;\n    let fontFactor = 1;\n\n    for (let i = 0; i < regionWords.length; i++) {\n      // 批量测量单词的 bounds\n      measureSprite(canvas, ctx, words, i);\n      const word = regionWords[i];\n      word.x = center[0];\n      word.y = center[1];\n\n      if (word.hasText && word.sprite && place(board, word, maxR, ratio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      } else {\n        // console.log('失败迭代', word.text);\n        fontFactor = fontFactor * layoutConfig.fontSizeShrinkFactor;\n        for (let j = i; j < regionWords.length; j++) {\n          word.hasText = false;\n          word.sprite = null;\n          word.fontSize = Math.max(~~(word.fontSize * fontFactor), layoutConfig.minFontSize);\n        }\n        i--;\n      }\n    }\n  }\n\n  // // 对于放置失败的单词，缩小文字大小, 以 shapeCenter 为中心进行布局\n  // // 最多尝试尝试3次，如果还是失败，则认为该单词不能放置\n  // for (let _ = 0; _ < config.textLayoutTimes; _++) {\n  //   const failedWords = words.filter((word) => {\n  //     if (!word.hasPlaced) {\n  //       word.hasText = false\n  //       word.sprite = null\n  //       word.fontSize = ~~(word.fontSize * config.fontSizeShrinkFactor)\n  //     }\n\n  //     return !word.hasPlaced\n  //   })\n\n  //   if (failedWords.length === 0) break\n\n  //   for (let i = 0; i < failedWords.length; i++) {\n  //     const word = failedWords[i]\n  //     measureSprite(canvas, ctx, failedWords, i)\n  //     word.x = shapeCenter[0]\n  //     word.y = shapeCenter[1]\n  //     if (\n  //       word.hasText &&\n  //       place(board, word, shapeMaxR, shapeRatio, size, boardSize, stepFactor)\n  //     ) {\n  //       word.hasPlaced = true\n  //     }\n  //   }\n  // }\n\n  layoutConfig.board = board;\n}\n\nexport function layoutGlobalShrink(\n  words: CloudWordType[],\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const { stepFactor, importantWordCount, globalShinkLimit } = layoutConfig;\n  const {\n    size,\n    segmentation: { regions },\n    tempCtx: ctx,\n    tempCanvas: canvas,\n    boardSize,\n    shapeCenter,\n    shapeMaxR,\n    shapeRatio\n  } = segmentationOutput;\n  const boardOrigin = initBoardWithShape(segmentationOutput);\n  let board = boardOrigin.slice(0);\n\n  const fontFactor = layoutConfig.fontSizeShrinkFactor;\n\n  // 同一个词如果降低到globalShinkLimit还没有布局成功，恢复到该词未布局状态\n  let id = null;\n  let idIntialFactor = 1;\n  /* eslint-disable no-loop-func */\n  let globalShinkFactor = 1;\n  // 找到高优保障词weight，暂定10个\n  const importantCount = importantWordCount;\n  let weightStd = 0;\n  if (words.length > importantCount) {\n    const wordWeights = words.sort((word0, word1) => {\n      return word1.weight - word0.weight;\n    });\n    weightStd = wordWeights[importantCount].weight;\n  }\n\n  // 对每个区域开始进行布局\n  for (let k = 0; k < regions.length; k++) {\n    const region = regions[k];\n    const { words: regionWords, center, maxR, ratio } = region;\n    let restartTag = false;\n    for (let i = 0; i < regionWords.length; i++) {\n      // 批量测量单词的 bounds\n      measureSprite(canvas, ctx, words, i);\n      const word = regionWords[i];\n      word.x = center[0];\n      word.y = center[1];\n\n      if (!word.skip && word.hasText && word.sprite && place(board, word, maxR, ratio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      } else if (!word.skip && word.weight > weightStd && globalShinkFactor > globalShinkLimit) {\n        const wordId = word.datum[Symbol.for('vGrammar_id')];\n        if (wordId !== id) {\n          id = wordId;\n          idIntialFactor = globalShinkFactor;\n        }\n        // 缩小字号\n        globalShinkFactor = globalShinkFactor * fontFactor;\n        words.forEach(word => {\n          word.hasText = false;\n          word.sprite = null;\n          word.fontSize = word.fontSize * fontFactor; // 这里因为存在字号缩小-还原逻辑，因此不加最小字号限制\n        });\n\n        // 清空布局画布\n        board = boardOrigin.slice(0);\n        // console.log('重启布局', word.text, globalShinkFactor);\n        restartTag = true;\n        break;\n      } else if (!word.skip && word.datum[Symbol.for('vGrammar_id')] === id) {\n        words.forEach(word => {\n          word.hasText = false;\n          word.sprite = null;\n          word.fontSize = word.fontSize / globalShinkFactor;\n        });\n\n        word.skip = true;\n        globalShinkFactor = idIntialFactor;\n        id = null;\n\n        // 清空布局画布\n        board = boardOrigin.slice(0);\n        // console.log('重启布局0', word.text, idIntialFactor);\n        restartTag = true;\n        break;\n      }\n    }\n    if (restartTag) {\n      // 重新布局\n      // k--;\n      k = -1;\n      continue;\n    }\n  }\n\n  // 对于放置失败的单词，缩小文字大小, 以 shapeCenter 为中心进行布局\n  // 最多尝试尝试3次，如果还是失败，则认为该单词不能放置\n  for (let _ = 0; _ < layoutConfig.textLayoutTimes; _++) {\n    const failedWords = words.filter(word => {\n      if (!word.hasPlaced) {\n        word.hasText = false;\n        word.sprite = null;\n        word.fontSize = Math.max(~~(word.fontSize * layoutConfig.fontSizeShrinkFactor), layoutConfig.minFontSize);\n      }\n\n      return !word.hasPlaced;\n    });\n\n    if (failedWords.length === 0) {\n      break;\n    }\n\n    for (let i = 0; i < failedWords.length; i++) {\n      const word = failedWords[i];\n      measureSprite(canvas, ctx, failedWords, i);\n      word.x = shapeCenter[0];\n      word.y = shapeCenter[1];\n      if (word.hasText && place(board, word, shapeMaxR, shapeRatio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      }\n    }\n  }\n\n  layoutConfig.board = board;\n}\n\nexport function layoutSelfEnlarge(\n  words: CloudWordType[],\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const { size, stepFactor, importantWordCount } = layoutConfig;\n  const {\n    segmentation: { regions },\n    tempCtx: ctx,\n    tempCanvas: canvas,\n    boardSize,\n    shapeCenter,\n    shapeMaxR,\n    shapeRatio\n  } = segmentationOutput;\n  const boardOrigin = initBoardWithShape(segmentationOutput);\n  let board = boardOrigin.slice(0);\n\n  const fontFactor = layoutConfig.fontSizeEnlargeFactor;\n  // const fontFactor = 1.5;\n\n  // 找到高优保障词weight，暂定10个\n  const importantCount = Math.min(importantWordCount, words.length);\n  let weightStd = 0;\n  if (words.length > importantCount) {\n    const wordWeights = words.sort((word0, word1) => {\n      return word1.weight - word0.weight;\n    });\n    weightStd = wordWeights[importantCount - 1].weight;\n  }\n\n  // 高优词布局成功数量\n  let importantWordSuccessedNum = 0;\n  // 目前放大系数\n  let globalEnlargeFactor = 1;\n  // 回退标志\n  let layoutFinish = false;\n\n  // 对每个区域开始进行布局\n  for (let k = 0; k < regions.length; k++) {\n    const region = regions[k];\n    const { words: regionWords, center, maxR, ratio } = region;\n    let restartTag = false;\n    for (let i = 0; i < regionWords.length; i++) {\n      // 批量测量单词的 bounds\n      measureSprite(canvas, ctx, words, i);\n      const word = regionWords[i];\n      word.x = center[0];\n      word.y = center[1];\n\n      if (word.hasText && word.sprite && place(board, word, maxR, ratio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n        if (word.weight >= weightStd) {\n          importantWordSuccessedNum++;\n        }\n        if (importantWordSuccessedNum >= importantCount && !layoutFinish) {\n          // 重点词完全布局，尝试增大字号\n          // 增大系数\n          globalEnlargeFactor = globalEnlargeFactor * fontFactor;\n\n          // 增大字号\n          words.forEach(word => {\n            word.hasText = false;\n            word.sprite = null;\n            word.fontSize = word.fontSize * fontFactor; // 这里因为存在字号缩小-还原逻辑，因此不加最小字号限制\n          });\n\n          // 清空布局画布\n          board = boardOrigin.slice(0);\n          // console.log('重启布局', word.text, globalEnlargeFactor);\n          restartTag = true;\n          importantWordSuccessedNum = 0;\n\n          break;\n        }\n      } else if (word.weight >= weightStd && globalEnlargeFactor > 1) {\n        // 之前重点词完全布局，此次重点词未完成布局，回退字号\n        words.forEach(word => {\n          word.hasText = false;\n          word.sprite = null;\n          word.fontSize = word.fontSize / fontFactor;\n        });\n\n        // 恢复系数\n        globalEnlargeFactor = globalEnlargeFactor / fontFactor;\n        layoutFinish = true;\n\n        // 清空布局画布\n        board = boardOrigin.slice(0);\n        // console.log('重启布局0', word.text, globalEnlargeFactor);\n        restartTag = true;\n\n        break;\n      } else if (word.weight >= weightStd) {\n        // 初次未完成布局，使用ensureMapping算法\n        // console.log('use layoutGlobalShrink')\n        return layoutGlobalShrink(words, layoutConfig, segmentationOutput);\n      }\n    }\n    if (restartTag) {\n      // 重新布局\n      // k--;\n      k = -1;\n      continue;\n    }\n  }\n\n  // 对于放置失败的单词，缩小文字大小, 以 shapeCenter 为中心进行布局\n  // 最多尝试尝试3次，如果还是失败，则认为该单词不能放置\n  for (let _ = 0; _ < layoutConfig.textLayoutTimes; _++) {\n    const failedWords = words.filter(word => {\n      if (!word.hasPlaced) {\n        word.hasText = false;\n        word.sprite = null;\n        word.fontSize = Math.max(~~(word.fontSize * layoutConfig.fontSizeShrinkFactor), layoutConfig.minFontSize);\n      }\n\n      return !word.hasPlaced;\n    });\n\n    if (failedWords.length === 0) {\n      break;\n    }\n\n    for (let i = 0; i < failedWords.length; i++) {\n      const word = failedWords[i];\n      measureSprite(canvas, ctx, failedWords, i);\n      word.x = shapeCenter[0];\n      word.y = shapeCenter[1];\n      if (word.hasText && place(board, word, shapeMaxR, shapeRatio, size, boardSize, stepFactor)) {\n        word.hasPlaced = true;\n      }\n    }\n  }\n\n  layoutConfig.board = board;\n}\n\n/**\n * 使用螺旋线放置单词，成功返回 true\n */\nfunction place(\n  board: number[],\n  word: CloudWordType,\n  maxR: number,\n  ratio: number,\n  size: [number, number],\n  boardSize: [number, number],\n  stepFactor: number\n) {\n  const startX = word.x;\n  const startY = word.y;\n  const spiral = archimedeanSpiral(ratio);\n\n  const dt = 1 * stepFactor;\n  let dxdy;\n  let dx;\n  let dy;\n  let t = -dt;\n\n  while ((dxdy = spiral((t += dt)))) {\n    const {\n      wordSize,\n      bounds: { dTop, dBottom, dLeft, dRight }\n    } = word;\n    dx = dxdy[0];\n    dy = dxdy[1];\n\n    // 判断螺旋线是否超出了最大的半径\n    if (Math.min(Math.abs(dx), Math.abs(dy)) >= maxR) {\n      break;\n    }\n    word.x = ~~(startX + dx);\n    word.y = ~~(startY + dy);\n\n    // 检测根据单词的 bounds 检测是否超出范围\n    if (word.x - dLeft < 0 || word.x + dRight > size[0] || word.y - dTop < 0 || word.y + dBottom > size[1]) {\n      continue;\n    }\n\n    if (!isCollideWithBoard(word, board, boardSize)) {\n      placeWordOnBoard(word, board, boardSize);\n\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * 在 board 中放置 word\n * 会在 filling 中复用\n */\nexport function placeWordOnBoard(word: CloudWordType, board: number[], boardSize: [number, number]) {\n  const { wordSize } = word;\n  // 放置单词，以 x, y 为中心\n  const sprite = word.sprite;\n  const w = wordSize[0] >> 5; // 单词占用的 int 的数量\n  const sw = boardSize[0] >> 5; // board 的宽度\n  const lx = word.x - (w << 4); // 单词的左边界\n  const sx = lx % 32; // 单词偏移（px）, 当前元素右侧移除数量\n  const msx = 32 - sx; // 需要从sprite上一个元素中移除的数量\n  const h = wordSize[1];\n  let x = (word.y - (wordSize[1] >> 1)) * sw + (lx >> 5); // 数组的起始位置\n  let last;\n\n  if (sx === 0) {\n    // 恰好对齐，不需要偏移\n    for (let j = 0; j < h; j++) {\n      for (let i = 0; i < w; i++) {\n        board[x + i] |= sprite[j * w + i];\n      }\n      x += sw;\n    }\n  } else {\n    for (let j = 0; j < h; j++) {\n      last = 0;\n      for (let i = 0; i <= w; i++) {\n        board[x + i] |= (last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0);\n      }\n      x += sw;\n    }\n  }\n}\n\n/**\n * 检测 word 是否与 board 中的元素发生碰撞\n *\n * 会在 filling words 中复用\n */\nexport function isCollideWithBoard(word: CloudWordType, board: number[], boardSize: [number, number]) {\n  const { sprite, wordSize } = word;\n\n  const sw = boardSize[0] >> 5;\n  const w = wordSize[0] >> 5;\n  const lx = word.x - (w << 4); // 单词的左边界\n  const sx = lx % 32; // sprite数组左侧偏移\n  const msx = 32 - sx; // 位移遮罩\n  const h = wordSize[1];\n  let last;\n  let x = (word.y - (wordSize[1] >> 1)) * sw + (lx >> 5); // 数组的起始位置\n\n  // 逐行遍历单词sprite，判断与已绘制内容重叠\n  if (sx === 0) {\n    // 恰好对齐，不需要偏移\n    for (let j = 0; j < h; j++) {\n      for (let i = 0; i < w; i++) {\n        if (board[x + i] & sprite[j * w + i]) {\n          return true;\n        }\n      }\n      x += sw;\n    }\n  } else {\n    for (let j = 0; j < h; j++) {\n      last = 0;\n      for (let i = 0; i <= w; i++) {\n        if (((last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0)) & board[x + i]) {\n          return true;\n        }\n      }\n      x += sw;\n    }\n  }\n\n  return false;\n}\n\nfunction archimedeanSpiral(ratio: number) {\n  // t 为弧度值\n  return function (t: number) {\n    return [ratio * (t *= 0.1) * Math.cos(t), t * Math.sin(t)];\n  };\n}\n\n/**\n * 测量一批单词的 sprite\n * 会在测量 filling words 时复用，修改时注意兼容性\n */\nexport function measureSprite(\n  canvas: HTMLCanvasElement | any,\n  ctx: CanvasRenderingContext2D | null,\n  words: CloudWordType[] | any,\n  wi: number\n) {\n  if (words[wi].sprite || words[wi].fontSize === 0) {\n    return;\n  }\n\n  const cw = 2048;\n  const ch = 2048;\n  const radians = Math.PI / 180;\n  const n = words.length;\n\n  canvas.width = cw;\n  canvas.height = ch;\n  ctx.clearRect(0, 0, cw, ch);\n  ctx.textAlign = 'center';\n\n  let x = 0;\n  let y = 0;\n  let maxHeight = 0;\n  let wordW; // 单词盒子 宽度\n  let wordH;\n  let yMax = 0; // 记录画布中绘制的 y 最大范围\n\n  const wiDist = wi;\n  --wi;\n  while (++wi < n) {\n    const word = words[wi];\n    const fontSize = Math.max(word.fontSize, 2); // 最小字号2px\n    ctx.save();\n    ctx.font = word.fontStyle + ' ' + word.fontWeight + ' ' + fontSize + 'px ' + word.fontFamily;\n\n    // 计算单词盒子宽高\n    wordW = ctx.measureText(word.text + 'm').width + word.padding * 2;\n    wordH = fontSize * 2 + word.padding * 2;\n\n    if (word.rotate !== 0) {\n      const sr = Math.sin(word.rotate * radians);\n      const cr = Math.cos(word.rotate * radians);\n      const wcr = wordW * cr;\n      const wsr = wordW * sr;\n      const hcr = wordH * cr;\n      const hsr = wordH * sr;\n      wordW = Math.max(Math.abs(wcr + hsr), Math.abs(wcr - hsr));\n      wordH = ~~Math.max(Math.abs(wsr + hcr), Math.abs(wsr - hcr));\n    }\n    wordW = ((wordW + 31) >> 5) << 5; // 宽度必须是 32 的倍数\n    wordH = Math.ceil(wordH);\n\n    // 记录当前行最大高度\n    if (wordH > maxHeight) {\n      maxHeight = wordH;\n    }\n\n    // 如果当前行放不下，就另起一行，y方向向下移动当前行的最大高度\n    if (x + wordW >= cw) {\n      x = 0;\n      y += maxHeight;\n      maxHeight = wordH;\n    }\n\n    // 如果绘制不下，则停止\n    if (y + wordH >= ch) {\n      if (y === 0) {\n        word.hasText = false;\n        continue;\n      } else {\n        break;\n      }\n    }\n    // 更新绘制范围 y 的最大值\n    if (y + wordH >= yMax) {\n      yMax = y + wordH;\n    }\n\n    ctx.translate(x + (wordW >> 1), y + (wordH >> 1));\n    if (word.rotate !== 0) {\n      ctx.rotate(word.rotate * radians);\n    }\n    ctx.fillText(word.text, 0, 0);\n    if (word.padding) {\n      ctx.lineWidth = 2 * word.padding;\n      ctx.strokeText(word.text, 0, 0);\n    }\n    ctx.restore();\n\n    // 词语绘制完成，记录其在画布上位置信息\n    word.LT = [x, y]; // 左上角点\n    word.wordSize = [wordW, wordH];\n\n    // 指示在临时画布上绘制过了单词\n    word.hasText = true;\n\n    // x位置右移，等待下一个词语绘制\n    x += wordW;\n  }\n\n  if (yMax === 0) {\n    return;\n  }\n  const pixels = ctx.getImageData(0, 0, cw, yMax).data;\n\n  let i;\n  let j;\n\n  // 提取画布上的 sprite 信息\n  while (--wi >= wiDist) {\n    const word = words[wi];\n    if (!word.hasText) {\n      word.bounds = {\n        dTop: Infinity,\n        dBottom: -Infinity,\n        dLeft: Infinity,\n        dRight: -Infinity\n      };\n      break;\n    }\n\n    const { LT = [0, 0], wordSize } = word;\n    [x, y] = LT;\n    const w32 = wordSize[0] >> 5;\n    // 将数组归0\n    const sprite = new Array(w32 * wordSize[1]).fill(0);\n\n    // 先记录单词 bounds 的行列号，然后转换成与中心的delta\n    let [dTop, dBottom, dLeft, dRight] = [Infinity, -Infinity, Infinity, -Infinity];\n\n    for (j = 0; j < wordSize[1]; j++) {\n      let seen: any;\n      for (i = 0; i < wordSize[0]; i++) {\n        // 取 alpha 通道的值，\n        if (pixels[((y + j) * cw + (x + i)) * 4 + 3] > 0) {\n          const k = w32 * j + (i >> 5);\n          const m = 1 << (31 - (i % 32));\n          sprite[k] |= m;\n\n          if (i < dLeft) {\n            dLeft = i;\n          }\n          if (i > dRight) {\n            dRight = i;\n          }\n          seen |= m;\n        }\n      }\n      if (seen) {\n        if (j < dTop) {\n          dTop = j;\n        }\n        if (j > dBottom) {\n          dBottom = j;\n        }\n      }\n    }\n\n    // 记录单词准确的的 bounds\n    word.bounds = {\n      dTop: (wordSize[1] >> 1) - dTop,\n      dBottom: dBottom - (wordSize[1] >> 1),\n      dLeft: (wordSize[0] >> 1) - dLeft,\n      dRight: dRight - (wordSize[0] >> 1)\n    };\n    word.sprite = sprite;\n    // 后续操作中 LT 无意义\n    delete word.LT;\n  }\n\n  // debug 代码\n  // words.forEach((word) => {\n  //   const {\n  //     LT,\n  //     wordSize,\n  //     hasText,\n  //     sprite,\n  //     bounds: { dTop, dBottom, dLeft, dRight },\n  //   } = word\n  //   if (hasText) {\n  //     paint(sprite, wordSize)\n  //     // 绘制用于 debug 的\n  //     ctx.save()\n  //     ctx.strokeStyle = '#f00'\n  //     ctx.fillStyle = '#0f0'\n  //     // 绘制 word 包围盒\n  //     ctx.strokeRect(...LT, ...wordSize)\n  //     ctx.translate(LT[0] + wordSize[0] / 2, LT[1] + wordSize[1] / 2)\n  //     ctx.strokeStyle = '#00f'\n  //     // 绘制bounds\n  //     ctx.strokeRect(-dLeft, -dTop, dLeft + dRight, dTop + dBottom)\n  //     // 绘制中心点\n  //     ctx.fillRect(0, 0, 3, 3)\n  //     ctx.restore()\n  //   }\n  // })\n\n  // document.body.prepend(canvas)\n}\n\n/**\n *  根据 shape 相关的信息初始化 board\n */\nfunction initBoardWithShape(segmentationOutput: SegmentationOutputType) {\n  const {\n    segmentation: { labels },\n    boardSize,\n    size\n  } = segmentationOutput;\n  // board 每个 int 编码 32 个像素的占用信息，求得 w32 表示一行有几个 int\n  const w32 = boardSize[0] >> 5;\n  const board = new Array(w32 * size[1]).fill(0);\n\n  for (let i = 0; i < size[1]; i++) {\n    for (let j = 0; j < size[0]; j++) {\n      const label = labels[i * size[0] + j];\n      if (label === 0) {\n        // 取得 board 中对应 int 的索引\n        const k = w32 * i + (j >> 5);\n        // 构造代表该像素被占用的 int\n        const m = 1 << (31 - (j % 32));\n        board[k] |= m;\n      }\n    }\n  }\n\n  // 对 boardSize 和 size 进行比较，如果 boardSize 大于 size，则将差距的部分设置为不可放置\n  if (boardSize[0] > size[0]) {\n    const width = boardSize[0] - size[0];\n    const m = (1 << width) - 1;\n    for (let y = 0; y < size[1]; y++) {\n      const k = w32 * y + w32 - 1;\n      board[k] |= m;\n    }\n  }\n\n  return board;\n}\n"]}