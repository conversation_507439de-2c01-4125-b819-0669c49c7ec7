{"version": 3, "sources": ["../src/compare.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC5D,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AACtD,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAGhC,MAAM,UAAU,GAAG,MAAM,CAAC;AAE1B,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,CAAO,EAAE,CAAO,EAAE,EAAE;IAC5C,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACpC,OAAO,CAAC,CAAC,CAAC;KACX;IAED,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACpC,OAAO,CAAC,CAAC;KACV;IAED,MAAM,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;QACrD,OAAO,CAAC,CAAC,CAAC;KACX;IAED,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;QACrD,OAAO,CAAC,CAAC;KACV;IAED,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,WAAgC,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE;IACvF,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC3D,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,MAA6B,EAAE,MAAgB,EAAE,CAAS,EAAE,EAAE;IAC9E,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,OAAO,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE;QACxB,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC3B,CAAC,IAAI,CAAC,CAAC;YACP,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3B;QAED,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,MAA6B,EAAE,MAAgB,EAAE,EAAE,CACrE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AAEjG,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,MAAuB,EAAE,MAA0B,EAAE,MAAW,EAAE,EAAE,EAAE;IAC5F,MAAM,WAAW,GAAa,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAElD,MAAM,GAAG,GAAa,EAAE,CAAC;IACzB,MAAM,GAAG,GAA0B,EAAE,CAAC;IACtC,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC;IAEzC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC7B,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;YACZ,OAAO;SACR;QACD,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5D,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtB,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,QAAgB,EAAE,EAAE;YAC/D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC", "file": "compare.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-util/src/compare.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\nimport { isNil, array, isFunction } from '@visactor/vutils';\nimport { accessor, accessorFields } from './accessor';\nimport { field } from './field';\nimport type { FieldGetterFunction, ParameterFields } from './types';\n\nconst DESCENDING = 'desc';\n\nexport const ascending = (u?: any, v?: any) => {\n  if ((u < v || isNil(u)) && !isNil(v)) {\n    return -1;\n  }\n\n  if ((u > v || isNil(v)) && !isNil(u)) {\n    return 1;\n  }\n\n  const numericV = v instanceof Date ? +v : v;\n  const numericU = u instanceof Date ? +u : u;\n\n  if (Number.isNaN(numericU) && !Number.isNaN(numericV)) {\n    return -1;\n  }\n\n  if (Number.isNaN(numericV) && !Number.isNaN(numericU)) {\n    return 1;\n  }\n\n  return 0;\n};\n\nconst compare1 = (fieldGetter: FieldGetterFunction, order: number) => (a: any, b: any) => {\n  return ascending(fieldGetter(a), fieldGetter(b)) * order;\n};\n\nconst compareN = (fields: FieldGetterFunction[], orders: number[], n: number) => {\n  orders.push(0); // pad zero for convenient lookup\n  return (a: any, b: any) => {\n    let f;\n    let c = 0;\n    let i = -1;\n    while (c === 0 && i + 1 < n) {\n      i += 1;\n      f = fields[i];\n      c = ascending(f(a), f(b));\n    }\n\n    return c * orders[i];\n  };\n};\n\nconst comparator = (fields: FieldGetterFunction[], orders: number[]) =>\n  fields.length === 1 ? compare1(fields[0], orders[0]) : compareN(fields, orders, fields.length);\n\nexport const compare = (fields: ParameterFields, orders?: string | string[], opt: any = {}) => {\n  const arrayOrders: string[] = array(orders) || [];\n\n  const ord: number[] = [];\n  const get: FieldGetterFunction[] = [];\n  const fmap = {};\n  const gen = opt.comparator || comparator;\n\n  array(fields).forEach((f, i) => {\n    if (isNil(f)) {\n      return;\n    }\n    ord.push(arrayOrders[i] === DESCENDING ? -1 : 1);\n    const fieldGetter = isFunction(f) ? f : field(f, null, opt);\n    get.push(fieldGetter);\n\n    (accessorFields(fieldGetter) || []).forEach((fieldStr: string) => {\n      fmap[fieldStr] = 1;\n    });\n  });\n\n  return get.length === 0 ? null : accessor(gen(get, ord), Object.keys(fmap));\n};\n"]}