# Nodeloc OAuth 故障排查指南

## 错误信息分析

### 当前错误
```json
{
  "error": "invalid_request",
  "error_description": "The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed. The 'redirect_uri' parameter is required when using OpenID Connect 1.0."
}
```

## 可能的原因和解决方案

### 1. 检查 OAuth 应用配置

#### 在 Nodeloc 应用管理中检查：
1. 访问 [https://conn.nodeloc.cc/apps](https://conn.nodeloc.cc/apps)
2. 找到你的 OAuth 应用
3. 确认以下设置：

**必须设置的字段：**
- ✅ **应用名称**：任意名称
- ✅ **回调 URL**：`https://yourdomain.com/oauth/nodeloc`
- ✅ **权限范围**：必须包含 `openid` 和 `profile`

**常见错误：**
- ❌ 回调URL末尾多了斜杠：`/oauth/nodeloc/`
- ❌ 协议不匹配：HTTP vs HTTPS
- ❌ 域名不匹配：www vs 非www
- ❌ 端口号问题：开发环境的端口号

### 2. 检查前端代码

#### 当前的OAuth URL构建：
```javascript
const params = new URLSearchParams({
  'response_type': 'code',
  'client_id': nodeloc_client_id,
  'redirect_uri': redirect_uri,
  'scope': 'openid profile',
  'state': state
});
const oauthUrl = `https://conn.nodeloc.cc/oauth2/auth?${params.toString()}`;
```

#### 验证步骤：
1. 打开浏览器开发者工具
2. 点击"使用 Nodeloc 继续"按钮
3. 查看控制台输出的调试信息
4. 检查生成的URL是否包含所有必需参数

### 3. 手动测试 OAuth URL

#### 使用提供的测试工具：
1. 打开 `test_oauth.html` 文件
2. 输入你的 Client ID
3. 确认域名和回调路径
4. 生成测试URL并点击测试

#### 标准的OAuth URL格式：
```
https://conn.nodeloc.cc/oauth2/auth?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=https%3A//yourdomain.com/oauth/nodeloc&scope=openid%20profile&state=RANDOM_STATE
```

### 4. 常见配置错误

#### 4.1 回调地址不匹配
**问题**：注册时的回调地址与实际使用的不一致

**解决方案**：
- 开发环境：`http://localhost:3000/oauth/nodeloc`
- 生产环境：`https://yourdomain.com/oauth/nodeloc`

#### 4.2 Client ID 错误
**问题**：使用了错误的 Client ID

**解决方案**：
1. 重新检查 Nodeloc 应用管理中的 Client ID
2. 确保没有多余的空格或特殊字符

#### 4.3 权限范围问题
**问题**：scope 参数不正确

**解决方案**：
- 必须包含：`openid profile`
- 可选添加：`email`

### 5. 网络和环境检查

#### 5.1 HTTPS 要求
- 生产环境必须使用 HTTPS
- 开发环境可以使用 HTTP (localhost)

#### 5.2 防火墙和代理
- 确保可以访问 `conn.nodeloc.cc`
- 检查是否有代理或防火墙阻止请求

### 6. 调试步骤

#### 步骤1：验证基本连接
```bash
curl -I https://conn.nodeloc.cc/oauth2/auth
```

#### 步骤2：手动构建OAuth URL
使用 `debug_oauth_url.html` 工具生成标准URL

#### 步骤3：检查浏览器网络面板
1. 打开开发者工具 → Network 面板
2. 点击OAuth登录按钮
3. 查看实际发送的请求

#### 步骤4：对比PHP SDK
参考 `oauth2/connect.php` 中的实现：
```php
$params = [
    'response_type' => 'code',
    'client_id' => $ClientId,
    'redirect_uri' => $RedirectUri,
    'scope' => 'openid profile',
    'state' => $state
];
$Oauth_url = 'https://conn.nodeloc.cc/oauth2/auth?' . http_build_query($params);
```

### 7. 临时解决方案

#### 使用PHP SDK测试
1. 配置 `oauth2/config.php`
2. 访问 `oauth2/index.php`
3. 测试OAuth流程是否正常

#### 如果PHP SDK正常工作：
1. 对比PHP生成的URL和我们生成的URL
2. 检查参数编码是否一致
3. 验证回调地址配置

### 8. 联系技术支持

如果以上步骤都无法解决问题：

1. **收集信息**：
   - Client ID（脱敏处理）
   - 完整的OAuth URL
   - 错误截图
   - 浏览器和版本

2. **联系渠道**：
   - [Nodeloc 社区](https://nodeloc.cc/)
   - [OAuth2 应用管理](https://conn.nodeloc.cc/apps)

### 9. 检查清单

在提交问题前，请确认：

- [ ] Client ID 正确无误
- [ ] 回调地址与注册时完全一致
- [ ] 包含所有必需参数（response_type, client_id, redirect_uri, scope, state）
- [ ] 参数正确编码
- [ ] 网络连接正常
- [ ] 浏览器控制台无其他错误
- [ ] 使用了正确的授权端点URL

### 10. 下一步

如果问题仍然存在，请：
1. 使用 `test_oauth.html` 生成测试URL
2. 手动访问该URL查看具体错误
3. 截图保存错误信息
4. 检查Nodeloc应用管理中的配置
