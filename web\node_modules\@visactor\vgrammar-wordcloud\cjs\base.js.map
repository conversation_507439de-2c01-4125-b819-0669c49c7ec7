{"version": 3, "sources": ["../src/base.ts"], "names": [], "mappings": ";;;AAAA,6CAA4E;AAG5E,qCAA4C;AAC5C,iCAAiD;AAEjD,MAAsB,UAAU;IA2C9B,YAAY,OAAmB;;QAC7B,IAAI,CAAC,OAAO,GAAG,IAAA,cAAK,EAAC,EAAE,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,IAAA,mBAAU,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnC,IAAI,CAAC,KAAK,GAAG,IAAA,yBAAgB,EAAC,IAAI,CAAC,OAAO,CAAC,KAAe,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;SACjC;QAGD,IAAI,CAAC,OAAO,GAAG,MAAA,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mCAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,iBAAiB,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAI,CAAC,eAAe,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,gBAAgB,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,iBAAiB,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAI,CAAC,cAAc,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,cAAc,mCAAI,CAAC,CAAC,GAAU,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAE3E,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAC1B,KAAK,aAAa;gBAChB,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE;oBACvB,OAAO,IAAA,qBAAc,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChC,CAAC,CAAC;gBACF,MAAM;YAER,KAAK,cAAc;gBACjB,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE;oBACvB,OAAO,IAAA,qBAAc,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChC,CAAC,CAAC;gBACF,MAAM;YAER;gBACE,IAAI,CAAC,YAAY,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAChD,MAAM;SACT;QAED,IAAI,CAAC,IAAA,cAAK,EAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC/B,IAAI,CAAC,aAAa,GAAG,IAAA,mBAAU,EAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClD,CAAC,CAAC,CAAC,CAAM,EAAE,EAAE,WAAC,OAAA,IAAA,uBAAc,EAAC,MAAC,IAAI,CAAC,OAAO,CAAC,MAA6B,CAAC,CAAC,CAAC,mCAAI,CAAC,CAAC,CAAA,EAAA;gBACjF,CAAC,CAAC,GAAG,EAAE,WAAC,OAAA,IAAA,uBAAc,EAAC,MAAC,IAAI,CAAC,OAAO,CAAC,MAAiB,mCAAI,CAAC,CAAC,CAAA,EAAA,CAAC;SAChE;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACpF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAEjF,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE;gBACxB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;oBAClC,OAAO,CAAC,CAAC;iBACV;gBAED,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;oBAC5C,OAAO,CAAC,CAAC;iBACV;gBAED,IAAI,aAAa,KAAK,CAAC,EAAE;oBACvB,OAAO,WAAW,CAAC;iBACpB;gBAED,IAAI,aAAa,GAAG,CAAC,EAAE;oBAErB,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;iBACxG;gBACD,OAAO,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC;YACrD,CAAC,CAAC;SACH;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;SAC9B;IACH,CAAC;IAGD,UAAU;;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,MAAA,IAAI,CAAC,gBAAgB,mCAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;SACnG;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;IACnH,CAAC;IAED,cAAc;;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;YACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,MAAA,IAAI,CAAC,gBAAgB,mCAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SAC1D;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;YAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAClF,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAE9B,OAAO,CAAC,GAAG,GAAG,EAAE;gBACd,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAEjC,CAAC,EAAE,CAAC;gBACJ,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;oBACrB,MAAM;iBACP;aACF;YAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAQD,eAAe;QACb,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;YACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC3B;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;YAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrF,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAA,cAAK,EAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IACpH,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;;AAtLH,gCAuLC;AAtLQ,yBAAc,GAAgC;IACnD,UAAU,EAAE,uCAAuC,GAAG,uDAAuD;IAC7G,UAAU,EAAE,QAAQ;IACpB,KAAK,EAAE,aAAa;IACpB,SAAS,EAAE,QAAQ;IACnB,WAAW,EAAE,EAAE;IACf,cAAc,EAAE,KAAK;IACrB,MAAM,EAAE,KAAK;IAEb,WAAW,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACzB,WAAW,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACxB,aAAa,EAAE,CAAC;IAChB,WAAW,EAAE,GAAG;IAEhB,MAAM,EAAE,KAAK;IAEb,KAAK,EAAE,QAAQ;IACf,eAAe,EAAE,CAAC;IAClB,eAAe,EAAE,CAAC;IAClB,eAAe,EAAE,MAAM;CACxB,CAAC", "file": "base.js", "sourcesContent": ["import { degreeToRadian, isFunction, isNil, merge } from '@visactor/vutils';\nimport type { IProgressiveTransformResult } from '@visactor/vgrammar-core';\nimport type { IBaseLayoutOptions, TagItemFunction, TagOutputItem } from './interface';\nimport { getShapeFunction } from './shapes';\nimport { functor, randomHslColor } from './util';\n\nexport abstract class BaseLayout<T extends IBaseLayoutOptions> implements IProgressiveTransformResult {\n  static defaultOptions: Partial<IBaseLayoutOptions> = {\n    fontFamily: '\"Trebuchet MS\", \"Heiti TC\", \"微軟正黑體\", ' + '\"Arial Unicode MS\", \"Droid Fallback Sans\", sans-serif',\n    fontWeight: 'normal',\n    color: 'random-dark',\n    fontStyle: 'normal',\n    minFontSize: 12, // 0 to disable\n    drawOutOfBound: false,\n    shrink: false,\n\n    minRotation: -Math.PI / 2,\n    maxRotation: Math.PI / 2,\n    rotationSteps: 0,\n    rotateRatio: 0.1,\n\n    random: false,\n\n    shape: 'circle',\n    progressiveTime: 0,\n    progressiveStep: 0,\n    backgroundColor: '#fff'\n  };\n\n  options: Partial<T>;\n\n  shape: (theta: number) => number;\n  getTextFontWeight: TagItemFunction<string>;\n  getTextFontSize: TagItemFunction<number>;\n  getTextFontFamily: TagItemFunction<string>;\n  getText: TagItemFunction<string | number>;\n  getTextColor: TagItemFunction<string>;\n  getTextFontStyle: TagItemFunction<string>;\n  getTextRotate: TagItemFunction<number>;\n  outputCallback: (res: any[]) => any[];\n\n  /* ================== runtime vars ================== */\n  escapeTime?: number;\n  result: TagOutputItem[];\n  data?: any[];\n  currentStepIndex?: number;\n  progressiveIndex?: number;\n  progressiveResult?: TagOutputItem[];\n\n  constructor(options: Partial<T>) {\n    this.options = merge({}, BaseLayout.defaultOptions, options);\n\n    if (!isFunction(this.options.shape)) {\n      this.shape = getShapeFunction(this.options.shape as string);\n    } else {\n      this.shape = this.options.shape;\n    }\n\n    /* function for getting the font-weight of the text */\n    this.getText = functor(this.options.text) ?? ((d: any) => d);\n    this.getTextFontWeight = functor(this.options.fontWeight);\n    this.getTextFontSize = functor(this.options.fontSize);\n    this.getTextFontStyle = functor(this.options.fontStyle);\n    this.getTextFontFamily = functor(this.options.fontFamily);\n    this.outputCallback = this.options.outputCallback ?? ((res: any[]) => res);\n\n    switch (this.options.color) {\n      case 'random-dark':\n        this.getTextColor = () => {\n          return randomHslColor(10, 50);\n        };\n        break;\n\n      case 'random-light':\n        this.getTextColor = () => {\n          return randomHslColor(50, 90);\n        };\n        break;\n\n      default:\n        this.getTextColor = functor(this.options.color);\n        break;\n    }\n\n    if (!isNil(this.options.rotate)) {\n      this.getTextRotate = isFunction(this.options.rotate)\n        ? (d: any) => degreeToRadian((this.options.rotate as (d: any) => number)(d) ?? 0)\n        : () => degreeToRadian((this.options.rotate as number) ?? 0);\n    } else if (this.options.useRandomRotate) {\n      const rotationRange = Math.abs(this.options.maxRotation - this.options.minRotation);\n      const rotationSteps = Math.abs(Math.floor(this.options.rotationSteps));\n      const minRotation = Math.min(this.options.maxRotation, this.options.minRotation);\n\n      this.getTextRotate = () => {\n        if (this.options.rotateRatio === 0) {\n          return 0;\n        }\n\n        if (Math.random() > this.options.rotateRatio) {\n          return 0;\n        }\n\n        if (rotationRange === 0) {\n          return minRotation;\n        }\n\n        if (rotationSteps > 0) {\n          // Min rotation + zero or more steps * span of one step\n          return minRotation + (Math.floor(Math.random() * rotationSteps) * rotationRange) / (rotationSteps - 1);\n        }\n        return minRotation + Math.random() * rotationRange;\n      };\n    } else {\n      this.getTextRotate = () => 0;\n    }\n  }\n\n  /* Return true if we had spent too much time */\n  exceedTime() {\n    if (this.options.progressiveStep > 0) {\n      return this.progressiveIndex >= ((this.currentStepIndex ?? 0) + 1) * this.options.progressiveStep;\n    }\n\n    return this.options.progressiveTime > 0 && new Date().getTime() - this.escapeTime > this.options.progressiveTime;\n  }\n\n  progressiveRun() {\n    if (this.options.progressiveStep > 0) {\n      this.currentStepIndex = (this.currentStepIndex ?? 0) + 1;\n    } else if (this.options.progressiveTime > 0) {\n      this.escapeTime = Date.now();\n    }\n\n    if (this.data && this.progressiveIndex && this.progressiveIndex < this.data.length) {\n      this.progressiveResult = [];\n      const len = this.data.length;\n      let i = this.progressiveIndex;\n\n      while (i < len) {\n        const drawn = this.layoutWord(i);\n\n        i++;\n        this.progressiveIndex = i;\n        if (this.exceedTime()) {\n          break;\n        }\n      }\n\n      return this.progressiveResult;\n    }\n\n    return this.result;\n  }\n\n  abstract layoutWord(i: number): boolean;\n  abstract layout(\n    data: any[],\n    config: { width: number; height: number; origin?: [number, number]; canvas?: HTMLCanvasElement }\n  ): any[];\n\n  initProgressive() {\n    this.progressiveIndex = -1;\n    if (this.options.progressiveStep > 0) {\n      this.currentStepIndex = 0;\n    } else if (this.options.progressiveTime > 0) {\n      this.escapeTime = Date.now();\n    }\n\n    this.progressiveResult = [];\n  }\n\n  output() {\n    return this.result ? this.outputCallback(this.result) : null;\n  }\n\n  progressiveOutput() {\n    return this.progressiveResult ? this.outputCallback(this.progressiveResult) : null;\n  }\n\n  unfinished() {\n    return this.data && this.data.length && !isNil(this.progressiveIndex) && this.progressiveIndex < this.data.length;\n  }\n\n  release() {\n    this.data = null;\n    this.result = null;\n    this.progressiveIndex = null;\n    this.progressiveResult = null;\n  }\n}\n"]}