{"version": 3, "sources": ["../src/interface.ts"], "names": [], "mappings": "", "file": "interface.js", "sourcesContent": ["export type TagItemAttribute<T> = T | ((d?: any) => T);\n\nexport type TagItemFunction<T> = (d?: any) => T;\n\nexport type Bounds = [{ x: number; y: number }, { x: number; y: number }];\nexport interface Rect {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n}\nexport type FieldOption = { field: string };\nexport type CallbackOption = (datum: any) => any;\nexport type AsType = {\n  x: string;\n  y: string;\n  fontFamily: string;\n  fontSize: string;\n  fontStyle: string;\n  fontWeight: string;\n  angle: string;\n  opacity: string;\n  visible: string;\n  isFillingWord: string;\n  color: string;\n};\nexport type SegmentationInputType = {\n  shapeUrl: string;\n  size: [number, number];\n  ratio: number;\n  tempCanvas?: HTMLCanvasElement | any;\n  tempCtx?: CanvasRenderingContext2D | null;\n  removeWhiteBorder: boolean;\n  boardSize: [number, number];\n  random: boolean;\n  randomGenerator?: any;\n};\nexport type ShapeConfigType = {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  scale: number;\n};\nexport type segmentationType = {\n  regions: any;\n  labels: number[];\n  labelNumber: number;\n};\nexport type ShapeBoundsType = {\n  x1: number;\n  x2: number;\n  y1: number;\n  y2: number;\n  width: number;\n  height: number;\n};\nexport interface SegmentationOutputType extends SegmentationInputType {\n  segmentation: segmentationType;\n  shapeConfig: ShapeConfigType;\n  shapeBounds: ShapeBoundsType;\n  shapeMaxR: number;\n  shapeRatio: number;\n  shapeCenter: number[];\n  shapeArea: number;\n\n  fillingInitialFontSize?: number;\n  fillingDeltaFontSize?: number;\n}\nexport type wordsConfigType = {\n  getText: TagItemFunction<string>;\n  getFontSize?: TagItemFunction<number>;\n  fontSizeRange?: [number, number];\n\n  colorMode: 'linear' | 'ordinal';\n  getColor: TagItemFunction<string>;\n  getFillingColor?: TagItemFunction<string>;\n  colorList?: string[];\n  getColorHex?: TagItemFunction<string>;\n\n  getFontFamily: TagItemFunction<string>;\n  rotateList: number[];\n  getPadding: TagItemFunction<number>;\n  getFontStyle: TagItemFunction<string>;\n  getFontWeight: TagItemFunction<string>;\n  getFontOpacity: TagItemFunction<number>;\n\n  fontSizeScale?: TagItemFunction<number>;\n  colorScale?: TagItemFunction<string>;\n  fillingColorScale?: TagItemFunction<string>;\n};\nexport type LayoutConfigType = {\n  size: [number, number];\n  ratio: number;\n\n  shapeUrl: string;\n  random: boolean;\n  textLayoutTimes: number;\n  removeWhiteBorder: boolean;\n  layoutMode: 'default' | 'ensureMapping' | 'ensureMappingEnlarge';\n  fontSizeShrinkFactor: number;\n  stepFactor: number;\n  importantWordCount: number;\n  globalShinkLimit: number;\n  fontSizeEnlargeFactor: number;\n\n  fillingRatio: number;\n  fillingTimes: number;\n  fillingXStep: number;\n  fillingYStep: number;\n  fillingInitialFontSize?: number;\n  fillingDeltaFontSize?: number;\n  fillingInitialOpacity: number;\n  fillingDeltaOpacity: number;\n\n  getFillingFontFamily: TagItemFunction<string>;\n  getFillingFontStyle: TagItemFunction<string>;\n  getFillingFontWeight: TagItemFunction<string>;\n  getFillingPadding: TagItemFunction<number>;\n  fillingRotateList: number[];\n  fillingDeltaFontSizeFactor: number;\n\n  // fill color 相关\n  fillingColorList: string[];\n\n  // 经过计算，补充的内容\n  sameColorList: boolean;\n  board?: number[];\n\n  minInitFontSize: number;\n  minFontSize: number;\n  minFillFoontSize: number;\n};\nexport type CloudWordType = {\n  x: number;\n  y: number;\n  weight: number;\n  text: string;\n  fontFamily: string;\n  fontWeight: string;\n  fontStyle: string;\n  rotate: number;\n  fontSize: number;\n  opacity: number;\n  padding: number;\n  color: string;\n  fillingColor: string;\n  datum: any;\n  visible: boolean;\n  hasPlaced: boolean;\n\n  wordSize?: [number, number];\n  bounds?: any;\n  hasText?: boolean;\n  sprite?: number[];\n  LT?: [number, number]; // 左上角点\n};\n"]}