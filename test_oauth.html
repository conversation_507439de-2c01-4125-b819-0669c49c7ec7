<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nodeloc OAuth 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 4px; }
        .url-box { width: 100%; height: 100px; margin-top: 10px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Nodeloc OAuth URL 测试工具</h1>
        
        <div class="form-group">
            <label for="clientId">Client ID:</label>
            <input type="text" id="clientId" placeholder="输入你的 Nodeloc Client ID">
        </div>
        
        <div class="form-group">
            <label for="domain">域名:</label>
            <input type="text" id="domain" value="https://yourdomain.com" placeholder="例如: https://yourdomain.com">
        </div>
        
        <div class="form-group">
            <label for="redirectPath">回调路径:</label>
            <input type="text" id="redirectPath" value="/oauth/nodeloc" placeholder="/oauth/nodeloc">
        </div>
        
        <button onclick="generateAndTestURL()">生成并测试 OAuth URL</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function generateAndTestURL() {
            const clientId = document.getElementById('clientId').value.trim();
            const domain = document.getElementById('domain').value.trim();
            const redirectPath = document.getElementById('redirectPath').value.trim();
            
            // 验证输入
            if (!clientId) {
                showResult('请输入 Client ID', 'error');
                return;
            }
            
            if (!domain) {
                showResult('请输入域名', 'error');
                return;
            }
            
            // 生成随机state
            const state = generateRandomState();
            
            // 构建redirect_uri
            const redirect_uri = domain + redirectPath;
            
            // 使用URLSearchParams确保正确编码
            const params = new URLSearchParams({
                'response_type': 'code',
                'client_id': clientId,
                'redirect_uri': redirect_uri,
                'scope': 'openid profile',
                'state': state
            });
            
            const oauthUrl = `https://conn.nodeloc.cc/oauth2/auth?${params.toString()}`;
            
            // 显示结果
            showResult(`
                <h3>生成的 OAuth URL:</h3>
                <textarea class="url-box" readonly>${oauthUrl}</textarea>
                
                <h3>参数详情:</h3>
                <ul>
                    <li><strong>response_type:</strong> code</li>
                    <li><strong>client_id:</strong> ${clientId}</li>
                    <li><strong>redirect_uri:</strong> ${redirect_uri}</li>
                    <li><strong>scope:</strong> openid profile</li>
                    <li><strong>state:</strong> ${state}</li>
                </ul>
                
                <h3>测试步骤:</h3>
                <ol>
                    <li>确保在 <a href="https://conn.nodeloc.cc/apps" target="_blank">Nodeloc 应用管理</a> 中设置的回调地址为: <code>${redirect_uri}</code></li>
                    <li>点击下面的链接测试OAuth流程</li>
                    <li>如果出现错误，请检查Client ID和回调地址是否正确</li>
                </ol>
                
                <p><a href="${oauthUrl}" target="_blank" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🚀 测试 OAuth 登录</a></p>
                
                <h3>常见错误排查:</h3>
                <ul>
                    <li><strong>invalid_request:</strong> 检查所有必需参数是否存在</li>
                    <li><strong>invalid_client:</strong> 检查 Client ID 是否正确</li>
                    <li><strong>invalid_redirect_uri:</strong> 检查回调地址是否与注册时一致</li>
                </ul>
            `, 'success');
        }
        
        function generateRandomState() {
            return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        }
        
        function showResult(content, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = content;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // 页面加载时自动填入当前域名
        window.onload = function() {
            const currentDomain = window.location.origin;
            document.getElementById('domain').value = currentDomain;
        };
    </script>
</body>
</html>
