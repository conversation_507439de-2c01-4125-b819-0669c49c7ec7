"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.LabelBase = void 0;

const vrender_core_1 = require("@visactor/vrender-core"), vutils_1 = require("@visactor/vutils"), base_1 = require("../core/base"), label_smartInvert_1 = require("../util/label-smartInvert"), util_1 = require("../util"), constant_1 = require("../constant"), overlap_1 = require("./overlap"), animate_1 = require("./animate/animate"), util_2 = require("./util"), constant_2 = require("../constant"), register_1 = require("./register");

(0, register_1.loadLabelComponent)();

class LabelBase extends base_1.AbstractComponent {
    setBitmap(bitmap) {
        this._bitmap = bitmap;
    }
    setBitmapTool(bmpTool) {
        this._bmpTool = bmpTool;
    }
    constructor(attributes, options) {
        super((null == options ? void 0 : options.skipDefault) ? attributes : (0, vutils_1.merge)({}, LabelBase.defaultAttributes, attributes)), 
        this.name = "label", this._onHover = e => {
            const target = e.target;
            target === this._lastHover || (0, vutils_1.isEmpty)(target.states) || (target.addState(constant_1.StateValue.hover, !0), 
            (0, util_1.traverseGroup)(this, (node => {
                node === target || (0, vutils_1.isEmpty)(node.states) || node.addState(constant_1.StateValue.hoverReverse, !0);
            })), this._lastHover = target);
        }, this._onUnHover = e => {
            this._lastHover && ((0, util_1.traverseGroup)(this, (node => {
                (0, vutils_1.isEmpty)(node.states) || (node.removeState(constant_1.StateValue.hoverReverse), 
                node.removeState(constant_1.StateValue.hover));
            })), this._lastHover = null);
        }, this._onClick = e => {
            const target = e.target;
            if (this._lastSelect === target && target.hasState("selected")) return this._lastSelect = null, 
            void (0, util_1.traverseGroup)(this, (node => {
                (0, vutils_1.isEmpty)(node.states) || (node.removeState(constant_1.StateValue.selectedReverse), 
                node.removeState(constant_1.StateValue.selected));
            }));
            (0, vutils_1.isEmpty)(target.states) || (target.addState(constant_1.StateValue.selected, !0), 
            (0, util_1.traverseGroup)(this, (node => {
                node === target || (0, vutils_1.isEmpty)(node.states) || node.addState(constant_1.StateValue.selectedReverse, !0);
            })), this._lastSelect = target);
        }, this._handleRelatedGraphicSetState = e => {
            var _a, _b, _c, _d, _e;
            if ((null === (_a = e.detail) || void 0 === _a ? void 0 : _a.type) === vrender_core_1.AttributeUpdateType.STATE || (null === (_b = e.detail) || void 0 === _b ? void 0 : _b.type) === vrender_core_1.AttributeUpdateType.ANIMATE_UPDATE && (null === (_c = e.detail.animationState) || void 0 === _c ? void 0 : _c.isFirstFrameOfStep)) {
                const currentStates = null !== (_e = null === (_d = e.target) || void 0 === _d ? void 0 : _d.currentStates) && void 0 !== _e ? _e : [];
                (this._isCollectionBase ? [ ...this._graphicToText.values() ] : [ this._graphicToText.get(e.target) ]).forEach((label => {
                    label && (label.text && label.text.useStates(currentStates), label.labelLine && label.labelLine.useStates(currentStates));
                }));
            }
        };
    }
    labeling(textBounds, graphicBounds, position, offset) {}
    _createLabelLine(text, baseMark) {
        const points = (0, util_2.connectLineBetweenBounds)(text.AABBBounds, null == baseMark ? void 0 : baseMark.AABBBounds);
        if (points) {
            const line = vrender_core_1.graphicCreator.line({
                points: points
            });
            return baseMark && baseMark.attribute.fill && line.setAttribute("stroke", baseMark.attribute.fill), 
            this.attribute.line && !(0, vutils_1.isEmpty)(this.attribute.line.style) && line.setAttributes(this.attribute.line.style), 
            this._setStatesOfLabelLine(line), line;
        }
    }
    render() {
        if (this._prepare(), (0, vutils_1.isNil)(this._idToGraphic) || this._isCollectionBase && (0, 
        vutils_1.isNil)(this._idToPoint)) return;
        const {overlap: overlap, smartInvert: smartInvert, dataFilter: dataFilter, customLayoutFunc: customLayoutFunc, customOverlapFunc: customOverlapFunc} = this.attribute;
        let labels, data = this.attribute.data;
        (0, vutils_1.isFunction)(dataFilter) && (data = dataFilter(data)), labels = (0, 
        vutils_1.isFunction)(customLayoutFunc) ? customLayoutFunc(data, this.getRelatedGraphic.bind(this), this._isCollectionBase ? d => this._idToPoint.get(d.id) : null) : this._layout(data), 
        (0, vutils_1.isFunction)(customOverlapFunc) ? labels = customOverlapFunc(labels, this.getRelatedGraphic.bind(this), this._isCollectionBase ? d => this._idToPoint.get(d.id) : null) : !1 !== overlap && (labels = this._overlapping(labels)), 
        labels && labels.length && labels.forEach((label => {
            this._bindEvent(label), this._setStatesOfText(label);
        })), !1 !== smartInvert && this._smartInvert(labels), this._renderLabels(labels);
    }
    _bindEvent(target) {
        if (this.attribute.disableTriggerEvent) return;
        if (!target) return;
        const {hover: hover, select: select} = this.attribute;
        hover && (target.addEventListener("pointermove", this._onHover), target.addEventListener("pointerout", this._onUnHover)), 
        select && target.addEventListener("pointerdown", this._onClick);
    }
    _setStatesOfText(target) {
        if (!target) return;
        const state = this.attribute.state;
        state && !(0, vutils_1.isEmpty)(state) && (target.states = state);
    }
    _setStatesOfLabelLine(target) {
        if (!target) return;
        const state = this.attribute.labelLineState;
        state && !(0, vutils_1.isEmpty)(state) && (target.states = state);
    }
    _createLabelText(attributes) {
        var _a, _b;
        if ("rich" === attributes.textType) {
            attributes.textConfig = attributes.text, attributes.width = null !== (_a = attributes.width) && void 0 !== _a ? _a : 0, 
            attributes.height = null !== (_b = attributes.height) && void 0 !== _b ? _b : 0;
            return vrender_core_1.graphicCreator.richtext(attributes);
        }
        if ("html" === attributes.textType) {
            attributes.textConfig = [], attributes.html = Object.assign(Object.assign({
                dom: attributes.text
            }, constant_2.DEFAULT_HTML_TEXT_SPEC), attributes);
            return vrender_core_1.graphicCreator.richtext(attributes);
        }
        return vrender_core_1.graphicCreator.text(attributes);
    }
    _prepare() {
        var _a, _b, _c, _d, _e;
        const currentBaseMarks = [];
        let baseMarks;
        if (baseMarks = (0, vutils_1.isFunction)(this.attribute.getBaseMarks) ? this.attribute.getBaseMarks() : (0, 
        util_1.getMarksByName)(this.getRootNode(), this.attribute.baseMarkGroupName), baseMarks.forEach((mark => {
            "willRelease" !== mark.releaseStatus && currentBaseMarks.push(mark);
        })), null === (_a = this._idToGraphic) || void 0 === _a || _a.clear(), null === (_b = this._idToPoint) || void 0 === _b || _b.clear(), 
        this._baseMarks = currentBaseMarks, this._isCollectionBase = "line-data" === this.attribute.type, 
        !currentBaseMarks || 0 === currentBaseMarks.length) return;
        const {data: data} = this.attribute;
        if (data && 0 !== data.length) {
            if (this._idToGraphic || (this._idToGraphic = new Map), this._isCollectionBase) {
                this._idToPoint || (this._idToPoint = new Map);
                let cur = 0;
                for (let i = 0; i < currentBaseMarks.length; i++) {
                    const baseMark = currentBaseMarks[i], points = (0, util_2.getPointsOfLineArea)(baseMark);
                    if (points && points.length) for (let j = 0; j < points.length; j++) {
                        const textData = data[cur];
                        textData && points[j] && ((0, vutils_1.isValid)(textData.id) || (textData.id = `vrender-component-${this.name}-${cur}`), 
                        this._idToPoint.set(textData.id, points[j]), this._idToGraphic.set(textData.id, baseMark)), 
                        cur++;
                    }
                }
            } else for (let i = 0; i < currentBaseMarks.length; i++) {
                const textData = data[i], baseMark = currentBaseMarks[i];
                textData && baseMark && ((0, vutils_1.isValid)(textData.id) || (textData.id = `vrender-component-${this.name}-${i}`), 
                this._idToGraphic.set(textData.id, baseMark));
            }
            if (!1 !== this.attribute.animation) {
                const animation = (0, vutils_1.isObject)(this.attribute.animation) ? this.attribute.animation : {};
                this._animationConfig = {
                    enter: (0, vutils_1.merge)({}, animate_1.DefaultLabelAnimation, animation, null !== (_c = this.attribute.animationEnter) && void 0 !== _c ? _c : {}),
                    exit: (0, vutils_1.merge)({}, animate_1.DefaultLabelAnimation, animation, null !== (_d = this.attribute.animationExit) && void 0 !== _d ? _d : {}),
                    update: (0, vutils_1.isArray)(this.attribute.animationUpdate) ? this.attribute.animationUpdate : (0, 
                    vutils_1.merge)({}, animate_1.DefaultLabelAnimation, animation, null !== (_e = this.attribute.animationUpdate) && void 0 !== _e ? _e : {})
                };
            }
        }
    }
    getRelatedGraphic(item) {
        return this._idToGraphic.get(item.id);
    }
    _layout(data = []) {
        const {textStyle: textStyle = {}, position: position, offset: offset} = this.attribute, labels = [];
        for (let i = 0; i < data.length; i++) {
            const textData = data[i], baseMark = this.getRelatedGraphic(textData);
            if (!baseMark) continue;
            const labelAttribute = Object.assign(Object.assign({
                fill: this._isCollectionBase ? (0, vutils_1.isArray)(baseMark.attribute.stroke) ? baseMark.attribute.stroke.find((entry => !!entry && !0 !== entry)) : baseMark.attribute.stroke : baseMark.attribute.fill
            }, textStyle), textData), text = this._createLabelText(labelAttribute), textBounds = this.getGraphicBounds(text), actualPosition = (0, 
            vutils_1.isFunction)(position) ? position(textData) : position, graphicBounds = this._isCollectionBase ? this.getGraphicBounds(null, this._idToPoint.get(textData.id), actualPosition) : this.getGraphicBounds(baseMark, {
                x: textData.x,
                y: textData.y
            }, actualPosition), textLocation = this.labeling(textBounds, graphicBounds, actualPosition, offset);
            textLocation && (labelAttribute.x = textLocation.x, labelAttribute.y = textLocation.y, 
            text.setAttributes(textLocation)), labels.push(text);
        }
        return labels;
    }
    _overlapping(labels) {
        var _a, _b, _c, _d;
        if (0 === labels.length) return [];
        const option = (0, vutils_1.isObject)(this.attribute.overlap) ? this.attribute.overlap : {}, result = [], baseMarkGroup = this.getBaseMarkGroup(), size = null !== (_a = option.size) && void 0 !== _a ? _a : {
            width: null !== (_b = null == baseMarkGroup ? void 0 : baseMarkGroup.AABBBounds.width()) && void 0 !== _b ? _b : 0,
            height: null !== (_c = null == baseMarkGroup ? void 0 : baseMarkGroup.AABBBounds.height()) && void 0 !== _c ? _c : 0
        };
        if (0 === size.width || 0 === size.height) return labels;
        const {avoidBaseMark: avoidBaseMark, strategy: strategy = [], hideOnHit: hideOnHit = !0, clampForce: clampForce = !0, avoidMarks: avoidMarks = [], overlapPadding: overlapPadding} = option, bmpTool = this._bmpTool || (0, 
        overlap_1.bitmapTool)(size.width, size.height), bitmap = this._bitmap || bmpTool.bitmap(), checkBounds = strategy.some((s => "bound" === s.type));
        avoidBaseMark && (null === (_d = this._baseMarks) || void 0 === _d || _d.forEach((mark => {
            mark.AABBBounds && bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, mark.AABBBounds, !0));
        }))), avoidMarks.length > 0 && avoidMarks.forEach((avoid => {
            (0, vutils_1.isString)(avoid) ? (0, util_1.getNoneGroupMarksByName)(this.getRootNode(), avoid).forEach((avoidMark => {
                avoidMark.AABBBounds && bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, avoidMark.AABBBounds, !0));
            })) : avoid.AABBBounds && bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, avoid.AABBBounds, !0));
        }));
        for (let i = 0; i < labels.length; i++) {
            if (!1 === labels[i].visible) continue;
            const text = labels[i], baseMark = this.getRelatedGraphic(text.attribute);
            if (text.update(), !(0, vutils_1.isRectIntersect)(baseMark.AABBBounds, {
                x1: 0,
                x2: bmpTool.width,
                y1: 0,
                y2: bmpTool.height
            }, !0)) continue;
            if ((0, overlap_1.canPlace)(bmpTool, bitmap, text.AABBBounds, clampForce, overlapPadding)) {
                if (!checkBounds) {
                    bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, text.AABBBounds, !0)), result.push(text);
                    continue;
                }
                if (checkBounds && baseMark && baseMark.AABBBounds && this._canPlaceInside(text.AABBBounds, baseMark.AABBBounds)) {
                    bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, text.AABBBounds, !0)), result.push(text);
                    continue;
                }
            }
            let hasPlace = !1;
            for (let j = 0; j < strategy.length; j++) if (hasPlace = (0, overlap_1.place)(bmpTool, bitmap, strategy[j], this.attribute, text, this._isCollectionBase ? this.getGraphicBounds(null, this._idToPoint.get(labels[i].attribute.id)) : this.getGraphicBounds(baseMark, labels[i].attribute), this.labeling), 
            !1 !== hasPlace) {
                text.setAttributes({
                    x: hasPlace.x,
                    y: hasPlace.y
                }), result.push(text);
                break;
            }
            if (!hasPlace && clampForce) {
                const {dx: dx = 0, dy: dy = 0} = (0, overlap_1.clampText)(text, bmpTool.width, bmpTool.height);
                if (0 === dx && 0 === dy) {
                    if ((0, overlap_1.canPlace)(bmpTool, bitmap, text.AABBBounds)) {
                        bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, text.AABBBounds, !0)), result.push(text);
                        continue;
                    }
                } else if ((0, overlap_1.canPlace)(bmpTool, bitmap, {
                    x1: text.AABBBounds.x1 + dx,
                    x2: text.AABBBounds.x2 + dx,
                    y1: text.AABBBounds.y1 + dy,
                    y2: text.AABBBounds.y2 + dy
                })) {
                    text.setAttributes({
                        x: text.attribute.x + dx,
                        y: text.attribute.y + dy
                    }), bitmap.setRange((0, overlap_1.boundToRange)(bmpTool, text.AABBBounds, !0)), 
                    result.push(text);
                    continue;
                }
            }
            !hasPlace && !hideOnHit && result.push(text);
        }
        return (0, vutils_1.isFunction)(this.onAfterLabelOverlap) && this.onAfterLabelOverlap(bitmap), 
        result;
    }
    getBaseMarkGroup() {
        const {baseMarkGroupName: baseMarkGroupName} = this.attribute;
        if (baseMarkGroupName) return this.getRootNode().find((node => node.name === baseMarkGroupName), !0);
    }
    getGraphicBounds(graphic, point = {}) {
        if (graphic) {
            if (!1 !== graphic.attribute.visible) return graphic.AABBBounds;
            const {x: x, y: y} = graphic.attribute;
            return {
                x1: x,
                x2: x,
                y1: y,
                y2: y
            };
        }
        const {x: x, y: y} = point;
        return {
            x1: x,
            x2: x,
            y1: y,
            y2: y
        };
    }
    _renderLabels(labels) {
        !1 === this._enableAnimation || !1 === this.attribute.animation ? this._renderWithOutAnimation(labels) : this._renderWithAnimation(labels);
    }
    _renderWithAnimation(labels) {
        var _a;
        const currentTextMap = new Map, prevTextMap = this._graphicToText || new Map, texts = [], labelLines = [], {visible: showLabelLine} = null !== (_a = this.attribute.line) && void 0 !== _a ? _a : {};
        labels.forEach(((text, index) => {
            const relatedGraphic = this.getRelatedGraphic(text.attribute), textId = text.attribute.id, textKey = this._isCollectionBase ? textId : relatedGraphic, state = (null == prevTextMap ? void 0 : prevTextMap.get(textKey)) ? "update" : "enter";
            let labelLine;
            if (showLabelLine && (labelLine = this._createLabelLine(text, relatedGraphic)), 
            "enter" === state) {
                if (texts.push(text), currentTextMap.set(textKey, labelLine ? {
                    text: text,
                    labelLine: labelLine
                } : {
                    text: text
                }), relatedGraphic) {
                    const {from: from, to: to} = (0, animate_1.getAnimationAttributes)(text.attribute, "fadeIn");
                    this.add(text), labelLine && (labelLines.push(labelLine), this.add(labelLine)), 
                    this._syncStateWithRelatedGraphic(relatedGraphic), this._animationConfig.enter.duration > 0 && relatedGraphic.once("animate-bind", (a => {
                        text.setAttributes(from), labelLine && labelLine.setAttributes(from);
                        const listener = this._afterRelatedGraphicAttributeUpdate(text, texts, labelLine, labelLines, index, relatedGraphic, to, this._animationConfig.enter);
                        relatedGraphic.on("afterAttributeUpdate", listener);
                    }));
                }
            } else if ("update" === state) {
                const prevLabel = prevTextMap.get(textKey);
                prevTextMap.delete(textKey), currentTextMap.set(textKey, prevLabel);
                const prevText = prevLabel.text, {duration: duration, easing: easing} = this._animationConfig.update;
                (0, animate_1.updateAnimation)(prevText, text, this._animationConfig.update), prevLabel.labelLine && labelLine && prevLabel.labelLine.animate().to(labelLine.attribute, duration, easing);
            }
        })), prevTextMap.forEach((label => {
            var _a;
            null === (_a = label.text) || void 0 === _a || _a.animate().to((0, animate_1.getAnimationAttributes)(label.text.attribute, "fadeOut").to, this._animationConfig.exit.duration, this._animationConfig.exit.easing).onEnd((() => {
                this.removeChild(label.text), label.labelLine && this.removeChild(label.labelLine);
            }));
        })), this._graphicToText = currentTextMap;
    }
    _renderWithOutAnimation(labels) {
        var _a;
        const currentTextMap = new Map, prevTextMap = this._graphicToText || new Map, texts = [], {visible: showLabelLine} = null !== (_a = this.attribute.line) && void 0 !== _a ? _a : {};
        labels.forEach((text => {
            const relatedGraphic = this.getRelatedGraphic(text.attribute), state = (null == prevTextMap ? void 0 : prevTextMap.get(relatedGraphic)) ? "update" : "enter", textKey = this._isCollectionBase ? text.attribute.id : relatedGraphic;
            let labelLine;
            if (showLabelLine && (labelLine = this._createLabelLine(text, relatedGraphic)), 
            "enter" === state) texts.push(text), currentTextMap.set(textKey, labelLine ? {
                text: text,
                labelLine: labelLine
            } : {
                text: text
            }), this.add(text), labelLine && this.add(labelLine), this._syncStateWithRelatedGraphic(relatedGraphic); else if ("update" === state) {
                const prevLabel = prevTextMap.get(textKey);
                prevTextMap.delete(textKey), currentTextMap.set(textKey, prevLabel), prevLabel.text.setAttributes(text.attribute), 
                prevLabel.labelLine && labelLine && prevLabel.labelLine.setAttributes(labelLine.attribute);
            }
        })), prevTextMap.forEach((label => {
            this.removeChild(label.text), label.labelLine && this.removeChild(label.labelLine);
        })), this._graphicToText = currentTextMap;
    }
    _syncStateWithRelatedGraphic(relatedGraphic) {
        this.attribute.syncState && relatedGraphic.on("afterAttributeUpdate", this._handleRelatedGraphicSetState);
    }
    _afterRelatedGraphicAttributeUpdate(text, texts, labelLine, labelLines, index, relatedGraphic, to, {mode: mode, duration: duration, easing: easing, delay: delay}) {
        const listener = event => {
            var _a, _b, _c;
            const {detail: detail} = event;
            if (!detail) return {};
            const step = null === (_a = detail.animationState) || void 0 === _a ? void 0 : _a.step;
            if (!(detail.type === vrender_core_1.AttributeUpdateType.ANIMATE_UPDATE && step && !("wait" === step.type && null == (null === (_b = step.prev) || void 0 === _b ? void 0 : _b.type)))) return {};
            if (detail.type === vrender_core_1.AttributeUpdateType.ANIMATE_END) return text.setAttributes(to), 
            void (labelLine && labelLine.setAttributes(to));
            const onStart = () => {
                relatedGraphic && (relatedGraphic.onAnimateBind = void 0, relatedGraphic.removeEventListener("afterAttributeUpdate", listener));
            };
            switch (mode) {
              case "after":
                detail.animationState.end && (text.animate({
                    onStart: onStart
                }).wait(delay).to(to, duration, easing), labelLine && labelLine.animate().wait(delay).to(to, duration, easing));
                break;

              case "after-all":
                index === texts.length - 1 && detail.animationState.end && (texts.forEach((t => {
                    t.animate({
                        onStart: onStart
                    }).wait(delay).to(to, duration, easing);
                })), labelLines.forEach((t => {
                    t.animate().wait(delay).to(to, duration, easing);
                })));
                break;

              default:
                if (this._isCollectionBase) {
                    const point = this._idToPoint.get(text.attribute.id);
                    !point || text.animates && text.animates.has("label-animate") || !relatedGraphic.containsPoint(point.x, point.y, vrender_core_1.IContainPointMode.LOCAL, null === (_c = this.stage) || void 0 === _c ? void 0 : _c.pickerService) || (text.animate({
                        onStart: onStart
                    }).wait(delay).to(to, duration, easing), labelLine && labelLine.animate().wait(delay).to(to, duration, easing));
                } else detail.animationState.isFirstFrameOfStep && (text.animate({
                    onStart: onStart
                }).wait(delay).to(to, duration, easing), labelLine && labelLine.animate().wait(delay).to(to, duration, easing));
            }
        };
        return listener;
    }
    _smartInvert(labels) {
        var _a, _b, _c, _d, _e;
        const option = (0, vutils_1.isObject)(this.attribute.smartInvert) ? this.attribute.smartInvert : {}, {textType: textType, contrastRatiosThreshold: contrastRatiosThreshold, alternativeColors: alternativeColors, mode: mode} = option, fillStrategy = null !== (_a = option.fillStrategy) && void 0 !== _a ? _a : "invertBase", strokeStrategy = null !== (_b = option.strokeStrategy) && void 0 !== _b ? _b : "base", brightColor = null !== (_c = option.brightColor) && void 0 !== _c ? _c : "#ffffff", darkColor = null !== (_d = option.darkColor) && void 0 !== _d ? _d : "#000000", outsideEnable = null !== (_e = option.outsideEnable) && void 0 !== _e && _e;
        if ("null" !== fillStrategy || "null" !== strokeStrategy) for (let i = 0; i < labels.length; i++) {
            const label = labels[i];
            if (!label) continue;
            const baseMark = this.getRelatedGraphic(label.attribute), backgroundColor = baseMark.attribute.fill, foregroundColor = label.attribute.fill, baseColor = backgroundColor, invertColor = (0, 
            label_smartInvert_1.labelSmartInvert)(foregroundColor, backgroundColor, textType, contrastRatiosThreshold, alternativeColors, mode), similarColor = (0, 
            label_smartInvert_1.contrastAccessibilityChecker)(invertColor, brightColor) ? brightColor : darkColor;
            if (outsideEnable) {
                const fill = (0, label_smartInvert_1.smartInvertStrategy)(fillStrategy, baseColor, invertColor, similarColor);
                if (fill && label.setAttributes({
                    fill: fill
                }), 0 === label.attribute.lineWidth) continue;
                const stroke = (0, label_smartInvert_1.smartInvertStrategy)(strokeStrategy, baseColor, invertColor, similarColor);
                stroke && label.setAttributes({
                    stroke: stroke
                });
            } else {
                if (this._canPlaceInside(label.AABBBounds, baseMark.AABBBounds)) {
                    const fill = (0, label_smartInvert_1.smartInvertStrategy)(fillStrategy, baseColor, invertColor, similarColor);
                    if (fill && label.setAttributes({
                        fill: fill
                    }), 0 === label.attribute.lineWidth) continue;
                    const stroke = (0, label_smartInvert_1.smartInvertStrategy)(strokeStrategy, baseColor, invertColor, similarColor);
                    stroke && label.setAttributes({
                        stroke: stroke
                    });
                } else {
                    if (0 === label.attribute.lineWidth) continue;
                    if (label.attribute.stroke) {
                        label.setAttributes({
                            fill: (0, label_smartInvert_1.labelSmartInvert)(label.attribute.fill, label.attribute.stroke, textType, contrastRatiosThreshold, alternativeColors, mode)
                        });
                        continue;
                    }
                    const fill = (0, label_smartInvert_1.smartInvertStrategy)(fillStrategy, baseColor, invertColor, similarColor);
                    fill && label.setAttributes({
                        fill: fill
                    });
                    const stroke = (0, label_smartInvert_1.smartInvertStrategy)(strokeStrategy, baseColor, invertColor, similarColor);
                    stroke && label.setAttributes({
                        stroke: stroke
                    });
                }
            }
        }
    }
    _canPlaceInside(textBound, shapeBound) {
        return !(!textBound || !shapeBound) && shapeBound.encloses(textBound);
    }
    setLocation(point) {
        this.translateTo(point.x, point.y);
    }
    disableAnimation() {
        this._enableAnimation = !1;
    }
    enableAnimation() {
        this._enableAnimation = !0;
    }
}

exports.LabelBase = LabelBase, LabelBase.defaultAttributes = {
    textStyle: {
        fontSize: 12,
        textAlign: "center",
        textBaseline: "middle",
        boundsPadding: [ -1, 0, -1, 0 ]
    },
    offset: 0,
    pickable: !1
};
//# sourceMappingURL=base.js.map