{"version": 3, "sources": ["../src/cloud-layout.ts"], "names": [], "mappings": "AAYA,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAEjD,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEnD,OAAO,EAAE,qBAAqB,EAAE,MAAM,UAAU,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAMjC,MAAM,oBAAoB,GAAG,KAAK,CAAC;AAgDnC,MAAM,OAAO,WAAY,SAAQ,UAA+B;IA+B9D,YAAY,OAA4B;;QACtC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QA1BxD,OAAE,GAAW,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC5B,OAAE,GAAW,CAAC,IAAI,EAAE,CAAC;QACrB,UAAK,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAErC,4BAAuB,GAAG,KAAK,CAAC;QAChC,iBAAY,GAAW,CAAC,CAAC;QACzB,WAAM,GAAY,IAAI,CAAC;QACvB,YAAO,GAAY,IAAI,CAAC;QACxB,QAAG,GAAW,CAAC,CAAC;QAUhB,aAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAUnB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,WAAW,CAAC,cAAc,CAAC,WAAW,EAAE;YACtE,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC;SACnE;QAED,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACzC,CAAC,CAAC,MAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAgB,CAAC,mCAAI,OAAO,CAAC,WAAW;YAC/D,CAAC,CAAE,IAAI,CAAC,OAAO,CAAC,MAAmE,CAAC;QACtF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,EAAE;QACA,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,UAAU,CAAC,KAAa;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAG3B,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,OAAO,IAAI,CAAC;SACb;QAGD,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChB,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAczE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE;YACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;aAC9B;iBAAM;gBACL,IAAI,CAAC,OAAO,GAAG;oBACb,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE;oBAChC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE;iBACjC,CAAC;aACH;YAED,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAG1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,OAAO,IAAI,CAAC;SACb;QAOD,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAE5C,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;gBAE3B,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC9C,IAAI,SAAS,IAAI,QAAQ,EAAE;oBAEzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACxE;qBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;oBAE5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACvE;qBAAM;oBAEL,OAAO,IAAI,CAAC;iBACb;aACF;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;gBAElC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC7C;iBAAM;gBAEL,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC7C;YAED,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAY,EAAE,MAAyC;QAC5D,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAG3C,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvF,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,IAAI,GAAG,KAAK;aACf,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;YACd,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBACrB,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACrC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACnC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACrC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAC5B,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;gBACnC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC/B,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,CAAC;gBACP,EAAE,EAAE,CAAC;gBACL,EAAE,EAAE,CAAC;gBACL,EAAE,EAAE,CAAC;gBACL,EAAE,EAAE,CAAC;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,CAAC;gBACR,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QACjC,CAAC,CAAC,CAAC;QAEL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,MAAM,qBAAqB,GAAG,CAAC,CAAC;QAChC,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,OAAO,CAAC,GAAG,CAAC,EAAE;YACZ,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEjC,IAAI,KAAK,IAAI,eAAe,IAAI,qBAAqB,EAAE;gBACrD,CAAC,EAAE,CAAC;gBACJ,eAAe,GAAG,CAAC,CAAC;aACrB;iBAAM;gBACL,eAAe,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM;aACP;SACF;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE;YAC9D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChC;QAGD,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAe,CAAC,EAAE;YACvG,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACnE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,aAAa,CAAC,KAAsB;QAElC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACxB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAExB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,CAAC;QACN,IAAI,CAAM,CAAC;QAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC1B,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,GAAG,EAAE,CAAC;YACP,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;YAClB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC;YAC7B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,GAAG,SAAS,CAAC;YACxC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;YAC5B,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,GAAG,SAAS,CAAC;YACpC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;YAC1B,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;YAC5B,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;YAElB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAChB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtH,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc;YACxB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACjE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjD,CAAC;IAEO,uBAAuB,CAAC,QAAgB;QAC9C,IAAI,CAAC,uBAAuB,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IAC7G,CAAC;IAGO,oBAAoB;QAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;IACpF,CAAC;IAGO,WAAW,CAAC,MAAc;QAChC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC9E,OAAO;SACR;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACpF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAQ,CAAC;IAC9D,CAAC;IAGO,WAAW,CAAC,KAAe,EAAE,MAAc,EAAE,MAAY;QAC/D,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACjF,IAAI,SAAS,GAAG,iBAAiB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE;YACvB,SAAS,EAAE,CAAC;SACb;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,IAAI,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,UAAU,EAAE,CAAC;SACd;QACD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE9C,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QAC/E,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC;SAC/B;IACH,CAAC;IAIO,kBAAkB,CAAC,KAAY,EAAE,KAAa,EAAE,MAAc;QACpE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,MAAM,GAAG,oBAAoB,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/F;QACD,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG,oBAAoB,EAAE,CAAC,EAAE,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC;IAEO,UAAU,CAAC,MAAW;QAE5B,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACjB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAClB,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;QAEpD,MAAM,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;QACtC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;QAChD,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE7B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,KAAe,EAAE,GAAY,EAAE,MAAc,EAAE,SAAiB;QAC5E,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5F,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;YACtB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,EAAE,GAAW,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QACrB,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1F,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC;QACT,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,IAAI,IAAI,CAAC;QAET,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;YAC5B,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACb,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAGb,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAE5C,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAS7B,IAAI,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;aAChB;iBAAM,IAAI,EAAE,GAAG,CAAC,EAAE;gBACjB,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;aACzB;YAGD,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE3B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE;gBACpD,MAAM;aACP;YAED,IAAI,MAAM,IAAI,SAAS,EAAE;gBACvB,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;oBACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;iBACnB;gBACD,SAAS;aACV;YAED,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAGlD,IAAI,GAAG,GAAG,CAAC;YACX,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;gBAGrB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;oBAEhC,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;wBACnC,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;4BACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;yBACnB;wBACD,SAAS;qBACV;yBAAM,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;wBAE1C,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;qBACvC;iBACF;qBAAM;oBACL,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;wBACnC,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;4BACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;yBACnB;wBACD,SAAS;qBACV;iBACF;aACF;iBAAM;gBACL,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;oBACnC,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;wBACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;wBAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;qBACnB;oBACD,SAAS;iBACV;aACF;YAGD,SAAS,GAAG,IAAI,CAAC;YAEjB,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;gBACzC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;oBAErD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;oBAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC9B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7B,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;oBACrB,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;oBACpB,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC5C,IAAI,IAAI,CAAC;oBACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1B,IAAI,GAAG,CAAC,CAAC;wBACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;4BAC3B,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;yBACjF;wBAED,CAAC,IAAI,EAAE,CAAC;qBACT;oBAGD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;oBAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBAInB,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QAID,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;SACvB;QACD,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACxD,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,UAAU;QAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAMO,QAAQ,CAAC,GAAY,EAAE,EAAU;QACvC,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;QACtC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC1B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;YACH,OAAO;SACR;QACD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE;YAC3D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC1B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;SACJ;IACH,CAAC;IASO,mBAAmB,CAAC,GAAY,EAAE,EAAU;QAClD,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,KAAK,CAAC;SACd;QACD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC;IACnC,CAAC;;AA1fM,0BAAc,GAAiC;IACpD,OAAO,EAAE,KAAK;IACd,WAAW,EAAE,CAAC;CACf,CAAC;AA+fJ,SAAS,WAAW,CAAC,eAAoB,EAAE,CAAU,EAAE,IAAe,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;IACxG,IAAI,CAAC,CAAC,MAAM,EAAE;QACZ,OAAO;KACR;IACD,MAAM,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC;IAClC,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;IAEpC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;IACjD,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,IAAI,CAAC,CAAC;IACN,IAAI,GAAG,CAAC;IACR,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,EAAE,EAAE,CAAC;IACL,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE;QACf,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC;QACtG,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9C,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,CAAC,KAAK,EAAE;YACX,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1E,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;SAC1D;aAAM;YACL,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,GAAG,IAAI,EAAE;YACZ,IAAI,GAAG,CAAC,CAAC;SACV;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACpB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,IAAI,IAAI,CAAC;YACV,IAAI,GAAG,CAAC,CAAC;SACV;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;YACf,MAAM;SACP;QACD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,CAAC,KAAK,EAAE;YACX,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACnB;QACD,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,OAAO,EAAE;YACb,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;YAC5B,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5B;QACD,CAAC,CAAC,OAAO,EAAE,CAAC;QAEZ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QACZ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;QACX,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;QAEX,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;QAEjB,CAAC,IAAI,CAAC,CAAC;KACR;IAED,MAAM,eAAe,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;IAC5E,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC;IACpC,MAAM,MAAM,GAAU,EAAE,CAAC;IACzB,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE;QAChB,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QACb,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;YACd,SAAS;SACV;QACD,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QACZ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACb,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QAEhB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACf;QACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,CAAC,IAAI,IAAI,EAAE;YACb,OAAO;SACR;QACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;QACjB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACtB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAGtB,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACf,IAAI,IAAI,CAAC,CAAC;aACX;YAED,IAAI,IAAI,EAAE;gBACR,OAAO,GAAG,CAAC,CAAC;aACb;iBAAM;gBAEL,CAAC,CAAC,EAAE,EAAE,CAAC;gBACP,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;aACL;SACF;QACD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC;QACtB,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;KACjD;AACH,CAAC;AAGD,SAAS,YAAY,CAAC,GAAY,EAAE,KAAe,EAAE,IAAsB;IACzE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACxB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC;IACzB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5B,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACrB,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAAC;IACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1B,IAAI,GAAG,CAAC,CAAC;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBACpF,OAAO,IAAI,CAAC;aACb;SACF;QACD,CAAC,IAAI,EAAE,CAAC;KACT;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,WAAW,CAAC,MAAc,EAAE,CAAU;IAC7C,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QACrB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;KACnB;IACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QACrB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;KACnB;IACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QACrB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;KACnB;IACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QACrB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;KACnB;AACH,CAAC;AAED,SAAS,YAAY,CAAC,CAAU,EAAE,CAAS;IACzC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClG,CAAC;AAED,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,IAAsB,EAAE,EAAE;IAC7D,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1G,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,IAAsB,EAAE,EAAE;IAC7D,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1G,CAAC,CAAC;AAEF,SAAS,YAAY,CAAC,GAAY,EAAE,IAAsB;IACxD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;IAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC;IACzB,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAEpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;aAO3C;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC7B;SACF;QACD,CAAC,IAAI,CAAC,CAAC;KACR;IAGD,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACnB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,EAAE,GAAG,EAAE,CAAC;IACd,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAGlB,uCACK,GAAG,KACN,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,EAC1B,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,EAC5B,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EACf,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EACf,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EACf,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EACf,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAE1B,MAAM,EAAE,OAAO,IACf;AACJ,CAAC", "file": "cloud-layout.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-wordcloud/src/CloudLayout.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\n/**\n * 小程序canvas相关API，getImageData、draw都是异步的，导致渐进渲染流程处理非常麻烦，实际上小程序并未使用这个算法，所以暂时不考虑支持小程序\n */\nimport { vglobal } from '@visactor/vrender-core';\nimport type { IProgressiveTransformResult } from '@visactor/vgrammar-core';\nimport { isString, merge } from '@visactor/vutils';\nimport type { Bounds, IBaseLayoutOptions, TagItemAttribute, TagItemFunction, TagOutputItem } from './interface';\nimport { getMaxRadiusAndCenter } from './shapes';\nimport { BaseLayout } from './base';\nimport { spirals } from './spirals';\nimport { functor } from './util';\n\n// Word cloud layout by <PERSON>, https://www.jasondavies.com/wordcloud/\n// Algorithm due to Jonathan Feinberg, http://static.mrfeinberg.com/bv_ch03.pdf\n\n// 一次扩充数组的数量\nconst MAX_ARGUMENTS_LENGTH = 60000;\n\nexport interface TagItem {\n  text: number | string;\n  /** font-family */\n  fontFamily?: string;\n  /** font-style */\n  fontStyle?: string;\n  /** font-weight */\n  fontWeight?: string;\n  /** 旋转角度 */\n  angle?: number;\n  /** font-size */\n  fontSize?: number;\n  padding?: number;\n  /** 偏移量，仅内部计算使用 */\n  xoff?: number;\n  /** 偏移量，仅内部计算使用 */\n  yoff?: number;\n  /** 中心点坐标 */\n  x?: number;\n  /** 中心点坐标 */\n  y?: number;\n  /** 右下角点坐标 */\n  x1?: number;\n  /** 右下角点坐标 */\n  y1?: number;\n  /** 左上角点坐标 */\n  x0?: number;\n  /** 左上角点坐标 */\n  y0?: number;\n  hasText?: boolean;\n  /** 像素是否有填充 */\n  sprite?: number[];\n  datum: any;\n  /** 旋转后，词语所占区域的宽度 */\n  width: number;\n  /** 旋转后，词语所占区域的高度 */\n  height: number;\n}\n\ninterface ICloudLayoutOptions extends IBaseLayoutOptions {\n  spiral?: 'archimedean' | 'rectangular' | ((size: [number, number]) => (t: number) => [number, number]);\n\n  padding?: TagItemAttribute<number>;\n  enlarge?: boolean;\n}\n\nexport class CloudLayout extends BaseLayout<ICloudLayoutOptions> implements IProgressiveTransformResult {\n  getTextPadding: TagItemFunction<number>;\n  spiral: (size: [number, number]) => (t: any) => [number, number];\n  random: () => number;\n\n  /* ==================== runtime vars ======================== */\n  cw: number = (1 << 11) >> 5;\n  ch: number = 1 << 11;\n  _size: [number, number] = [256, 256];\n  _originSize: [number, number];\n  _isBoardExpandCompleted = false;\n  _placeStatus: number = 0;\n  _tTemp?: number = null;\n  _dtTemp?: number = null;\n  _dy: number = 0;\n\n  contextAndRatio?: { context: CanvasRenderingContext2D; ratio: number; canvas: HTMLCanvasElement };\n  _board: number[];\n  /** 已经绘制文字的最小包围盒 */\n  _bounds: Bounds;\n\n  /**\n   * 最大无法放置字体缓存, key值为rotate + 摆放顺序(顺时针|逆时针)的组合\n   */\n  cacheMap = new Map();\n\n  static defaultOptions: Partial<ICloudLayoutOptions> = {\n    enlarge: false,\n    minFontSize: 2\n  };\n\n  constructor(options: ICloudLayoutOptions) {\n    super(merge({}, CloudLayout.defaultOptions, options));\n\n    if (this.options.minFontSize <= CloudLayout.defaultOptions.minFontSize) {\n      this.options.minFontSize = CloudLayout.defaultOptions.minFontSize;\n    }\n\n    this.spiral = isString(this.options.spiral)\n      ? spirals[this.options.spiral as string] ?? spirals.archimedean\n      : (this.options.spiral as (size: [number, number]) => (t: any) => [number, number]);\n    this.random = this.options.random ? Math.random : () => 1;\n    this.getTextPadding = functor(this.options.padding);\n  }\n\n  zoomRatio() {\n    return this._originSize[0] / this._size[0];\n  }\n\n  dy() {\n    return this._dy;\n  }\n\n  layoutWord(index: number) {\n    const d = this.data[index];\n\n    // 当 text 为空时，直接跳过对其的布局，否则会卡死\n    if (('' + d.text).trim() === '') {\n      return true;\n    }\n\n    // size可能会更新\n    const { maxRadius, center } = getMaxRadiusAndCenter(this.options.shape as string, this._size);\n    d.x = center[0];\n    d.y = center[1];\n    cloudSprite(this.contextAndRatio, d, this.data, index, this.cw, this.ch);\n    /* 一次place判断可能发生的情况：\n     * 1. 成功找到位置，更新board，返回true ==》 更新词语位置，完成布局\n     * 2. range和shape判断一直无法通过，直到delta大于max，返回false，等待扩大board范围再次尝试布局\n     * 3. 在螺旋的过程中collide检测一直无法找到合适位置，直到delta大于max，返回false，等待扩大board范围再次尝试布局\n     *\n     * 目标：\n     * 1. `超长词`语判断(d.fontSize > size)，返回false，size扩大到可以容纳d.fontSize\n     * 2. range和shape判断一直无法通过，直到delta大于max，返回false，等待扩大board范围再次尝试布局\n     * `长词`？记录可以容纳词语的最小fontSize（一次），扩大board范围再次尝试布局；目前测试数据只命中1,3，未发现命中2的情况\n     * 3. 在螺旋的过程中collide检测一直无法找到合适位置，直到delta大于max，返回false，等待扩大board范围再次尝试布局\n     * 从起点开始不断进行collide检测，第一次未通过range和shape判断时，记录当时的dt，扩大画布以后从该dt开始扫描\n     *\n     */\n    this._placeStatus = 0;\n    if (d.hasText && this.place(this._board, d, this._bounds, maxRadius)) {\n      this.result.push(d);\n      if (this._bounds) {\n        cloudBounds(this._bounds, d);\n      } else {\n        this._bounds = [\n          { x: d.x + d.x0, y: d.y + d.y0 },\n          { x: d.x + d.x1, y: d.y + d.y1 }\n        ];\n      }\n      // Temporary hack\n      d.x -= this._size[0] >> 1;\n      d.y -= this._size[1] >> 1;\n\n      // 清空t, dt缓存\n      this._tTemp = null; // 初始化t缓存\n      this._dtTemp = null; // 初始化dt缓存\n\n      return true;\n    }\n    // 扩大画布问题：\n    // 每次扩大画布，都是依据当前单词的d.fontSize和minFontSize比较后再扩大，\n    // 如果某个词绘制顺序比较靠前，且尺寸较大，就会在绘制时将board拉大，\n    // 后续尺寸较小的词语再画在borad后，其实际大小就会远远小于minFontSize，\n    // 是不是应该先遍历数据，找到最小的词语尺寸，按照minFontSize算出board能扩大的最大尺寸，\n    // 后面再绘制时board扩大不能超过这个尺寸。\n    this.updateBoardExpandStatus(d.fontSize);\n    if (d.hasText && this.shouldShrinkContinue()) {\n      // 不需要为hasText为false时扩大画布\n      if (this._placeStatus === 1) {\n        // 按照字体要求能扩大的最大尺寸\n        const maxSize0 = (d.fontSize * this._originSize[0]) / this.options.minFontSize;\n        const distSize0 = Math.max(d.width, d.height);\n        if (distSize0 <= maxSize0) {\n          // 扩大尺寸满足最小字体要求 =》 按照要求扩大board\n          this.expandBoard(this._board, this._bounds, distSize0 / this._size[0]);\n        } else if (this.options.clip) {\n          // 扩大尺寸不满足最小字体要求，但支持裁剪 =》 按最大尺寸扩大，裁剪词语\n          this.expandBoard(this._board, this._bounds, maxSize0 / this._size[0]);\n        } else {\n          // 扩大尺寸不满足最小字体要求，且不支持裁剪 =》 丢弃词语\n          return true;\n        }\n      } else if (this._placeStatus === 3) {\n        // 扩大画布\n        this.expandBoard(this._board, this._bounds);\n      } else {\n        // 扩大画布\n        this.expandBoard(this._board, this._bounds);\n      }\n      // 更新一次状态，下次大尺寸词语进入裁剪\n      this.updateBoardExpandStatus(d.fontSize);\n      return false;\n    }\n    this._tTemp = null; // 初始化t缓存\n    this._dtTemp = null; // 初始化dt缓存\n    return true;\n  }\n\n  layout(words: any[], config: { width: number; height: number }) {\n    this.initProgressive();\n    this.result = [];\n    this._size = [config.width, config.height];\n    // console.time('prepare data');\n    // 开始新的layout时清除旧的缓存\n    this.clearCache();\n    this._originSize = [...this._size];\n    const contextAndRatio = this.getContext(vglobal.createCanvas({ width: 1, height: 1 }));\n    this.contextAndRatio = contextAndRatio;\n    this._board = new Array((this._size[0] >> 5) * this._size[1]).fill(0);\n    // 已经绘制文字的最小包围盒\n    this._bounds = null;\n\n    const n = words.length;\n    let i = 0;\n\n    this.result = [];\n    const data = words\n      .map((d: any) => {\n        return {\n          text: this.getText(d),\n          fontFamily: this.getTextFontFamily(d),\n          fontStyle: this.getTextFontStyle(d),\n          fontWeight: this.getTextFontWeight(d),\n          angle: this.getTextRotate(d),\n          fontSize: ~~this.getTextFontSize(d),\n          padding: this.getTextPadding(d),\n          xoff: 0,\n          yoff: 0,\n          x1: 0,\n          y1: 0,\n          x0: 0,\n          y0: 0,\n          hasText: false,\n          sprite: null,\n          datum: d,\n          x: 0,\n          y: 0,\n          width: 0,\n          height: 0\n        };\n      })\n      .sort(function (a, b) {\n        return b.fontSize - a.fontSize;\n      });\n\n    this.data = data;\n    const maxSingleWordTryCount = 2;\n    let curWordTryCount = 0;\n\n    while (i < n) {\n      const drawn = this.layoutWord(i);\n\n      if (drawn || curWordTryCount >= maxSingleWordTryCount) {\n        i++;\n        curWordTryCount = 0;\n      } else {\n        curWordTryCount++;\n      }\n      this.progressiveIndex = i;\n      if (this.exceedTime()) {\n        break;\n      }\n    }\n\n    if (!this.options.clip && this.options.enlarge && this._bounds) {\n      this.shrinkBoard(this._bounds);\n    }\n\n    // 处理y方向偏移\n    if (this._bounds && ['cardioid', 'triangle', 'triangle-upright'].includes(this.options.shape as string)) {\n      const currentCenterY = (this._bounds[0].y + this._bounds[1].y) / 2;\n      this._dy = -(currentCenterY - this._size[1] / 2);\n    }\n\n    return this.result;\n  }\n\n  formatTagItem(words: TagOutputItem[]) {\n    /** 调整结果 */\n    const size = this._size;\n    const zoomRatio = this.zoomRatio();\n    const globalDy = this.dy();\n    const dx = size[0] >> 1;\n    const dy = size[1] >> 1;\n\n    const n = words.length;\n    const result = [];\n    let w;\n    let t: any;\n\n    for (let i = 0; i < n; ++i) {\n      w = words[i];\n      t = {};\n      t.datum = w.datum;\n      t.x = (w.x + dx) * zoomRatio;\n      t.y = (w.y + dy + globalDy) * zoomRatio;\n      t.fontFamily = w.fontFamily;\n      t.fontSize = w.fontSize * zoomRatio;\n      t.fontStyle = w.fontStyle;\n      t.fontWeight = w.fontWeight;\n      t.angle = w.angle;\n\n      result.push(t);\n    }\n\n    return result;\n  }\n\n  output() {\n    return this.outputCallback ? this.outputCallback(this.formatTagItem(this.result)) : this.formatTagItem(this.result);\n  }\n\n  progressiveOutput() {\n    return this.outputCallback\n      ? this.outputCallback(this.formatTagItem(this.progressiveResult))\n      : this.formatTagItem(this.progressiveResult);\n  }\n  // 词语尺寸是否达小于最小尺寸，true时不能继续扩大画布“\n  private updateBoardExpandStatus(fontSize: number) {\n    this._isBoardExpandCompleted = fontSize * (this._originSize[0] / this._size[0]) < this.options.minFontSize;\n  }\n\n  // 是否可以继续扩大画布，true可以继续扩大\n  private shouldShrinkContinue() {\n    return !this.options.clip && this.options.shrink && !this._isBoardExpandCompleted;\n  }\n\n  // 根据 bounds 最大比例缩小 size\n  private shrinkBoard(bounds: Bounds) {\n    const leftTopPoint = bounds[0];\n    const rightBottomPoint = bounds[1];\n    if (rightBottomPoint.x >= this._size[0] || rightBottomPoint.y >= this._size[1]) {\n      return;\n    }\n    const minXValue = Math.min(leftTopPoint.x, this._size[0] - rightBottomPoint.x);\n    const minYValue = Math.min(leftTopPoint.y, this._size[1] - rightBottomPoint.y);\n    const minRatio = Math.min(minXValue / this._size[0], minYValue / this._size[1]) * 2;\n    this._size = this._size.map(v => v * (1 - minRatio)) as any;\n  }\n\n  // 扩充 bitmap\n  private expandBoard(board: number[], bounds: Bounds, factor?: any) {\n    const expandedLeftWidth = (this._size[0] * (factor || 1.1) - this._size[0]) >> 5;\n    let diffWidth = expandedLeftWidth * 2 > 2 ? expandedLeftWidth : 2;\n    if (diffWidth % 2 !== 0) {\n      diffWidth++;\n    }\n    let diffHeight = Math.ceil((this._size[1] * (diffWidth << 5)) / this._size[0]);\n    if (diffHeight % 2 !== 0) {\n      diffHeight++;\n    }\n    const w = this._size[0];\n    const h = this._size[1];\n    const widthArr = new Array(diffWidth).fill(0);\n\n    const heightArr = new Array((diffHeight / 2) * (diffWidth + (w >> 5))).fill(0);\n    this.insertZerosToArray(board, h * (w >> 5), heightArr.length + diffWidth / 2);\n    for (let i = h - 1; i > 0; i--) {\n      this.insertZerosToArray(board, i * (w >> 5), widthArr.length);\n    }\n    this.insertZerosToArray(board, 0, heightArr.length + diffWidth / 2);\n    this._size = [w + (diffWidth << 5), h + diffHeight];\n    if (bounds) {\n      bounds[0].x += (diffWidth << 5) / 2;\n      bounds[0].y += diffHeight / 2;\n      bounds[1].x += (diffWidth << 5) / 2;\n      bounds[1].y += diffHeight / 2;\n    }\n  }\n\n  // 分组扩充填充数组, 一次填充超过大概126000+会报stack overflow，worker环境下大概6w,这边取个比较小的\n  // https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why\n  private insertZerosToArray(array: any[], index: number, length: number) {\n    const len = Math.floor(length / MAX_ARGUMENTS_LENGTH);\n    const restLen = length % MAX_ARGUMENTS_LENGTH;\n\n    for (let i = 0; i < len; i++) {\n      array.splice(index + i * MAX_ARGUMENTS_LENGTH, 0, ...new Array(MAX_ARGUMENTS_LENGTH).fill(0));\n    }\n    array.splice(index + len * MAX_ARGUMENTS_LENGTH, 0, ...new Array(restLen).fill(0));\n  }\n\n  private getContext(canvas: any) {\n    // 缩放比例\n    canvas.width = 1;\n    canvas.height = 1;\n    const tempContext = canvas.getContext('2d');\n    const imageData = tempContext.getImageData(0, 0, 1, 1);\n    const ratio = Math.sqrt(imageData.data.length >> 2);\n\n    canvas.width = (this.cw << 5) / ratio;\n    canvas.height = this.ch / ratio;\n    const context = canvas.getContext('2d');\n    context.fillStyle = context.strokeStyle = 'red';\n    context.textAlign = 'center';\n\n    return { context: context, ratio: ratio, canvas };\n  }\n\n  private place(board: number[], tag: TagItem, bounds: Bounds, maxRadius: number) {\n    let isCollide = false;\n    // 情况1，超长词语\n    if (this.shouldShrinkContinue() && (tag.width > this._size[0] || tag.height > this._size[1])) {\n      this._placeStatus = 1;\n      return false;\n    }\n\n    const dt: number = this.random() < 0.5 ? 1 : -1;\n    // 根据缓存判断是否有放置空间\n    if (!this.shouldShrinkContinue() && this.isSizeLargerThanMax(tag, dt)) {\n      return null;\n    }\n    const startX = tag.x;\n    const startY = tag.y;\n    const maxDelta = Math.sqrt(this._size[0] * this._size[0] + this._size[1] * this._size[1]);\n    const s = this.spiral(this._size);\n    let t: number = -dt;\n    let dxdy;\n    let dx;\n    let dy;\n    let _tag;\n\n    this._tTemp = null; // 初始化t缓存\n    this._dtTemp = null; // 初始化dt缓存\n    while ((dxdy = s((t += dt)))) {\n      dx = dxdy[0];\n      dy = dxdy[1];\n\n      // 半径\n      const radius = Math.sqrt(dx ** 2 + dy ** 2);\n      // 弧度\n      let rad = Math.atan(dy / dx);\n      /*\n       * 弧度从x轴正方向开始，逆时针方向为正，范围[0, 2PI]\n       * atan返回值∈[-1/2PI, 1/2PI]，可以正确描述一四象限\n       * 第一象限atan为正，弧度正确\n       * 第二象限atan为负，等同于第四象限，弧度需要+PI矫正到第二象限\n       * 第三象限atan为正，等同于第一象限，弧度需要+PI矫正到第三象限\n       * 第四象限atan为负，需要矫正到正值，因此弧度需要+2PI\n       */\n      if (dx < 0) {\n        rad += Math.PI;\n      } else if (dy < 0) {\n        rad = 2 * Math.PI + rad;\n      }\n\n      // 半径更改比例[0, 1]\n      const rx = this.shape(rad);\n\n      if (Math.min(Math.abs(dx), Math.abs(dy)) >= maxDelta) {\n        break;\n      } // (dx, dy)距离中心超过maxDelta，跳出螺旋返回false\n\n      if (radius >= maxRadius) {\n        if (isCollide && this._tTemp === null) {\n          this._tTemp = t;\n          this._dtTemp = dt;\n        }\n        continue; // 判断是否在指定形状内\n      }\n\n      tag.x = startX + ~~(radius * rx * Math.cos(-rad));\n      tag.y = startY + ~~(radius * rx * Math.sin(-rad));\n\n      // 超出画布范围\n      _tag = tag;\n      if (this.options.clip) {\n        // 通过剪裁文字，让文字能够正常渲染\n\n        if (!this.shouldShrinkContinue()) {\n          // 当文字全部在外面时\n          if (isFullOutside(_tag, this._size)) {\n            if (isCollide && this._tTemp === null) {\n              this._tTemp = t;\n              this._dtTemp = dt;\n            }\n            continue;\n          } else if (isPartOutside(_tag, this._size)) {\n            // 部分在外面\n            _tag = clipInnerTag(_tag, this._size);\n          }\n        } else {\n          if (isPartOutside(_tag, this._size)) {\n            if (isCollide && this._tTemp === null) {\n              this._tTemp = t;\n              this._dtTemp = dt;\n            }\n            continue;\n          }\n        }\n      } else {\n        if (isPartOutside(_tag, this._size)) {\n          if (isCollide && this._tTemp === null) {\n            this._tTemp = t;\n            this._dtTemp = dt;\n          }\n          continue;\n        }\n      }\n\n      // 进入collide检测\n      isCollide = true;\n      // TODO only check for collisions within current bounds.\n      if (!bounds || collideRects(_tag, bounds)) {\n        if (!bounds || !cloudCollide(_tag, board, this._size)) {\n          // 合并文字占用部分到board\n          const sprite = _tag.sprite;\n          const w = _tag.width >> 5;\n          const sw = this._size[0] >> 5;\n          const lx = _tag.x - (w << 4);\n          const sx = lx & 0x7f;\n          const msx = 32 - sx;\n          const h = _tag.y1 - _tag.y0;\n          let x = (_tag.y + _tag.y0) * sw + (lx >> 5);\n          let last;\n          for (let j = 0; j < h; j++) {\n            last = 0;\n            for (let i = 0; i <= w; i++) {\n              board[x + i] |= (last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0);\n            }\n            // paint(board, size, originSize)\n            x += sw;\n          }\n          // paint(_tag.sprite, [_tag.width, _tag.height])\n          // paint(board, size, originSize)\n          tag.sprite = null;\n          _tag.sprite = null;\n          // if (Date.now() - start > 10) {\n          //   console.log(_tag.text, Date.now() - start, placeCount)\n          // }\n          return true;\n        }\n      }\n    }\n    // if (Date.now() - start > 50) {\n    //   console.log(_tag.text, Date.now() - start, placeCount)\n    // }\n    if (this._tTemp !== null) {\n      this._placeStatus = 3;\n    }\n    !this.shouldShrinkContinue() && this.setCache(_tag, dt);\n    return false;\n  }\n\n  /**\n   * 清除缓存\n   */\n  private clearCache() {\n    this.cacheMap.clear();\n  }\n  /**\n   * 设置缓存\n   * @param {*} tag\n   * @param {number} dt 旋转方向, 1, -1\n   */\n  private setCache(tag: TagItem, dt: number) {\n    const cacheKey = `${tag.angle}-${dt}`;\n    const w = tag.x1 - tag.x0;\n    const h = tag.y1 - tag.y0;\n    if (!this.cacheMap.has(cacheKey)) {\n      this.cacheMap.set(cacheKey, {\n        width: w,\n        height: h\n      });\n      return;\n    }\n    const { width, height } = this.cacheMap.get(cacheKey);\n    if ((w < width && h < height) || (w <= width && h < height)) {\n      this.cacheMap.set(cacheKey, {\n        width: w,\n        height: h\n      });\n    }\n  }\n\n  /**\n   * 判断当前text是否能放置\n   * 如果缓存中有同旋转角度和旋转方向的text,\n   * 且当前text的boundingBox大于缓存boundingBox, 则跳过放置尝试\n   * @param {*} tag\n   * @param {*} dt 旋转方向, 1, -1\n   */\n  private isSizeLargerThanMax(tag: TagItem, dt: number) {\n    const cacheKey = `${tag.angle}-${dt}`;\n    if (!this.cacheMap.has(cacheKey)) {\n      return false;\n    }\n    const { width, height } = this.cacheMap.get(cacheKey);\n    const w = tag.x1 - tag.x0;\n    const h = tag.y1 - tag.y0;\n    return w >= width && h >= height;\n  }\n}\n\n// Fetches a monochrome sprite bitmap for the specified text.\n// Load in batches for speed.\n// cloudSprite从索引di开始向后绘制词语sprite，直到可以一次绘制的最大范围；\n// 如果索引di在它自己的轮次都无法绘制（hasText = true），那么它就是超大词语，无法在屏幕出现\n// 此时，不需要为他扩大画布\nfunction cloudSprite(contextAndRatio: any, d: TagItem, data: TagItem[], di: number, cw: number, ch: number) {\n  if (d.sprite) {\n    return;\n  }\n  const c = contextAndRatio.context;\n  const ratio = contextAndRatio.ratio;\n  // 设置transform\n  c.setTransform(ratio, 0, 0, ratio, 0, 0);\n  c.clearRect(0, 0, (cw << 5) / ratio, ch / ratio);\n  let x = 0;\n  let y = 0;\n  let maxh = 0;\n  const n = data.length;\n  let w;\n  let w32;\n  let h;\n  let i;\n  let j;\n  --di;\n  while (++di < n) {\n    d = data[di];\n    c.save();\n    c.font = d.fontStyle + ' ' + d.fontWeight + ' ' + ~~((d.fontSize + 1) / ratio) + 'px ' + d.fontFamily;\n    w = c.measureText(d.text + 'm').width * ratio;\n    h = d.fontSize << 1;\n    if (d.angle) {\n      const sr = Math.sin(d.angle);\n      const cr = Math.cos(d.angle);\n      const wcr = w * cr;\n      const wsr = w * sr;\n      const hcr = h * cr;\n      const hsr = h * sr;\n      w = ((Math.max(Math.abs(wcr + hsr), Math.abs(wcr - hsr)) + 31) >> 5) << 5;\n      h = ~~Math.max(Math.abs(wsr + hcr), Math.abs(wsr - hcr));\n    } else {\n      w = ((w + 31) >> 5) << 5;\n    }\n    // w, h为旋转后，词语所占区域的宽高\n    if (h > maxh) {\n      maxh = h;\n    } // 记录当前行最大高度\n    // 如果当前行放不下，就另起一行，y方向向下移动当前行的最大高度\n    if (x + w >= cw << 5) {\n      x = 0;\n      y += maxh;\n      maxh = 0;\n    }\n\n    if (y + h >= ch) {\n      break;\n    } // 绘制区域的高度为2048px，超过长度下次绘制（TODO: 如果存在超高词语，这里是否可以当做一个退出机制？）\n    c.translate((x + (w >> 1)) / ratio, (y + (h >> 1)) / ratio);\n    if (d.angle) {\n      c.rotate(d.angle);\n    }\n    c.fillText(d.text, 0, 0);\n    if (d.padding) {\n      c.lineWidth = 2 * d.padding;\n      c.strokeText(d.text, 0, 0);\n    }\n    c.restore();\n    // 词语绘制完成，记录其在画布上的相对位置和范围\n    d.width = w;\n    d.height = h;\n    d.xoff = x;\n    d.yoff = y;\n    // x0, x1, y0, y1是四角相对于中心点的相对坐标\n    d.x1 = w >> 1;\n    d.y1 = h >> 1;\n    d.x0 = -d.x1;\n    d.y0 = -d.y1;\n    d.hasText = true;\n    // x位置右移，等待下一个词语绘制\n    x += w;\n  }\n\n  const pixelsImageData = c.getImageData(0, 0, (cw << 5) / ratio, ch / ratio);\n  const pixels = pixelsImageData.data;\n  const sprite: any[] = [];\n  while (--di >= 0) {\n    d = data[di];\n    if (!d.hasText) {\n      continue;\n    }\n    w = d.width;\n    w32 = w >> 5;\n    h = d.y1 - d.y0;\n    // Zero the buffer\n    for (i = 0; i < h * w32; i++) {\n      sprite[i] = 0;\n    }\n    x = d.xoff;\n    if (x == null) {\n      return;\n    }\n    y = d.yoff;\n    let seen = 0;\n    let seenRow = -1;\n    for (j = 0; j < h; j++) {\n      for (i = 0; i < w; i++) {\n        // 在sprite数组中，每一个Uint32的数字记录了32个像素的绘制情况\n        // 在pixels中，只取alpha通道的值，因此需要每个像素需要 << 2 得到alpha通道\n        const k = w32 * j + (i >> 5);\n        const m = pixels[((y + j) * (cw << 5) + (x + i)) << 2] ? 1 << (31 - (i % 32)) : 0;\n        sprite[k] |= m;\n        seen |= m;\n      }\n      // 如果当前行发现着色，开始记录行号\n      if (seen) {\n        seenRow = j;\n      } else {\n        // 如果当前行未发现着色，则在结果中省去改行（高度--，y坐标++，左上角相对坐标++）\n        d.y0++;\n        h--;\n        j--;\n        y++;\n      }\n    }\n    d.y1 = d.y0 + seenRow; // 更新右下角相对坐标\n    d.sprite = sprite.slice(0, (d.y1 - d.y0) * w32); // 舍弃数组中冗余部分\n  }\n}\n\n// Use mask-based collision detection.\nfunction cloudCollide(tag: TagItem, board: number[], size: [number, number]) {\n  const sw = size[0] >> 5;\n  const sprite = tag.sprite;\n  const w = tag.width >> 5;\n  const lx = tag.x - (w << 4);\n  const sx = lx & 0x7f;\n  const msx = 32 - sx;\n  const h = tag.y1 - tag.y0;\n  let x = (tag.y + tag.y0) * sw + (lx >> 5);\n  let last;\n  for (let j = 0; j < h; j++) {\n    last = 0;\n    for (let i = 0; i <= w; i++) {\n      if (((last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0)) & board[x + i]) {\n        return true;\n      }\n    }\n    x += sw;\n  }\n  return false;\n}\n\nfunction cloudBounds(bounds: Bounds, d: TagItem) {\n  const b0 = bounds[0];\n  const b1 = bounds[1];\n  if (d.x + d.x0 < b0.x) {\n    b0.x = d.x + d.x0;\n  }\n  if (d.y + d.y0 < b0.y) {\n    b0.y = d.y + d.y0;\n  }\n  if (d.x + d.x1 > b1.x) {\n    b1.x = d.x + d.x1;\n  }\n  if (d.y + d.y1 > b1.y) {\n    b1.y = d.y + d.y1;\n  }\n}\n\nfunction collideRects(a: TagItem, b: Bounds) {\n  return a.x + a.x1 > b[0].x && a.x + a.x0 < b[1].x && a.y + a.y1 > b[0].y && a.y + a.y0 < b[1].y;\n}\n\nconst isFullOutside = (tag: TagItem, size: [number, number]) => {\n  return tag.x + tag.x0 > size[0] || tag.y + tag.y0 > size[0] || tag.x + tag.x1 < 0 || tag.y + tag.y1 < 0;\n};\n\nconst isPartOutside = (tag: TagItem, size: [number, number]) => {\n  return tag.x + tag.x0 < 0 || tag.y + tag.y0 < 0 || tag.x + tag.x1 > size[0] || tag.y + tag.y1 > size[1];\n};\n\nfunction clipInnerTag(tag: TagItem, size: [number, number]) {\n  const sprite = tag.sprite;\n  const h = tag.y1 - tag.y0;\n  const w = tag.width >> 5;\n  let x = 0;\n\n  const _sprite: number[] = [];\n  const js = Math.max(-(tag.y0 + tag.y), 0);\n  const je = Math.min(h + (size[1] - (tag.y1 + tag.y)), h);\n  const is = Math.max(-(tag.x0 + tag.x), 0) >> 5;\n  const ie = Math.min(w + ((size[0] - (tag.x1 + tag.x)) >> 5) + 1, w);\n\n  for (let j = 0; j < h; j++) {\n    for (let i = 0; i < w; i++) {\n      if (j < js || je <= j || i < is || ie <= i) {\n        // sprite[x + i] = null\n        // if (ie === i) {\n        //   var value = sprite[x + i]\n        //   var overflow = (ie << 5) - (tag.x1 + tag.x)\n        //   _sprite.push((value >> overflow << overflow))\n        // }\n      } else {\n        _sprite.push(sprite[x + i]);\n      }\n    }\n    x += w;\n  }\n  // paint(sprite, [tag.width, tag.height])\n  // var _sprite = sprite.filter(d => d !== null)\n  const xl = is << 5;\n  const xr = (w - ie) << 5;\n  const yb = js;\n  const yt = h - je;\n  // paint(_sprite, [tag.width - xl - xr, tag.height - yb - yt])\n\n  return {\n    ...tag,\n    width: tag.width - xl - xr,\n    height: tag.height - yb - yt,\n    x0: tag.x0 + xl,\n    x1: tag.x1 - xr,\n    y0: tag.y0 + yb,\n    y1: tag.y1 - yt,\n    x: tag.x + xl / 2 - xr / 2,\n    // y: tag.y + yb / 2 - yt / 2,\n    sprite: _sprite\n  };\n}\n"]}