{"version": 3, "sources": ["../src/wordcloud.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAC7C,6CAAoF;AAEpF,+CAA2C;AAC3C,+CAA2C;AAE3C,MAAM,MAAM,GAAG;IACb,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,UAAU,EAAE,YAAY;IACxB,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,YAAY;IACxB,KAAK,EAAE,OAAO;CACf,CAAC;AAeK,MAAM,SAAS,GAAG,CACvB,OAuBC,EACD,YAAmB,EACnB,EAAE;;IACF,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;QAClE,MAAM,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAEhE,OAAO,EAAE,CAAC;KACX;IAGD,MAAM,IAAI,GAAG,YAAY,CAAC;IAC1B,MAAM,UAAU,GAAG,CAAC,MAAA,OAAO,CAAC,IAAI,mCAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,EAAsB,CAAC;IAE5E,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IACjF,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC1E,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC7E,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAM,IAAI,GAAG,KAAK,CAAkB,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,aAAa,CAAC;IAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,MAAM,KAAK,GAAG,MAAA,OAAO,CAAC,KAAK,mCAAI,QAAQ,CAAC;IACxC,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,KAAK,CAAC;IACvC,MAAM,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,KAAK,CAAC;IACzC,MAAM,IAAI,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,KAAK,CAAC;IACnC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IACxC,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;IAC5C,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,MAAM,CAAC;IAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAG9C,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;IAE5C,IAAI,aAAa,IAAI,CAAC,IAAA,iBAAQ,EAAC,QAAQ,CAAC,EAAE;QACxC,MAAM,KAAK,GAAQ,QAAQ,CAAC;QAC5B,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAyB,CAAC,CAAC;QAE5F,QAAQ,GAAG,KAAK,CAAC,EAAE;YACjB,OAAO,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;KACH;IAED,IAAI,MAAM,GAAQ,0BAAW,CAAC;IAE9B,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;QACjC,MAAM,GAAG,wBAAU,CAAC;KACrB;SAAM,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;QACxC,MAAM,GAAG,wBAAU,CAAC;KACrB;IAGD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;QACxB,IAAI;QACJ,OAAO;QACP,MAAM;QACN,KAAK;QACL,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,OAAO;QACP,WAAW;QACX,MAAM,EAAE,aAAa;QACrB,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE;YAC/B,MAAM,GAAG,GAAU,EAAE,CAAC;YACtB,IAAI,CAAM,CAAC;YACX,IAAI,CAAgB,CAAC;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAChD,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;gBACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;gBAChC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;gBAC5B,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;gBAC9B,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;gBAChC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;gBAEtB,IAAI,cAAc,KAAK,yBAAyB,EAAE;oBAChD,uBAAuB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;iBACzD;gBAED,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACb;YACD,OAAO,GAAG,CAAC;QACb,CAAC;KACF,CAAC,CAAC;IAEH,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;QAClB,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;QACpB,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;KACtB,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE;QAC9D,OAAO;YACL,WAAW,EAAE,MAAM;SACpB,CAAC;KACH;IAED,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;AACzB,CAAC,CAAC;AAtIW,QAAA,SAAS,aAsIpB;AAGF,MAAM,KAAK,GAAG,CAAI,MAAyC,EAAE,EAAE;IAC7D,IAAI,IAAA,iBAAQ,EAAC,MAAM,CAAC,IAAI,IAAA,iBAAQ,EAAC,MAAM,CAAC,IAAI,IAAA,mBAAU,EAAC,MAAM,CAAC,EAAE;QAC9D,OAAO,MAA6B,CAAC;KACtC;IACD,OAAO,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAE,MAAsB,CAAC,KAAK,CAAM,CAAC;AACnE,CAAC,CAAC;AAEF,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE;IACzB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC;AAGF,MAAM,iBAAiB,GAAG,CAAC,MAAgB,EAAE,KAAe,EAAE,EAAE;IAC9D,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3B,OAAO,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACpC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAE7B,OAAO,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACnG,CAAC,CAAC;AAEF,MAAM,MAAM,GAAG,CAAC,KAAU,EAAE,IAAW,EAAE,EAAE;IACzC,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;IACpB,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;IACpB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,IAAI,CAAM,CAAC;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAE1B,CAAC,GAAG,IAAA,iBAAQ,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,GAAG,GAAG,CAAC,CAAC;SACT;QACD,IAAI,CAAC,GAAG,GAAG,EAAE;YACX,GAAG,GAAG,CAAC,CAAC;SACT;KACF;IAGD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE;QACpC,GAAG,IAAI,KAAK,CAAC;KACd;IAED,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,CAAC,CAAC;AAEF,SAAS,uBAAuB,CAAC,UAA4B,EAAE,CAAM,EAAE,CAAM,EAAE,EAAO,EAAE,QAAiB;IACvG,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC;IACtB,MAAM,GAAG,GAAG,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3G,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,SAAS,wBAAwB,CAC/B,CAAS,EACT,CAAS,EACT,CAAS,EACT,MAA2C,EAC3C,IAA8B;IAE9B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;IACtB,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACpC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAEtC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACxF,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAC1D,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACxC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAC1D,OAAO;QACL,CAAC,EAAE,EAAE;QACL,CAAC,EAAE,EAAE;QACL,CAAC,EAAE,EAAE;KACN,CAAC;AACJ,CAAC", "file": "wordcloud.js", "sourcesContent": ["import { CloudLayout } from './cloud-layout';\nimport { isFunction, isNumber, isString, toNumber, Logger } from '@visactor/vutils';\nimport type { TagOutputItem, TagItemAttribute } from './interface';\nimport { GridLayout } from './grid-layout';\nimport { FastLayout } from './fast-layout';\n\nconst OUTPUT = {\n  x: 'x',\n  y: 'y',\n  z: 'z',\n  fontFamily: 'fontFamily',\n  fontSize: 'fontSize',\n  fontStyle: 'fontStyle',\n  fontWeight: 'fontWeight',\n  angle: 'angle'\n};\n\nexport type FieldOption = { field: string };\nexport type CallbackOption = (datum: any) => any;\nexport type AsType = {\n  x: string;\n  y: string;\n  z: string;\n  fontFamily: string;\n  fontSize: string;\n  fontStyle: string;\n  fontWeight: string;\n  angle: string;\n};\n\nexport const transform = (\n  options: {\n    size?: [number, number];\n    fontFamily?: FieldOption | TagItemAttribute<string>;\n    fontStyle?: FieldOption | TagItemAttribute<string>;\n    fontWeight?: FieldOption | TagItemAttribute<string>;\n    fontSize?: FieldOption | TagItemAttribute<number>;\n    fontSizeRange?: [number, number];\n    rotate?: FieldOption | TagItemAttribute<number>;\n    text: FieldOption | CallbackOption | string;\n    spiral?: string;\n    padding?: FieldOption | TagItemAttribute<number>;\n    shape?: string;\n    shrink?: boolean;\n    enlarge?: boolean;\n    clip?: boolean;\n    minFontSize?: number;\n    randomVisible?: boolean;\n    as?: AsType;\n    layoutType?: string;\n    progressiveTime?: number;\n    progressiveStep?: number;\n    depth_3d?: number;\n    postProjection?: string;\n  },\n  upstreamData: any[]\n) => {\n  if (options.size && (options.size[0] <= 0 || options.size[1] <= 0)) {\n    const logger = Logger.getInstance();\n    logger.info('Wordcloud size dimensions must be greater than 0');\n    // size非法不报错，不进行布局，ChartSpace层会有用户初始化size为0的情况\n    return [];\n  }\n\n  /** 输入数据转换 */\n  const data = upstreamData;\n  const canvasSize = (options.size ?? [500, 500]).slice() as [number, number];\n  // canvasSize必须是整数\n  canvasSize[0] = Math.floor(canvasSize[0]);\n  canvasSize[1] = Math.floor(canvasSize[1]);\n  const fontFamily = options.fontFamily ? field(options.fontFamily) : 'sans-serif';\n  const fontStyle = options.fontStyle ? field(options.fontStyle) : 'normal';\n  const fontWeight = options.fontWeight ? field(options.fontWeight) : 'normal';\n  const rotate = options.rotate ? field(options.rotate) : 0;\n  const text = field<string | number>(options.text);\n  const spiral = options.spiral ?? 'archimedean';\n  const padding = options.padding ? field(options.padding) : 1;\n  const shape = options.shape ?? 'square';\n  const shrink = options.shrink ?? false;\n  const enlarge = options.enlarge ?? false;\n  const clip = options.clip ?? false;\n  const minFontSize = options.minFontSize;\n  const randomVisible = options.randomVisible;\n  const as = options.as || OUTPUT;\n  const depth_3d = options.depth_3d;\n  const postProjection = options.postProjection;\n\n  // 根据range转换fontSize\n  let fontSize = options.fontSize ? field(options.fontSize) : 14;\n  const fontSizeRange = options.fontSizeRange;\n  // 只有fontSize不为固定值时，fontSizeRange才生效\n  if (fontSizeRange && !isNumber(fontSize)) {\n    const fsize: any = fontSize;\n    const fontSizeSqrtScale = generateSqrtScale(extent(fsize, data), fontSizeRange as number[]);\n\n    fontSize = datum => {\n      return fontSizeSqrtScale(fsize(datum));\n    };\n  }\n\n  let Layout: any = CloudLayout;\n\n  if (options.layoutType === 'fast') {\n    Layout = FastLayout;\n  } else if (options.layoutType === 'grid') {\n    Layout = GridLayout;\n  }\n\n  /** 执行布局算法 */\n  const layout = new Layout({\n    text,\n    padding,\n    spiral,\n    shape,\n    rotate,\n    fontFamily,\n    fontStyle,\n    fontWeight,\n    fontSize,\n    shrink,\n    clip,\n    enlarge,\n    minFontSize,\n    random: randomVisible,\n    progressiveStep: options.progressiveStep,\n    progressiveTime: options.progressiveTime,\n    outputCallback: (words: any[]) => {\n      const res: any[] = [];\n      let t: any;\n      let w: TagOutputItem;\n\n      for (let i = 0, len = words.length; i < len; i++) {\n        w = words[i];\n        t = w.datum;\n        t[as.x] = w.x;\n        t[as.y] = w.y;\n        t[as.fontFamily] = w.fontFamily;\n        t[as.fontSize] = w.fontSize;\n        t[as.fontStyle] = w.fontStyle;\n        t[as.fontWeight] = w.fontWeight;\n        t[as.angle] = w.angle;\n\n        if (postProjection === 'StereographicProjection') {\n          stereographicProjection(canvasSize, w, t, as, depth_3d);\n        }\n\n        res.push(t);\n      }\n      return res;\n    }\n  });\n\n  layout.layout(data, {\n    width: canvasSize[0],\n    height: canvasSize[1]\n  });\n\n  if (options.progressiveStep > 0 || options.progressiveTime > 0) {\n    return {\n      progressive: layout\n    };\n  }\n\n  return layout.output();\n};\n\n// 取数逻辑\nconst field = <T>(option: FieldOption | TagItemAttribute<T>) => {\n  if (isString(option) || isNumber(option) || isFunction(option)) {\n    return option as TagItemAttribute<T>;\n  }\n  return (datum: any) => datum[(option as FieldOption).field] as T;\n};\n\nconst sqrt = (x: number) => {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n};\n\n// simulation sqrt scale\nconst generateSqrtScale = (domain: number[], range: number[]) => {\n  if (domain[0] === domain[1]) {\n    return (datum: number) => range[0]; // match smallest fontsize\n  }\n\n  const s0 = sqrt(domain[0]);\n  const s1 = sqrt(domain[1]);\n  const min = Math.min(s0, s1);\n  const max = Math.max(s0, s1);\n\n  return (datum: number) => ((sqrt(datum) - min) / (max - min)) * (range[1] - range[0]) + range[0];\n};\n\nconst extent = (field: any, data: any[]) => {\n  let min = +Infinity;\n  let max = -Infinity;\n  const n = data.length;\n  let v: any;\n\n  for (let i = 0; i < n; ++i) {\n    // 字符串类型转换\n    v = toNumber(field(data[i]));\n    if (v < min) {\n      min = v;\n    }\n    if (v > max) {\n      max = v;\n    }\n  }\n\n  // 如果单条数据，匹配最大字号\n  if (data.length === 1 && min === max) {\n    min -= 10000;\n  }\n\n  return [min, max];\n};\n\nfunction stereographicProjection(canvasSize: [number, number], w: any, t: any, as: any, depth_3d?: number) {\n  const maxSize = Math.max(canvasSize[0], canvasSize[1]);\n  const r = maxSize / 2;\n  const out = _StereographicProjection(canvasSize[0], canvasSize[1], r, { x: r, y: r, z: depth_3d ?? r }, w);\n  t[as.x] = out.x;\n  t[as.y] = out.y;\n  t[as.z] = out.z;\n}\n\nfunction _StereographicProjection(\n  w: number,\n  h: number,\n  r: number,\n  center: { x: number; y: number; z: number },\n  word: { x: number; y: number }\n) {\n  const { x, y } = word;\n  const theta = (x / w) * Math.PI * 2;\n  let phi = Math.PI - (y / h) * Math.PI;\n  // 由于cos函数的特性，调整phi的分布，向内聚\n  phi += ((phi < Math.PI / 2 ? 1 : -1) * Math.pow(Math.min(phi - Math.PI / 2, 1), 2)) / 5;\n  const nx = r * Math.sin(phi) * Math.cos(theta) + center.x;\n  const ny = r * Math.cos(phi) + center.y;\n  const nz = r * Math.sin(phi) * Math.sin(theta) + center.z;\n  return {\n    x: nx,\n    y: ny,\n    z: nz\n  };\n}\n"]}