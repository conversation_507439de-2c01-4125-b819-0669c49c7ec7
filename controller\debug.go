package controller

import (
	"fmt"
	"net/http"
	"net/url"
	"one-api/common"

	"github.com/gin-gonic/gin"
)

// DebugNodelocOAuth 调试Nodeloc OAuth URL生成
func DebugNodelocOAuth(c *gin.Context) {
	// 获取当前配置
	clientId := common.NodelocClientId
	if clientId == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "Nodeloc Client ID 未配置",
		})
		return
	}

	// 构建redirect_uri
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/oauth/nodeloc", scheme, c.Request.Host)

	// 生成测试state
	state := "test_state_12345"
	nonce := "test_nonce_67890"

	// 构建OAuth参数
	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", clientId)
	params.Set("redirect_uri", redirectURI)
	params.Set("scope", "openid profile")
	params.Set("state", state)
	params.Set("nonce", nonce)

	oauthURL := "https://conn.nodeloc.cc/oauth2/auth?" + params.Encode()

	// 返回调试信息
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"oauth_url":    oauthURL,
			"client_id":    clientId,
			"redirect_uri": redirectURI,
			"state":        state,
			"nonce":        nonce,
			"parameters": gin.H{
				"response_type": "code",
				"client_id":     clientId,
				"redirect_uri":  redirectURI,
				"scope":         "openid profile",
				"state":         state,
				"nonce":         nonce,
			},
			"instructions": []string{
				"1. 复制上面的 oauth_url",
				"2. 在浏览器中打开该URL",
				"3. 检查是否正确跳转到Nodeloc授权页面",
				"4. 如果出现错误，请检查Nodeloc应用配置中的回调地址是否为: " + redirectURI,
			},
		},
	})
}

// DebugOAuthConfig 调试OAuth配置信息
func DebugOAuthConfig(c *gin.Context) {
	// 构建当前的redirect_uri
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/oauth/nodeloc", scheme, c.Request.Host)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"nodeloc_oauth_enabled": common.NodelocOAuthEnabled,
			"nodeloc_client_id":     common.NodelocClientId,
			"nodeloc_client_secret": func() string {
				if common.NodelocClientSecret != "" {
					return "已配置 (长度: " + fmt.Sprintf("%d", len(common.NodelocClientSecret)) + ")"
				}
				return "未配置"
			}(),
			"redirect_uri":     redirectURI,
			"current_host":     c.Request.Host,
			"current_scheme":   scheme,
			"oauth_endpoints": gin.H{
				"authorization": "https://conn.nodeloc.cc/oauth2/auth",
				"token":         "https://conn.nodeloc.cc/oauth2/token",
				"userinfo":      "https://conn.nodeloc.cc/oauth2/userinfo",
			},
			"required_scope": "openid profile",
			"checklist": []string{
				"✓ 检查 nodeloc_oauth_enabled 是否为 true",
				"✓ 检查 nodeloc_client_id 是否已配置",
				"✓ 检查 nodeloc_client_secret 是否已配置",
				"✓ 在Nodeloc应用管理中设置回调地址为: " + redirectURI,
				"✓ 确保权限范围包含: openid profile",
			},
		},
	})
}
