{"version": 3, "sources": ["../src/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAGvE,MAAM,CAAN,IAAY,0BAGX;AAHD,WAAY,0BAA0B;IACpC,0FAA4D,CAAA;IAC5D,wFAA0D,CAAA;AAC5D,CAAC,EAHW,0BAA0B,KAA1B,0BAA0B,QAGrC;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,IAAc,EAAE,IAAc,EAAE,EAAE;IAC/D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;QAE9C,OAAO,IAAI,CAAC;KACb;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;QAC/E,OAAO,KAAK,CAAC;KACd;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAKF,MAAM,CAAC,MAAM,UAAU,GAAG,GAAG,EAAE;IAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7D,OAAO,GAAG,EAAE;QACV,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AAKF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,EAAE;IACxC,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC,CAAC;AAKF,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,eAAwB,EAAE,EAAE;IACtE,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QACf,OAAO,CAAC,CAAC;KACV;IAED,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;KACpD;IACD,OAAO,MAAM,CAAC;AAEhB,CAAC,CAAC;AAKF,MAAM,UAAU,SAAS,CAAC,GAAW;IACnC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;QAC3E,OAAO,IAAI,CAAC;KACb;IACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;QACrC,MAAM,SAAS,GAAG,WAAW,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAA,SAAS,CAAC,SAAS,0CAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9C,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE;YAC1D,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEtB,OAAO;SACR;QAED,SAAS,CAAC,eAAe,GAAG,GAAG,EAAE;;YAC/B,IAAI,SAAS,EAAE;gBACb,MAAM,OAAO,GAAG,MAAA,SAAS,CAAC,SAAS,0CAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE;oBAC1D,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACvB;qBAAM;oBACL,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,CAAC,CAAC;iBAC9C;aACF;iBAAM;gBACL,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,CAAC,CAAC;aAC9C;QACH,CAAC,CAAC;QACF,SAAS,CAAC,YAAY,GAAG,GAAG,EAAE;YAE5B,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAMD,MAAM,UAAU,WAAW,CAAC,kBAA0C;IACpE,MAAM,EACJ,IAAI,EACJ,YAAY,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EACjC,WAAW,EACX,WAAW,EACZ,GAAG,kBAAkB,CAAC;IACvB,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9E,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACzC,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAErF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;aACxB;iBAAM;gBACL,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;aACvE;YACD,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;QAC9B,MAAM,EACJ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EACd,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,KAAK,EACN,GAAG,MAAM,CAAC;QAEX,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;QACvB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC;QACxB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGzB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE;YAC7B,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;YACvB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;QAGD,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC1C,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;QACvB,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC;QACxB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACzC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAGH,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;IAC9C,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC;IACzB,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAGtC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;IACvB,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnD,GAAG,CAAC,QAAQ,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAG7D,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACrC,CAAC;AAKD,MAAM,UAAU,KAAK,CAAC,KAAe,EAAE,SAA2B;IAChE,MAAM,OAAO,GAAG,SAAS,CAAC;IAC1B,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC;IAC7B,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YACjC,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAErB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBACrD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBACrD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBACrD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;iBACtD;qBAAM;oBAEL,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;iBACtD;gBAED,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBACrD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;iBACtD;aACF;SACF;KACF;IAGD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAElC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAKD,MAAM,UAAU,IAAI,CAClB,KAAsB,EACtB,YAA6B,EAC7B,YAA8B,EAC9B,eAAuB,CAAC;IAGxB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC;IAC9B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;IACtC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;IACvC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEpC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IACH,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAGH,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE9B,SAAS,QAAQ,CAAC,IAAmB,EAAE,KAAc;QACnD,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC;QAEzB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAEjH,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;SAC3C;QACD,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC;QAK/D,GAAG,CAAC,OAAO,EAAE,CAAC;IAChB,CAAC;AACH,CAAC;AAMD,MAAM,UAAU,UAAU,CAAC,MAAW,EAAE,MAAwB,EAAE,IAAY,EAAE,IAAsB;IAEpG,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,CAAC,CAAC;IACb,IAAI,IAAI,CAAC;IACT,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAEZ,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QACjC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACb,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;YAChD,MAAM;SACP;QAMD,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;QACvB,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACpD;IAGD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,CAAM;IAC5B,OAAO,OAAO,CAAC,KAAK,UAAU;QAC5B,CAAC,CAAC,CAAC;QACH,CAAC,CAAC;YACE,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;AACR,CAAC", "file": "util.js", "sourcesContent": ["import { vglobal, createImage } from '@visactor/vrender-core';\nimport { isBase64, isNil, isValidUrl, Logger } from '@visactor/vutils';\nimport type { CloudWordType, LayoutConfigType, SegmentationOutputType } from './interface';\n\nexport enum WORDCLOUD_SHAPE_HOOK_EVENT {\n  BEFORE_WORDCLOUD_SHAPE_LAYOUT = 'beforeWordcloudShapeLayout',\n  AFTER_WORDCLOUD_SHAPE_LAYOUT = 'afterWordcloudShapeLayout'\n}\n\nexport const colorListEqual = (arr0: string[], arr1: string[]) => {\n  if (arr1.length === 1 && arr1[0] === '#537EF5') {\n    // 填充词默认值认为与核心词一致\n    return true;\n  }\n\n  if (!Array.isArray(arr0) || !Array.isArray(arr1) || arr0.length !== arr1.length) {\n    return false;\n  }\n\n  for (let i = 0; i < arr0.length; i++) {\n    if (arr0[i] !== arr1[i]) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * 随机拟合\n */\nexport const fakeRandom = () => {\n  let i = -1;\n  const arr = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9];\n  return () => {\n    i = (i + 1) % arr.length;\n    return arr[i];\n  };\n};\n\n/**\n * 判断是否为中文\n */\nexport const isChinese = (text: string) => {\n  return /^[\\u4e00-\\u9fa5]+$/.test(text);\n};\n\n/**\n * 计算字符长度，中文为1，符号/字母/其他字符为0.5\n */\nexport const calTextLength = (text: string, textLengthLimit?: number) => {\n  if (isNil(text)) {\n    return 0;\n  }\n\n  let length = 0;\n  for (const char of text) {\n    isChinese(char) ? (length += 1) : (length += 0.53);\n  }\n  return length;\n  // return length > textLengthLimit ? textLengthLimit + 1.5 : textLengthLimit;\n};\n\n/**\n * 使用 ResourceLoader 加载图片\n */\nexport function loadImage(url: string) {\n  if (!url || (!isValidUrl(url) && !isBase64(url) && !url.startsWith('<svg'))) {\n    return null;\n  }\n  return new Promise((resolve, reject) => {\n    const imageMark = createImage({ image: url });\n    const imgData = imageMark.resources?.get(url);\n\n    if (imgData && imgData.state === 'success' && imgData.data) {\n      resolve(imgData.data);\n\n      return;\n    }\n\n    imageMark.successCallback = () => {\n      if (imageMark) {\n        const imgData = imageMark.resources?.get(url);\n        if (imgData && imgData.state === 'success' && imgData.data) {\n          resolve(imgData.data);\n        } else {\n          reject(new Error('image load failed' + url));\n        }\n      } else {\n        reject(new Error('image load failed' + url));\n      }\n    };\n    imageMark.failCallback = () => {\n      // eslint-disable-next-line no-undef\n      const logger = Logger.getInstance();\n      logger.error('image 加载失败！', url);\n    };\n  });\n}\n\n/**\n * 绘制连通区域相关信息，用于 debug\n * 红色为边缘、黑方块为中心、黑色数字为面积\n */\nexport function paintLabels(segmentationOutput: SegmentationOutputType) {\n  const {\n    size,\n    segmentation: { regions, labels },\n    shapeBounds,\n    shapeCenter\n  } = segmentationOutput;\n  const paintCanvas = vglobal.createCanvas({ width: size[0], height: size[1] });\n  const ctx = paintCanvas.getContext('2d');\n  const colorList = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#00FFFF', '#FF00FF'];\n\n  for (let i = 0; i < size[1]; i++) {\n    for (let j = 0; j < size[0]; j++) {\n      if (labels[i * size[0] + j] === 0) {\n        ctx.fillStyle = '#fff';\n      } else {\n        ctx.fillStyle = colorList[labels[i * size[0] + j] % colorList.length];\n      }\n      ctx.fillRect(j, i, 1, 1);\n    }\n  }\n\n  regions.forEach((region: any) => {\n    const {\n      center: [x, y],\n      area,\n      boundary,\n      maxPoint,\n      label\n    } = region;\n    // 绘制中心点\n    ctx.fillStyle = '#000';\n    ctx.fillRect(x, y, 3, 3);\n    ctx.font = '15px serif';\n    ctx.fillText(area, x, y);\n\n    // 绘制边缘\n    for (const [x, y] of boundary) {\n      ctx.fillStyle = '#f00';\n      ctx.fillRect(x, y, 1, 1);\n    }\n\n    // 绘制最大点\n    const [iMin, iMax, jMin, jMax] = maxPoint;\n    ctx.fillStyle = '#00f';\n    ctx.font = '15px serif';\n    ctx.fillRect(jMin, iMin, 3, 3);\n    ctx.fillText(`${label}_min`, jMin, iMin);\n    ctx.fillRect(jMax, iMax, 3, 3);\n    ctx.fillText(`${label}_max`, jMax, iMax);\n  });\n\n  // 绘制边界\n  const { x1, y1, width, height } = shapeBounds;\n  ctx.strokeStyle = '#000';\n  ctx.strokeRect(x1, y1, width, height);\n\n  // 绘制整个 shape 的中心点\n  ctx.fillStyle = '#000';\n  ctx.fillRect(shapeCenter[0], shapeCenter[1], 3, 3);\n  ctx.fillText('shape center', shapeCenter[0], shapeCenter[1]);\n\n  // eslint-disable-next-line no-undef\n  document.body.prepend(paintCanvas);\n}\n\n/**\n * 绘制 board\n */\nexport function paint(board: number[], paintSize: [number, number]) {\n  const curSize = paintSize;\n  const imageData = new ImageData(curSize[0], curSize[1]);\n  const array = imageData.data;\n  const w32 = paintSize[0] >> 5;\n\n  for (let y = 0; y < curSize[1]; y++) {\n    for (let x = 0; x < w32; x++) {\n      const value = board[y * w32 + x];\n      const string = (value >>> 0).toString(2).padStart(32, '0');\n      for (let k = 0; k < 32; k++) {\n        if (string[k] === '1') {\n          // 占用像素, 填充白色\n          array[((x << 5) + y * curSize[0] + k) * 4 + 0] = 255;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 1] = 255;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 2] = 255;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 3] = 255;\n        } else {\n          // 未占用像素, 填充黑色\n          array[((x << 5) + y * curSize[0] + k) * 4 + 0] = 0;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 1] = 0;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 2] = 0;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 3] = 255;\n        }\n        // 数组元素分割线, 填充红色, 间隔32px\n        if (k === 0) {\n          array[((x << 5) + y * curSize[0] + k) * 4 + 0] = 255;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 1] = 0;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 2] = 0;\n          array[((x << 5) + y * curSize[0] + k) * 4 + 3] = 255;\n        }\n      }\n    }\n  }\n\n  // eslint-disable-next-line no-undef\n  const canvas = document.createElement('canvas');\n  canvas.width = curSize[0];\n  canvas.height = curSize[1];\n  const ctx = canvas.getContext('2d');\n  ctx.putImageData(imageData, 0, 0);\n  // eslint-disable-next-line no-undef\n  document.body.prepend(canvas);\n}\n\n/**\n * 绘制单词，查看布局效果\n */\nexport function draw(\n  words: CloudWordType[],\n  fillingWords: CloudWordType[],\n  layoutConfig: LayoutConfigType,\n  resizeFactor: number = 1\n) {\n  // eslint-disable-next-line no-undef\n  const canvas = document.createElement('canvas');\n  const radians = Math.PI / 180;\n  const { size } = layoutConfig;\n  canvas.width = size[0] * resizeFactor;\n  canvas.height = size[1] * resizeFactor;\n  const ctx = canvas.getContext('2d');\n\n  words.forEach(word => {\n    word.visible && drawText(word);\n  });\n  fillingWords.forEach(word => {\n    word.visible && drawText(word, '#308ebc');\n  });\n\n  // eslint-disable-next-line no-undef\n  document.body.prepend(canvas);\n\n  function drawText(word: CloudWordType, color?: string) {\n    ctx.save();\n    ctx.textAlign = 'center';\n    // ctx.textBaseline = 'middle'\n    ctx.font = word.fontStyle + ' ' + word.fontWeight + ' ' + word.fontSize * resizeFactor + 'px ' + word.fontFamily;\n    // ctx.fillStyle = color || colorList[~~(Math.random() * colorList.length)]\n    ctx.globalAlpha = word.opacity;\n    ctx.translate(word.x * resizeFactor, word.y * resizeFactor);\n    if (word.rotate) {\n      ctx.rotate((word.rotate * Math.PI) / 180);\n    }\n    ctx.fillText(word.text, 0, word.fontSize * 0.3 * resizeFactor);\n    // ctx.fillStyle = '#f00'\n    // ctx.beginPath()\n    // ctx.arc(0, 0, 1, 0, 2 * Math.PI)\n    // ctx.fill()\n    ctx.restore();\n  }\n}\n\n/**\n * 绘制螺旋线\n */\n\nexport function drawSpiral(spiral: any, center: [number, number], maxR: number, size: [number, number]) {\n  // eslint-disable-next-line no-undef\n  const canvas = document.createElement('canvas');\n  canvas.width = size[0];\n  canvas.height = size[1];\n  const ctx = canvas.getContext('2d');\n  const dt = 1;\n  let dxdy;\n  let dx;\n  let dy;\n  let t = -dt;\n\n  while ((dxdy = spiral((t += dt)))) {\n    dx = dxdy[0];\n    dy = dxdy[1];\n    if (Math.min(Math.abs(dx), Math.abs(dy)) >= maxR) {\n      break;\n    }\n\n    // ctx.beginPath()\n    // ctx.moveTo(center[0] + dx, center[1] + dy)\n    // ctx.lineTo(center[0] + dx, center[1] + dy)\n    // ctx.stroke()\n    ctx.fillStyle = '#f00';\n    ctx.fillRect(center[0] + dx, center[1] + dy, 3, 3);\n  }\n\n  // eslint-disable-next-line no-undef\n  document.body.prepend(canvas);\n}\n\nexport function functor(d: any) {\n  return typeof d === 'function'\n    ? d\n    : function () {\n        return d;\n      };\n}\n"]}