export var AXIS_ELEMENT_NAME;

!function(AXIS_ELEMENT_NAME) {
    AXIS_ELEMENT_NAME.innerView = "inner-view", AXIS_ELEMENT_NAME.axisContainer = "axis-container", 
    AXIS_ELEMENT_NAME.labelContainer = "axis-label-container", AXIS_ELEMENT_NAME.tickContainer = "axis-tick-container", 
    AXIS_ELEMENT_NAME.tick = "axis-tick", AXIS_ELEMENT_NAME.subTick = "axis-sub-tick", 
    AXIS_ELEMENT_NAME.label = "axis-label", AXIS_ELEMENT_NAME.title = "axis-title", 
    AXIS_ELEMENT_NAME.gridContainer = "axis-grid-container", AXIS_ELEMENT_NAME.grid = "axis-grid", 
    AXIS_ELEMENT_NAME.gridRegion = "axis-grid-region", AXIS_ELEMENT_NAME.line = "axis-line", 
    AXIS_ELEMENT_NAME.background = "axis-background", AXIS_ELEMENT_NAME.axisLabelBackground = "axis-label-background";
}(AXIS_ELEMENT_NAME || (AXIS_ELEMENT_NAME = {}));

export var AxisStateValue;

!function(AxisStateValue) {
    AxisStateValue.selected = "selected", AxisStateValue.selectedReverse = "selected_reverse", 
    AxisStateValue.hover = "hover", AxisStateValue.hoverReverse = "hover_reverse";
}(AxisStateValue || (AxisStateValue = {}));

export const DEFAULT_STATES = {
    [AxisStateValue.selectedReverse]: {},
    [AxisStateValue.selected]: {},
    [AxisStateValue.hover]: {},
    [AxisStateValue.hoverReverse]: {}
};
//# sourceMappingURL=constant.js.map