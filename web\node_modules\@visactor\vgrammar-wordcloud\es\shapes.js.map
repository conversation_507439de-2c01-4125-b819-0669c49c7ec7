{"version": 3, "sources": ["../src/shapes.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,MAAM,GAAG;IACpB,eAAe,EAAE,eAAe;IAChC,eAAe,EAAE,QAAQ;IACzB,QAAQ;IACR,OAAO;IACP,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,QAAQ;CACT,CAAC;AAEF,SAAS,OAAO;IACd,OAAO,UAAU,KAAa;QAC5B,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,IAAI;IACX,OAAO,UAAU,KAAa;QAC5B,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;YACrE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC;SAC9G;QACD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,MAAM;IACb,OAAO,UAAU,KAAa;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,UAAU,KAAa;QAC5B,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,eAAe;IACtB,OAAO,UAAU,KAAa;QAC5B,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ;IACf,OAAO,UAAU,KAAa;QAC5B,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,MAAM;IACb,OAAO;QACL,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,UAAU,KAAa;QAC5B,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,KAAa,EAAE,IAAsB;IACzE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAE5C,QAAQ,KAAK,EAAE;QACb,KAAK,UAAU;YACb,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;YAChC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACnD,MAAM;QACR,KAAK,iBAAiB;YACpB,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChG,MAAM;QACR,KAAK,UAAU,CAAC;QAChB,KAAK,iBAAiB;YACpB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACxB,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM;QACR,KAAK,MAAM;YACT,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM;QACR;YACE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM;KACT;IACD,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;AAC/B,CAAC;AAED,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAY,EAAE,EAAE;IAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;KACvB;IAED,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;AACzB,CAAC,CAAC", "file": "shapes.js", "sourcesContent": ["export const shapes = {\n  triangleForward: triangleForward,\n  triangleUpright: triangle,\n  triangle, // 三角形\n  diamond, // 菱形\n  square, // 方形\n  star, // 星形\n  cardioid, // 心形\n  circle, // 圆形\n  pentagon // 五角形\n};\n\nfunction diamond() {\n  return function (theta: number) {\n    const thetaPrime = theta % ((2 * Math.PI) / 4);\n    return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));\n  };\n}\nfunction star() {\n  return function (theta: number) {\n    const thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 10);\n    if (((theta + 0.955) % ((2 * Math.PI) / 5)) - (2 * Math.PI) / 10 >= 0) {\n      return 1 / (Math.cos((2 * Math.PI) / 10 - thetaPrime) + 3.07768 * Math.sin((2 * Math.PI) / 10 - thetaPrime));\n    }\n    return 1 / (Math.cos(thetaPrime) + 3.07768 * Math.sin(thetaPrime));\n  };\n}\nfunction square() {\n  return function (theta: number) {\n    return Math.min(1 / Math.abs(Math.cos(theta)), 1 / Math.abs(Math.sin(theta)));\n  };\n}\n\nfunction triangle() {\n  return function (theta: number) {\n    const thetaPrime = (theta + (Math.PI * 3) / 2) % ((2 * Math.PI) / 3);\n    return 1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime));\n  };\n}\nfunction triangleForward() {\n  return function (theta: number) {\n    const thetaPrime = theta % ((2 * Math.PI) / 3);\n    return 1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime));\n  };\n}\nfunction cardioid() {\n  return function (theta: number) {\n    return 1 - Math.sin(theta);\n  };\n}\nfunction circle() {\n  return function () {\n    return 1;\n  };\n}\n\nfunction pentagon() {\n  return function (theta: number) {\n    const thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 5);\n    return 1 / (Math.cos(thetaPrime) + 0.726543 * Math.sin(thetaPrime));\n  };\n}\n\nexport function getMaxRadiusAndCenter(shape: string, size: [number, number]) {\n  const w = size[0];\n  const h = size[1];\n  let maxRadius = 1;\n  const center = [size[0] >> 1, size[1] >> 1];\n\n  switch (shape) {\n    case 'cardioid':\n      center[1] = ~~((h / 2.7) * 0.6);\n      maxRadius = Math.floor(Math.min(w / 2.3, h / 2.6));\n      break;\n    case 'triangleForward':\n      maxRadius = h / Math.sqrt(0.75) > w ? Math.floor(w / 2) : Math.floor(h / (2 * Math.sqrt(0.75)));\n      break;\n    case 'triangle':\n    case 'triangleUpright':\n      center[1] = ~~(h / 1.5);\n      maxRadius = Math.floor(Math.min(h / 1.5, w / 2));\n      break;\n    case 'rect':\n      maxRadius = Math.floor(Math.max(h / 2, w / 2));\n      break;\n    default:\n      maxRadius = Math.floor(Math.min(w / 2, h / 2));\n      break;\n  }\n  return { maxRadius, center };\n}\n\nexport const getShapeFunction = (type: string) => {\n  if (shapes[type]) {\n    return shapes[type]();\n  }\n\n  return shapes.circle();\n};\n"]}