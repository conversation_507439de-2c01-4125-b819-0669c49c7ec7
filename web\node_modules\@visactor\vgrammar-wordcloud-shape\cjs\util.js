"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.functor = exports.drawSpiral = exports.draw = exports.paint = exports.paintLabels = exports.loadImage = exports.calTextLength = exports.isChinese = exports.fakeRandom = exports.colorListEqual = exports.WORDCLOUD_SHAPE_HOOK_EVENT = void 0;

const vrender_core_1 = require("@visactor/vrender-core"), vutils_1 = require("@visactor/vutils");

var WORDCLOUD_SHAPE_HOOK_EVENT;

!function(WORDCLOUD_SHAPE_HOOK_EVENT) {
    WORDCLOUD_SHAPE_HOOK_EVENT.BEFORE_WORDCLOUD_SHAPE_LAYOUT = "beforeWordcloudShapeLayout", 
    WORDCLOUD_SHAPE_HOOK_EVENT.AFTER_WORDCLOUD_SHAPE_LAYOUT = "afterWordcloudShapeLayout";
}(WORDCLOUD_SHAPE_HOOK_EVENT = exports.WORDCLOUD_SHAPE_HOOK_EVENT || (exports.WORDCLOUD_SHAPE_HOOK_EVENT = {}));

const colorListEqual = (arr0, arr1) => {
    if (1 === arr1.length && "#537EF5" === arr1[0]) return !0;
    if (!Array.isArray(arr0) || !Array.isArray(arr1) || arr0.length !== arr1.length) return !1;
    for (let i = 0; i < arr0.length; i++) if (arr0[i] !== arr1[i]) return !1;
    return !0;
};

exports.colorListEqual = colorListEqual;

const fakeRandom = () => {
    let i = -1;
    const arr = [ 0, .1, .2, .3, .4, .5, .6, .7, .8, .9 ];
    return () => (i = (i + 1) % arr.length, arr[i]);
};

exports.fakeRandom = fakeRandom;

const isChinese = text => /^[\u4e00-\u9fa5]+$/.test(text);

exports.isChinese = isChinese;

const calTextLength = (text, textLengthLimit) => {
    if ((0, vutils_1.isNil)(text)) return 0;
    let length = 0;
    for (const char of text) (0, exports.isChinese)(char) ? length += 1 : length += .53;
    return length;
};

function loadImage(url) {
    return url && ((0, vutils_1.isValidUrl)(url) || (0, vutils_1.isBase64)(url) || url.startsWith("<svg")) ? new Promise(((resolve, reject) => {
        var _a;
        const imageMark = (0, vrender_core_1.createImage)({
            image: url
        }), imgData = null === (_a = imageMark.resources) || void 0 === _a ? void 0 : _a.get(url);
        imgData && "success" === imgData.state && imgData.data ? resolve(imgData.data) : (imageMark.successCallback = () => {
            var _a;
            if (imageMark) {
                const imgData = null === (_a = imageMark.resources) || void 0 === _a ? void 0 : _a.get(url);
                imgData && "success" === imgData.state && imgData.data ? resolve(imgData.data) : reject(new Error("image load failed" + url));
            } else reject(new Error("image load failed" + url));
        }, imageMark.failCallback = () => {
            vutils_1.Logger.getInstance().error("image 加载失败！", url);
        });
    })) : null;
}

function paintLabels(segmentationOutput) {
    const {size: size, segmentation: {regions: regions, labels: labels}, shapeBounds: shapeBounds, shapeCenter: shapeCenter} = segmentationOutput, paintCanvas = vrender_core_1.vglobal.createCanvas({
        width: size[0],
        height: size[1]
    }), ctx = paintCanvas.getContext("2d"), colorList = [ "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#00FFFF", "#FF00FF" ];
    for (let i = 0; i < size[1]; i++) for (let j = 0; j < size[0]; j++) 0 === labels[i * size[0] + j] ? ctx.fillStyle = "#fff" : ctx.fillStyle = colorList[labels[i * size[0] + j] % colorList.length], 
    ctx.fillRect(j, i, 1, 1);
    regions.forEach((region => {
        const {center: [x, y], area: area, boundary: boundary, maxPoint: maxPoint, label: label} = region;
        ctx.fillStyle = "#000", ctx.fillRect(x, y, 3, 3), ctx.font = "15px serif", ctx.fillText(area, x, y);
        for (const [x, y] of boundary) ctx.fillStyle = "#f00", ctx.fillRect(x, y, 1, 1);
        const [iMin, iMax, jMin, jMax] = maxPoint;
        ctx.fillStyle = "#00f", ctx.font = "15px serif", ctx.fillRect(jMin, iMin, 3, 3), 
        ctx.fillText(`${label}_min`, jMin, iMin), ctx.fillRect(jMax, iMax, 3, 3), ctx.fillText(`${label}_max`, jMax, iMax);
    }));
    const {x1: x1, y1: y1, width: width, height: height} = shapeBounds;
    ctx.strokeStyle = "#000", ctx.strokeRect(x1, y1, width, height), ctx.fillStyle = "#000", 
    ctx.fillRect(shapeCenter[0], shapeCenter[1], 3, 3), ctx.fillText("shape center", shapeCenter[0], shapeCenter[1]), 
    document.body.prepend(paintCanvas);
}

function paint(board, paintSize) {
    const curSize = paintSize, imageData = new ImageData(curSize[0], curSize[1]), array = imageData.data, w32 = paintSize[0] >> 5;
    for (let y = 0; y < curSize[1]; y++) for (let x = 0; x < w32; x++) {
        const string = (board[y * w32 + x] >>> 0).toString(2).padStart(32, "0");
        for (let k = 0; k < 32; k++) "1" === string[k] ? (array[4 * ((x << 5) + y * curSize[0] + k) + 0] = 255, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 1] = 255, array[4 * ((x << 5) + y * curSize[0] + k) + 2] = 255, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 3] = 255) : (array[4 * ((x << 5) + y * curSize[0] + k) + 0] = 0, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 1] = 0, array[4 * ((x << 5) + y * curSize[0] + k) + 2] = 0, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 3] = 255), 0 === k && (array[4 * ((x << 5) + y * curSize[0] + k) + 0] = 255, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 1] = 0, array[4 * ((x << 5) + y * curSize[0] + k) + 2] = 0, 
        array[4 * ((x << 5) + y * curSize[0] + k) + 3] = 255);
    }
    const canvas = document.createElement("canvas");
    canvas.width = curSize[0], canvas.height = curSize[1];
    canvas.getContext("2d").putImageData(imageData, 0, 0), document.body.prepend(canvas);
}

function draw(words, fillingWords, layoutConfig, resizeFactor = 1) {
    const canvas = document.createElement("canvas"), {size: size} = (Math.PI, layoutConfig);
    canvas.width = size[0] * resizeFactor, canvas.height = size[1] * resizeFactor;
    const ctx = canvas.getContext("2d");
    function drawText(word, color) {
        ctx.save(), ctx.textAlign = "center", ctx.font = word.fontStyle + " " + word.fontWeight + " " + word.fontSize * resizeFactor + "px " + word.fontFamily, 
        ctx.globalAlpha = word.opacity, ctx.translate(word.x * resizeFactor, word.y * resizeFactor), 
        word.rotate && ctx.rotate(word.rotate * Math.PI / 180), ctx.fillText(word.text, 0, .3 * word.fontSize * resizeFactor), 
        ctx.restore();
    }
    words.forEach((word => {
        word.visible && drawText(word);
    })), fillingWords.forEach((word => {
        word.visible && drawText(word, "#308ebc");
    })), document.body.prepend(canvas);
}

function drawSpiral(spiral, center, maxR, size) {
    const canvas = document.createElement("canvas");
    canvas.width = size[0], canvas.height = size[1];
    const ctx = canvas.getContext("2d");
    let dxdy, dx, dy, t = -1;
    for (;(dxdy = spiral(t += 1)) && (dx = dxdy[0], dy = dxdy[1], !(Math.min(Math.abs(dx), Math.abs(dy)) >= maxR)); ) ctx.fillStyle = "#f00", 
    ctx.fillRect(center[0] + dx, center[1] + dy, 3, 3);
    document.body.prepend(canvas);
}

function functor(d) {
    return "function" == typeof d ? d : function() {
        return d;
    };
}

exports.calTextLength = calTextLength, exports.loadImage = loadImage, exports.paintLabels = paintLabels, 
exports.paint = paint, exports.draw = draw, exports.drawSpiral = drawSpiral, exports.functor = functor;
//# sourceMappingURL=util.js.map