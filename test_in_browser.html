<!DOCTYPE html>
<html>
<head>
    <title>Nodeloc OAuth 浏览器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffe6e6; border: 1px solid #ff9999; }
        .success { background: #e6ffe6; border: 1px solid #99ff99; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
        input { width: 300px; padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Nodeloc OAuth 浏览器测试工具</h1>
        
        <div>
            <h3>1. 检查API状态</h3>
            <button onclick="checkAPIStatus()">检查 /api/status</button>
            <button onclick="checkDebugConfig()">检查调试配置</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div>
            <h3>2. 手动构建OAuth URL</h3>
            <label>Client ID: <input type="text" id="clientId" placeholder="输入你的Client ID"></label><br>
            <label>域名: <input type="text" id="domain" placeholder="http://localhost:3000"></label><br>
            <button onclick="buildOAuthURL()">构建OAuth URL</button>
            <div id="oauthResult" class="result"></div>
        </div>

        <div>
            <h3>3. 测试前端状态获取</h3>
            <button onclick="checkFrontendStatus()">检查前端状态</button>
            <div id="frontendResult" class="result"></div>
        </div>
    </div>

    <script>
        // 检查API状态
        async function checkAPIStatus() {
            const resultDiv = document.getElementById('apiResult');
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>API状态检查结果:</h4>
                    <p><strong>Nodeloc OAuth启用:</strong> ${data.nodeloc_oauth}</p>
                    <p><strong>Nodeloc Client ID:</strong> ${data.nodeloc_client_id || '未配置'}</p>
                    <p><strong>完整响应:</strong></p>
                    <textarea readonly>${JSON.stringify(data, null, 2)}</textarea>
                `;
                
                // 自动填入Client ID
                if (data.nodeloc_client_id) {
                    document.getElementById('clientId').value = data.nodeloc_client_id;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<p>错误: ${error.message}</p>`;
            }
        }

        // 检查调试配置
        async function checkDebugConfig() {
            const resultDiv = document.getElementById('apiResult');
            try {
                const response = await fetch('/api/debug/oauth/config');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>调试配置检查结果:</h4>
                    <textarea readonly>${JSON.stringify(data, null, 2)}</textarea>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<p>调试端点错误: ${error.message}</p>`;
            }
        }

        // 构建OAuth URL
        function buildOAuthURL() {
            const clientId = document.getElementById('clientId').value;
            const domain = document.getElementById('domain').value || window.location.origin;
            const resultDiv = document.getElementById('oauthResult');
            
            if (!clientId) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<p>请输入Client ID</p>';
                return;
            }
            
            // 生成随机参数
            const state = Math.random().toString(36).substring(2, 15);
            const nonce = Math.random().toString(36).substring(2, 15);
            const redirectUri = domain + '/oauth/nodeloc';
            
            // 构建参数
            const params = new URLSearchParams({
                'response_type': 'code',
                'client_id': clientId,
                'redirect_uri': redirectUri,
                'scope': 'openid profile',
                'state': state,
                'nonce': nonce
            });
            
            const oauthUrl = `https://conn.nodeloc.cc/oauth2/auth?${params.toString()}`;
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>生成的OAuth URL:</h4>
                <textarea readonly>${oauthUrl}</textarea>
                <p><a href="${oauthUrl}" target="_blank" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">🚀 测试此URL</a></p>
                <h4>参数详情:</h4>
                <ul>
                    <li><strong>Client ID:</strong> ${clientId}</li>
                    <li><strong>Redirect URI:</strong> ${redirectUri}</li>
                    <li><strong>State:</strong> ${state}</li>
                    <li><strong>Nonce:</strong> ${nonce}</li>
                    <li><strong>Scope:</strong> openid profile</li>
                </ul>
            `;
        }

        // 检查前端状态
        function checkFrontendStatus() {
            const resultDiv = document.getElementById('frontendResult');
            
            // 检查localStorage
            const localStatus = localStorage.getItem('status');
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>前端状态检查:</h4>
                <p><strong>LocalStorage中的状态:</strong></p>
                <textarea readonly>${localStatus || '无数据'}</textarea>
                <p><strong>当前页面URL:</strong> ${window.location.href}</p>
                <p><strong>Origin:</strong> ${window.location.origin}</p>
            `;
        }

        // 页面加载时自动填入域名
        window.onload = function() {
            document.getElementById('domain').value = window.location.origin;
        };
    </script>
</body>
</html>
