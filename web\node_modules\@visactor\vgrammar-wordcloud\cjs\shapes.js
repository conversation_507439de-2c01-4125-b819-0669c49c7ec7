"use strict";

function diamond() {
    return function(theta) {
        const thetaPrime = theta % (2 * Math.PI / 4);
        return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));
    };
}

function star() {
    return function(theta) {
        const thetaPrime = (theta + .955) % (2 * Math.PI / 10);
        return (theta + .955) % (2 * Math.PI / 5) - 2 * Math.PI / 10 >= 0 ? 1 / (Math.cos(2 * Math.PI / 10 - thetaPrime) + 3.07768 * Math.sin(2 * Math.PI / 10 - thetaPrime)) : 1 / (Math.cos(thetaPrime) + 3.07768 * Math.sin(thetaPrime));
    };
}

function square() {
    return function(theta) {
        return Math.min(1 / Math.abs(Math.cos(theta)), 1 / Math.abs(Math.sin(theta)));
    };
}

function triangle() {
    return function(theta) {
        const thetaPrime = (theta + 3 * Math.PI / 2) % (2 * Math.PI / 3);
        return 1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime));
    };
}

function triangleForward() {
    return function(theta) {
        const thetaPrime = theta % (2 * Math.PI / 3);
        return 1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime));
    };
}

function cardioid() {
    return function(theta) {
        return 1 - Math.sin(theta);
    };
}

function circle() {
    return function() {
        return 1;
    };
}

function pentagon() {
    return function(theta) {
        const thetaPrime = (theta + .955) % (2 * Math.PI / 5);
        return 1 / (Math.cos(thetaPrime) + .726543 * Math.sin(thetaPrime));
    };
}

function getMaxRadiusAndCenter(shape, size) {
    const w = size[0], h = size[1];
    let maxRadius = 1;
    const center = [ size[0] >> 1, size[1] >> 1 ];
    switch (shape) {
      case "cardioid":
        center[1] = ~~(h / 2.7 * .6), maxRadius = Math.floor(Math.min(w / 2.3, h / 2.6));
        break;

      case "triangleForward":
        maxRadius = h / Math.sqrt(.75) > w ? Math.floor(w / 2) : Math.floor(h / (2 * Math.sqrt(.75)));
        break;

      case "triangle":
      case "triangleUpright":
        center[1] = ~~(h / 1.5), maxRadius = Math.floor(Math.min(h / 1.5, w / 2));
        break;

      case "rect":
        maxRadius = Math.floor(Math.max(h / 2, w / 2));
        break;

      default:
        maxRadius = Math.floor(Math.min(w / 2, h / 2));
    }
    return {
        maxRadius: maxRadius,
        center: center
    };
}

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.getShapeFunction = exports.getMaxRadiusAndCenter = exports.shapes = void 0, 
exports.shapes = {
    triangleForward: triangleForward,
    triangleUpright: triangle,
    triangle: triangle,
    diamond: diamond,
    square: square,
    star: star,
    cardioid: cardioid,
    circle: circle,
    pentagon: pentagon
}, exports.getMaxRadiusAndCenter = getMaxRadiusAndCenter;

const getShapeFunction = type => exports.shapes[type] ? exports.shapes[type]() : exports.shapes.circle();

exports.getShapeFunction = getShapeFunction;
//# sourceMappingURL=shapes.js.map