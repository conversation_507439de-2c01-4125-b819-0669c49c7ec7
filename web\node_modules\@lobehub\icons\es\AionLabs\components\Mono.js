'use client';

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var _excluded = ["size", "style"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
import { memo } from 'react';
import { TITLE } from "../style";
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var Icon = /*#__PURE__*/memo(function (_ref) {
  var _ref$size = _ref.size,
    size = _ref$size === void 0 ? '1em' : _ref$size,
    style = _ref.style,
    rest = _objectWithoutProperties(_ref, _excluded);
  return /*#__PURE__*/_jsxs("svg", _objectSpread(_objectSpread({
    fill: "currentColor",
    fillRule: "evenodd",
    height: size,
    style: _objectSpread({
      flex: 'none',
      lineHeight: 1
    }, style),
    viewBox: "0 0 24 24",
    width: size,
    xmlns: "http://www.w3.org/2000/svg"
  }, rest), {}, {
    children: [/*#__PURE__*/_jsx("title", {
      children: TITLE
    }), /*#__PURE__*/_jsx("path", {
      d: "M8.303.964c.038.016.064.01.076-.016a.487.487 0 00.048-.034L8.76.66a2.845 2.845 0 011.5-.575c.078-.007.148-.007.211 0 .12.01.19.016.21.016.01 0 .02 0 .029.004a.416.416 0 00.148.021c.016 0 .031.002.046.006a.303.303 0 00.15.015c.34-.085.613-.133.82-.144.191-.01.42.012.688.067.454.092.864.288 1.23.588.048.04.13.114.249.223.007.006.015.01.024.011.037.006.07.001.099-.015a1.06 1.06 0 01.716-.12l.367.022c.235.023.446.083.632.18.096.048.223.128.383.238a.05.05 0 00.025.009c.047.003.091-.035.125-.03.033.004.068-.02.106-.024a3.47 3.47 0 01.815 0c.135.015.264.047.386.097.306.127.583.296.83.507.3.255.57.55.765.9.133.239.22.455.263.65.021.1.046.224.073.372.002.01.005.018.009.028.07.172.167.335.23.508.04.113.07.208.087.286.024.108.047.239.069.394.006.051.018.108.034.17a.142.142 0 00.046.073c.273.233.49.514.617.841.037.095.06.207.068.337.013.199.02.41-.042.598-.046.143-.076.23-.089.26a.085.085 0 00-.005.047c.016.105.023.18.02.225-.013.152-.021.265-.026.34a1.14 1.14 0 01-.121.43c-.247.5-.7.773-1.25.844a.236.236 0 00-.09.038c-.018.01-.02.034-.007.07a.062.062 0 01.003.023 2.592 2.592 0 01-.068.463c-.068.3-.205.55-.41.748a1.68 1.68 0 01-.742.41c-.036.01-.116.022-.24.034a.033.033 0 00-.027.022c-.01.033 0 .097-.017.15-.023.069-.006.17-.026.239-.02.065-.015.134-.024.215-.006.044-.023.163-.053.357a12.686 12.686 0 01-1.535 4.4c-.105.184-.23.366-.363.564a.07.07 0 00-.01.052c.089.486.185.956.32 1.416.08.272.182.523.383.715.057.054.155.121.296.203.63.367 1.277.656 1.985.963a.019.019 0 01.007.005.02.02 0 01.004.008.02.02 0 010 .009 2.052 2.052 0 01-.135.176c-.757.875-1.549 1.639-2.448 2.277a7.977 7.977 0 01-2.65 1.23.417.417 0 01-.087.017c-.01 0-.019.002-.027.008-.04.027-.092.01-.138.037a.063.063 0 01-.033.008.275.275 0 00-.12.02.081.081 0 01-.028.007c-.14.004-.259.029-.387.031-.327.007-.686.034-1.02.025a8.142 8.142 0 01-.646-.037 5.625 5.625 0 01-.613-.13 6.23 6.23 0 01-1.764-.73 4.407 4.407 0 01-1.199-1.098 1.296 1.296 0 01-.276-.625.734.734 0 00-.029-.163.679.679 0 00-.022-.192.17.17 0 01-.006-.045.648.648 0 00-.008-.127c-.025-.112-.02-.18-.037-.31a22.16 22.16 0 00-.173-1.086.667.667 0 01-.012-.104c-.002-.053-.027-.086-.026-.14a.28.28 0 00-.023-.129.051.051 0 01-.005-.027.358.358 0 00-.016-.134c-.023-.06-.008-.123-.036-.198a.1.1 0 01-.006-.036.33.33 0 00-.012-.115c-.026-.069-.008-.136-.04-.208a.063.063 0 01-.004-.027.357.357 0 00-.015-.124c-.023-.064-.006-.12-.03-.18-.021-.052-.004-.119-.028-.17-.025-.055-.008-.125-.032-.187-.019-.047-.003-.093-.023-.148-.02-.06-.008-.13-.032-.184-.016-.036-.01-.079-.019-.125l-.05-.267a.98.98 0 00-.061-.24.022.022 0 00-.02-.014.278.278 0 01-.07-.019 2.243 2.243 0 01-.69-.397 1.597 1.597 0 01-.48-.695 2.39 2.39 0 01-.104-.4c-.03-.17-.147-.303-.217-.43a1.498 1.498 0 01-.142-.384c-.01-.041-.004-.115-.028-.147a.017.017 0 01.004-.024c.018-.013.024-.044.016-.092-.006-.037-.024-.055-.053-.054a.03.03 0 01-.013-.002.03.03 0 01-.01-.008c-.26-.308-.456-.554-.545-.897a2.623 2.623 0 01-.086-.483 4.554 4.554 0 00-.021-.279.02.02 0 01-.005-.014.497.497 0 00.046-.183.323.323 0 00-.056-.251.123.123 0 01-.023-.052l-.078-.394a.114.114 0 01-.002-.037c.017-.149.028-.237.034-.264.045-.212.126-.406.244-.583a.025.025 0 00.004-.018.026.026 0 00-.01-.015 2.888 2.888 0 01-.927-1.357A2.66 2.66 0 013 8.36c.001-.148.008-.28.02-.397a2.276 2.276 0 01.656-1.395c.166-.168.356-.318.572-.452.037-.022.053-.053.05-.092a.417.417 0 00-.024-.103.077.077 0 01-.005-.028 20.28 20.28 0 01.004-1.105c.006-.126.038-.28.096-.462.175-.556.438-1.015.79-1.379.215-.222.449-.4.7-.535.162-.087.314-.142.396-.252.173-.233.373-.45.6-.65.312-.274.68-.447 1.103-.519.104-.017.229-.01.345-.027zm.106.32l-.24-.047a1.42 1.42 0 00-.59.115c-.674.296-1.225.917-1.414 1.616-.045.17-.053.305-.075.51a.087.087 0 01-.035.06c-.113.084-.26.186-.363.302a2.227 2.227 0 00-.355.53 1.45 1.45 0 00-.133.55.745.745 0 00-.009.282l.016.24a.016.016 0 01-.015.017.264.264 0 00-.131.046c-.305.204-.512.52-.631.862-.042.12-.118.326-.078.436a.042.042 0 00.043.027.043.043 0 00.018-.005.557.557 0 00.194-.177c.237-.315.489-.593.86-.758a1.79 1.79 0 011.076-.127 7.415 7.415 0 01.518.114.012.012 0 00.007-.016 1.69 1.69 0 01-.134-.5.827.827 0 010-.142l.015-.128a1.89 1.89 0 00.61 1.269c.254.231.557.338.898.375.237.026.46.028.671.006a.32.32 0 01.128-.009 1.735 1.735 0 001.225-.678v-.006a.013.013 0 00-.005-.006.012.012 0 00-.006-.002l-.434-.048a4.718 4.718 0 00-.369-.141c-.138-.046-.271-.05-.41-.068-.453-.058-.831-.371-.914-.841-.028-.318.026-.623.244-.865.065-.072.143-.148.233-.227a.088.088 0 00.03-.096.056.056 0 01-.001-.026l.067-.312.174-.325a.014.014 0 00-.014-.02 1.24 1.24 0 00-.877.605c-.186.309-.264.69-.152 1.03a.004.004 0 01-.002.005h-.003l-.002-.002c-.222-.418-.247-.911.03-1.309.253-.36.775-.712 1.247-.67a.03.03 0 00.024-.01l.203-.214a2.28 2.28 0 01.443-.352c.112-.068.259-.128.379-.194a.016.016 0 00.001-.027.784.784 0 00-.267-.118 2.217 2.217 0 00-.79-.046c-.45.054-.943.304-1.302.596a.012.012 0 01-.013.002.013.013 0 01-.008-.01c0-.01.001-.018.005-.024.16-.231.342-.419.546-.562.507-.357 1.193-.4 1.768-.204.128.061.25.133.367.215a.219.219 0 00.073.037.52.52 0 00.287-.064.36.36 0 00.187-.269.23.23 0 00-.004-.128.065.065 0 00-.016-.023c-.057-.05-.085-.106-.085-.168 0-.12.075-.2.199-.177a.478.478 0 01.168.076.605.605 0 01.11.435l.004.004a.011.011 0 00.011.002c.214-.088.363-.079.641-.091a.019.019 0 01.014.005.019.019 0 01.005.013v.21a.011.011 0 01-.01.011c-.24.026-.398.053-.475.08-.558.201-.93.583-1.113 1.144-.105.38-.085.752.06 1.113.126.301.32.55.578.745.442.332 1.015.404 1.552.291.111-.023.209-.07.3-.11a.004.004 0 00.002-.003.005.005 0 000-.006l-.004-.001a1.237 1.237 0 01-.87-.314 1.726 1.726 0 01-.213-.235.79.79 0 00.546.172c.226-.01.468-.102.6-.297a.024.024 0 00.003-.02.025.025 0 00-.005-.009.025.025 0 00-.008-.006c-.497-.249-.698-.726-.654-1.268a.134.134 0 01.032-.054.014.014 0 01.023.009l.003.038a.537.537 0 00.053.19c.147.316.371.546.67.686.47.221 1.122.188 1.551-.114a.712.712 0 00.258-.32.417.417 0 00-.043-.41c-.122-.153-.27-.194-.44-.123-.117.048-.187.133-.296.164-.55.159-1.123-.414-.907-.96a.503.503 0 01.346-.266l.1.004a1.048 1.048 0 00-.146.241c-.103.23-.046.492.174.62.12.058.235.06.342.004.095-.05.145-.141.213-.22a.394.394 0 01.273-.14c.486-.046.884.347 1.062.77.061.201.09.398.084.591l-.037.278-.024.106c-.08.118-.138.254-.177.406-.07.28.045.482.245.662a.008.008 0 00.01 0 .008.008 0 00.003-.008.977.977 0 01.01-.526l.11-.242c.147-.198.335-.327.563-.387a1.613 1.613 0 01.412-.047.012.012 0 00.004-.003.013.013 0 00.003-.005V3.87c-.049-.26-.03-.483.054-.669.264-.578.864-.658 1.409-.47.262.091.474.247.699.383a.011.011 0 00.012 0 .011.011 0 00.004-.005.454.454 0 00-.048-.163 2.018 2.018 0 00-1.123-1.113 2.008 2.008 0 00-.558-.102.306.306 0 01.276-.158.01.01 0 00.006-.002.01.01 0 00.003-.006.01.01 0 00-.007-.01 2.14 2.14 0 01-.19-.064 1.71 1.71 0 00-1.061-.062.033.033 0 00-.02.05l.087.152a.044.044 0 00.048.021c.057-.012.135-.02.235-.025a.694.694 0 01.415.108 1.79 1.79 0 00-1.339.788.201.201 0 01-.02-.04c-.015-.034-.041-.05-.054-.08-.022-.052.029-.156.058-.2.107-.16.245-.297.412-.41a.021.021 0 00.008-.027.74.74 0 00-.18-.248 1.394 1.394 0 00-.693-.434c-.512-.143-1.04-.067-1.457.256a9.16 9.16 0 00-.268.221c-.046.04-.093.053-.14.04a.798.798 0 00-.52.017c-.307.12-.544.371-.593.71-.004.03-.002.07-.006.104a.026.026 0 01-.02.022 1.331 1.331 0 00-.347.153c-.308.174-.442.441-.403.8.01.083.036.169.079.259l-.052.216c-.064-.083-.137-.169-.18-.255a.988.988 0 01-.035-.814c.08-.214.284-.44.534-.39a.167.167 0 01.119.08.026.026 0 00.025.012.025.025 0 00.02-.019c.01-.036.02-.082.026-.138.074-.558.534-1.062 1.136-1.077.039 0 .077-.012.11-.034l.284-.182a.012.012 0 00.005-.008v-.005a2.488 2.488 0 00-1.269-.735 2.637 2.637 0 00-1.47.116.132.132 0 01-.08.003A2.257 2.257 0 009.123.71a2.2 2.2 0 00-.24.165c-.157.138-.316.274-.474.41zm.965 2.196a1.215 1.215 0 00-.388.183.453.453 0 00-.112.11.057.057 0 00-.01.025c-.061.389-.04.825.21 1.152.455.6 1.252.715 1.936.475.121-.042.246-.124.356-.174a.012.012 0 00.005-.004.011.011 0 00.001-.007.011.011 0 00-.008-.01 1.759 1.759 0 01-.896-.665 1.446 1.446 0 01-.25-.516c-.094-.44-.04-.879.164-1.317.084-.166.184-.323.302-.47a.248.248 0 00.033-.051.01.01 0 000-.006.01.01 0 00-.006-.01 1.398 1.398 0 00-.517.167c-.42.238-.757.638-.82 1.118zm-4.8 2.044c.088-.12.184-.224.285-.31a.128.128 0 00.045-.087l.03-.347c.083-.455.288-.84.613-1.152a1.54 1.54 0 01.302-.211.032.032 0 00.016-.03 1.823 1.823 0 01.121-.713.015.015 0 00-.003-.016.015.015 0 00-.016-.003.59.59 0 00-.076.037 2.595 2.595 0 00-.825.75 3.317 3.317 0 00-.53 1.224 2.961 2.961 0 00-.036.947.005.005 0 00.009 0l.064-.089zm13.143-.942c-.136.069-.282.106-.438.11-.306.01-.548-.09-.7-.357a.018.018 0 00-.027-.005l-.085.071c-.322.25-.288.603-.04.893.168.198.38.326.634.384.448.101.9.014 1.242-.295.233-.21.37-.47.41-.782.01-.071.02-.158.028-.26a.077.077 0 01.063-.069.263.263 0 01.11.012c.435.119.668.58.77.984.003.016 0 .024-.01.025-.121.005-.25.017-.386.035a.016.016 0 00-.013.013l.001.01a.016.016 0 00.007.006c.143.076.274.283.258.445-.016.163-.034.329-.053.5-.015.139-.01.249-.009.422 0 .01.002.02.006.03.04.114.082.196.217.19.01 0 .018-.003.027-.007.166-.069.242-.24.286-.403.08-.282.069-.554-.035-.816a1.831 1.831 0 00-.112-.21 1.95 1.95 0 01-.108-.211 1.833 1.833 0 00-.02-.744c-.16-.667-.796-1.429-1.555-1.175a.81.81 0 00-.56.84c.013.111.06.243.092.364zm-2.6.49a2.111 2.111 0 01-.429.433c-.291.217-.615.349-.97.394-.157.02-.335.01-.48.014a.618.618 0 00-.315.11c-.162.104-.35.23-.53.316-.347.166-.71.25-1.09.25a8.8 8.8 0 01-.405-.007.302.302 0 00-.252.105 38.7 38.7 0 01-.397.443l-.322.215-.319.16c-.223.114-.4.269-.53.466-.119.18-.17.362-.26.626a1.477 1.477 0 01-.374.58l-.337.198a.017.017 0 00-.007.02c.012.033.032.078.058.136.11.24.135.491.077.756a.989.989 0 01-.217.414.846.846 0 01-.428.221c-.456.069-.788-.255-.978-.629l-.002-.002h-.003l-.002.002-.001.003.067.316c.028.131.095.297.17.435.054.096.104.19.15.283-.013.121-.063.2-.15.238-.1.043-.238-.005-.338-.048a15.309 15.309 0 01-.44-.2.372.372 0 01-.205-.209c-.058-.165-.107-.359-.173-.494a1.045 1.045 0 00-.588-.533.36.36 0 00-.121-.024c-.008-.013-.01-.017-.004-.012l-.03-.485c-.024-.32.024-.64.37-.756.48-.057.528.515.79.722a.013.013 0 00.015 0 .012.012 0 00.006-.012c-.044-.306-.09-.588-.296-.818a.518.518 0 00-.465-.176c-.416.047-.58.386-.622.762-.021.193-.024.371-.009.535.023.356.12.796.163 1.006.047.233.11.455.188.665.07.19.212.426.299.554.13.194.301.362.51.504.366.246.76.297 1.186.153.058.431.147.86.266 1.29.026.092.057.177.093.253.186.518.478.98.876 1.384.255.26.515.48.78.664.007.012.033.03.077.05a.6.6 0 00.106.094c.484.329 1.004.613 1.56.852l.239.103c.197.087.36.15.485.191.295.094.462.146.502.158.39.113.79.172 1.2.175.312.004.598-.049.869-.184.207-.105.424-.32.505-.553.162-.468-.062-.95-.47-1.214a1.54 1.54 0 00-.665-.287 1.85 1.85 0 00-.492-.029c.07-.11.115-.178.135-.204.133-.175.385-.209.584-.177.167.028.309.053.426.077.223.045.397-.064.352-.313a.362.362 0 00-.179-.272 2.06 2.06 0 00-.65-.223.944.944 0 00-.268-.02c-.22.028-.395.05-.524.063a.519.519 0 00-.287.152.412.412 0 01-.145-.06.216.216 0 01-.063-.107c.05-.172.161-.264.336-.275a.097.097 0 01.07.026c.162.152.362.091.557.024a.99.99 0 01.715.01c.087.034.298.125.42.112.142-.016.214-.1.215-.251a.062.062 0 00-.03-.054 4.639 4.639 0 01-.144-.1.14.14 0 01-.033-.036 3.76 3.76 0 01-.135-.218c-.085-.147-.238-.192-.397-.23a.506.506 0 01-.246-.154c.1-.196.272-.313.515-.349a.596.596 0 01.32.037c.269.11.46.186.575.227.312.11.649.115.818-.213a.6.6 0 00.086-.284c.02-.508.05-1.347.092-2.517l.039-1.008c.01-.273.084-.515.323-.668.19-.11.433-.097.645-.112.428-.03.485-.436.408-.77-.064-.356-.321-.667-.634-.838a2.432 2.432 0 00-.531-.207 3.244 3.244 0 01-.357-.148 2 2 0 01-.355-.263v-.004l.004-.002h.004l.202.091c.137.056.28.076.428.06.309-.032.485-.33.288-.6-.13-.18-.364-.324-.53-.439a2.118 2.118 0 01-.405-.376 1.616 1.616 0 01-.288-.516.004.004 0 00-.005 0l-.001.002a1.46 1.46 0 01-.241.55zm3.914.445c-.011.02-.018.064-.031.082-.183.239-.33.338-.595.532a.794.794 0 00-.257.319.01.01 0 00.009.01c.105.008.212.002.322-.018.501-.093.61-.479.567-.921l-.002-.005a.008.008 0 00-.009-.002.008.008 0 00-.004.003zM3.266 7.891c-.048.295-.042.593.018.893.09.507.311 1.007.687 1.366.048.046.106.09.175.13.014.014.028.018.042.011.107-.05.223-.125.35-.223a.031.031 0 00.012-.025v-.09a.071.071 0 00-.066-.07.834.834 0 01-.453-.177 1.05 1.05 0 01-.356-.521 6.047 6.047 0 00-.07-.203l.017-.145a.007.007 0 01.015 0c.036.23.177.527.395.634a.29.29 0 00.309-.025.779.779 0 00.173-.35.079.079 0 00-.032-.081c-.355-.24-.385-.6-.078-.88.358-.325.868-.352 1.273-.125.034.019.087.053.157.104.05.036.102.072.155.107.075.074.17.158.285.25.2.163.408.24.67.25.411.017.748-.127 1.01-.432.127-.147.187-.294.292-.512a.919.919 0 01.611-.511.007.007 0 00-.004-.01l-.318-.069a1.755 1.755 0 00-.358-.045 1.02 1.02 0 00-.534.081c-.196.09-.357.28-.555.334-.267.073-.471-.005-.611-.233a.34.34 0 01-.034-.158l.009-.007c.213.276.51.121.558-.178a.66.66 0 00-.106-.473c-.213-.316-.518-.463-.916-.441-.397.022-.773.248-1.022.548-.192.232-.402.547-.709.458-.226-.066-.235-.278-.19-.477a.01.01 0 00-.015-.01c-.09.056-.165.115-.224.176a2.093 2.093 0 00-.562 1.129zm3.221 1.627a.99.99 0 00.174.337c.054.068.115.132.181.193.08.157.178.264.293.321.26.13.554.024.658-.251.078-.237.047-.444-.092-.62a.626.626 0 00-.435-.239 2.97 2.97 0 00-.527.017l-.35-.21a.01.01 0 00-.011 0 .01.01 0 00-.005.011c.016.16.054.307.114.44zM4.23 10.86a1.16 1.16 0 00-.08.132c-.074.158-.109.323-.102.495.01.26.196.672.528.6a.343.343 0 00.249-.202.3.3 0 00.042-.15 1.418 1.418 0 00-.093-.4 2.793 2.793 0 01-.16-.754.036.036 0 00-.008-.019.017.017 0 00-.01-.006c-.004-.001-.008 0-.012.002a1.674 1.674 0 00-.354.302zm.109 2.45a1.921 1.921 0 00.337.464c.002.001.003.002.005.001a.013.013 0 00.01-.004l.08-.096a1.21 1.21 0 01.622-.114c.03.003.06.005.091.004l.15.082c.084.057.17.115.257.176a2.12 2.12 0 01.272.259h.003a.005.005 0 00.002-.002v-.003a1.983 1.983 0 00-.133-.625 1.117 1.117 0 00-.635-.61 33.41 33.41 0 00-.275-.285.482.482 0 00-.413-.152.752.752 0 01-.516-.07.01.01 0 00-.008.004 1.79 1.79 0 00.151.971zm2.137 1.902c.006.025.008.037.006.037.023.307.058.602.106.887.045.443.092.81.14 1.1l.419 2.536c.092.552.126.872.185 1.457.022.214.095.407.218.578.203.283.438.53.705.744.459.367.97.646 1.534.838.444.168.906.28 1.385.339.476.054.963.053 1.461-.004.395-.038.794-.12 1.196-.245.119-.038.304-.104.554-.2a.891.891 0 00.114-.06.588.588 0 00.128-.046c.311-.15.613-.308.905-.473a2.43 2.43 0 00.276-.185v-.007a.012.012 0 00-.005-.005.011.011 0 00-.007-.002c-.716.065-1.372-.067-2.035-.383-.574-.273-1.088-.718-1.552-1.21a26.147 26.147 0 01-1.289-1.46l-2.286-2.761a9.577 9.577 0 01-.753-1.023 4.608 4.608 0 01-.577-1.342l-.147-.61a.018.018 0 00-.019-.014c-.45.038-.68.566-.688.958a3.17 3.17 0 00.026.556zm-1.47-1.214a.506.506 0 00-.027.551.638.638 0 00.231.251.105.105 0 00.088.011c.302-.095.58-.148.887-.034h.004a.008.008 0 00.004-.002.007.007 0 00.001-.008 1.908 1.908 0 00-.302-.475 1.227 1.227 0 00-.432-.357.533.533 0 00-.21-.065c-.098-.003-.18.04-.244.128zm.96 2.255a.76.76 0 00.244.08l.14.016a.033.033 0 00.031-.018 2.273 2.273 0 00-.07-.799.02.02 0 00-.007-.01.019.019 0 00-.012-.004c-.179.008-.356.033-.533.074-.009-.01-.023-.016-.043-.018a1.019 1.019 0 01-.384-.434.012.012 0 00-.017.009c-.069.454.279.892.652 1.104z"
    }), /*#__PURE__*/_jsx("path", {
      d: "M7.126 3.698a2.128 2.128 0 00-.016 1.07l-.091-.023c-.31-.215-.696-.139-.966.09a.02.02 0 01-.018.005.978.978 0 00-.294-.023c.15-.23.352-.383.604-.457.083-.024.187-.044.31-.058a.023.023 0 00.018-.015c.091-.24.242-.436.453-.59zM14.187 8.01c.415.181.695.507.724.97a.035.035 0 01-.009.025l-.018.021c-.241.232-.463.462-.735.66-.23.165-.56.315-.849.297a.335.335 0 01-.24-.112.968.968 0 01-.038-.37.043.043 0 00-.038-.047.448.448 0 01-.148-.045c.06-.134.094-.251.102-.352.029-.327-.238-.5-.53-.538a1.515 1.515 0 00-.7.074c-.1.03-.193.073-.279.126a.756.756 0 00-.164.14 9.013 9.013 0 00-.144.164.02.02 0 00-.004.012c0 .005.002.01.005.013.015.016.022.031.021.046l-.178.074-.172-.031a6.66 6.66 0 00-.755-.142 3.515 3.515 0 01-.465-.116c.215.01.404 0 .565-.033a2.67 2.67 0 00.502-.156c.564-.236.991-.43 1.538-.496a.041.041 0 00.035-.032c.047-.211.19-.285.397-.327.138-.029.313-.035.526-.02.378.028.715.066 1.05.196z"
    })]
  }));
});
export default Icon;