# OAuth 回调地址问题修复报告

## 问题描述

在测试 Nodeloc OAuth 登录时遇到错误：
```json
{
  "error": "invalid_request",
  "error_description": "The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed. The 'redirect_uri' parameter is required when using OpenID Connect 1.0."
}
```

## 问题原因

1. **缺少 redirect_uri 参数**：OAuth授权URL中没有包含必需的 `redirect_uri` 参数
2. **OpenID Connect 要求**：Nodeloc 使用 OpenID Connect 1.0，该协议强制要求 `redirect_uri` 参数

## 修复内容

### 1. 添加 redirect_uri 参数

**修复前的代码**：
```javascript
window.open(
  `https://conn.nodeloc.cc/oauth2/auth?response_type=code&client_id=${nodeloc_client_id}&state=${state}&scope=openid profile`,
);
```

**修复后的代码**：
```javascript
const redirect_uri = `${window.location.origin}/oauth/nodeloc`;
window.open(
  `https://conn.nodeloc.cc/oauth2/auth?response_type=code&client_id=${nodeloc_client_id}&redirect_uri=${encodeURIComponent(redirect_uri)}&state=${state}&scope=openid profile`,
);
```

### 2. 统一所有 OAuth 提供商

为了保持一致性，也为其他 OAuth 提供商添加了 `redirect_uri` 参数：

- **GitHub OAuth**：添加 `redirect_uri=/oauth/github`
- **OIDC OAuth**：确认已有 `redirect_uri=/oauth/oidc`
- **Nodeloc OAuth**：添加 `redirect_uri=/oauth/nodeloc`

### 3. 正确的回调流程

```mermaid
graph TD
    A[用户点击登录] --> B[构建授权URL]
    B --> C[包含redirect_uri参数]
    C --> D[重定向到Nodeloc授权页面]
    D --> E[用户授权]
    E --> F[Nodeloc回调到前端路由]
    F --> G[前端OAuth2Callback组件]
    G --> H[调用后端API /api/oauth/nodeloc]
    H --> I[后端处理OAuth流程]
    I --> J[返回登录结果]
```

## 修复的文件

### 前端文件
- `web/src/helpers/api.js`
  - 修复 `onNodelocOAuthClicked` 函数
  - 修复 `onGitHubOAuthClicked` 函数
  - 确认 `onOIDCClicked` 函数正确

### 文档文件
- `NODELOC_CALLBACK_SETUP.md` - 更新回调流程说明
- `OAUTH_CALLBACK_FIX.md` - 本修复报告

## 正确的配置

### 在 Nodeloc OAuth 应用中设置
```
回调地址: https://yourdomain.com/oauth/nodeloc
```

### 本地开发测试
```
回调地址: http://localhost:3000/oauth/nodeloc
```

## OAuth 参数说明

### 必需参数
- `response_type=code` - 授权码模式
- `client_id` - 应用的客户端ID
- `redirect_uri` - 回调地址（必须与注册时一致）
- `state` - CSRF防护参数
- `scope=openid profile` - 请求的权限范围

### 完整的授权URL示例
```
https://conn.nodeloc.cc/oauth2/auth?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=https%3A//yourdomain.com/oauth/nodeloc&state=RANDOM_STATE&scope=openid%20profile
```

## 测试验证

修复后请测试以下场景：

1. **正常登录流程**
   - 点击"使用 Nodeloc 继续"
   - 正确跳转到授权页面
   - 授权后正确回调到应用

2. **错误处理**
   - 用户拒绝授权
   - 网络错误
   - 无效的授权码

3. **安全验证**
   - State参数验证
   - CSRF防护
   - 重复提交防护

## 注意事项

1. **URL编码**：redirect_uri 必须进行 URL 编码
2. **完全匹配**：回调地址必须与注册时完全一致
3. **HTTPS要求**：生产环境必须使用 HTTPS
4. **域名验证**：确保回调地址指向你控制的域名

修复完成后，Nodeloc OAuth 登录应该可以正常工作了！
