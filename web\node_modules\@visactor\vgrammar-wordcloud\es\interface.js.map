{"version": 3, "sources": ["../src/interface.ts"], "names": [], "mappings": "", "file": "interface.js", "sourcesContent": ["export type TagItemAttribute<T> = T | ((d?: any) => T);\n\nexport type TagItemFunction<T> = (d?: any) => T;\n\nexport type Bounds = [{ x: number; y: number }, { x: number; y: number }];\nexport interface Rect {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n}\n\nexport interface IBaseLayoutOptions {\n  text?: TagItemAttribute<string | number>;\n  fontFamily?: TagItemAttribute<string>;\n  fontWeight?: TagItemAttribute<string>;\n  fontSize?: TagItemAttribute<number>;\n  fontStyle?: TagItemAttribute<string>;\n\n  color?: 'random-dark' | 'random-light' | TagItemAttribute<string>;\n\n  drawOutOfBound?: boolean;\n  /**\n   * shrink to fit when the words are to many or to great\n   */\n  shrink?: boolean;\n  /** clip text to fit */\n  clip?: boolean;\n  minFontSize?: number;\n\n  useRandomRotate?: boolean;\n  minRotation?: number;\n  maxRotation?: number;\n  rotationSteps?: number;\n  rotateRatio?: number;\n  rotate?: TagItemAttribute<number>;\n\n  random?: boolean;\n  shape?: string | ((theta: number) => number);\n  progressiveTime?: number;\n  backgroundColor?: string;\n  outputCallback?: (res: any[]) => any[];\n  progressiveStep?: number;\n}\n\n/** the output type of layout */\nexport interface TagOutputItem {\n  /** original input data */\n  datum: any;\n  x: number;\n  y: number;\n  fontFamily: string;\n  fontSize: number;\n  fontStyle: string;\n  fontWeight: string;\n  angle: number;\n  width: number;\n  height: number;\n  text: string;\n  color?: string;\n}\n"]}