"use strict";

var StateValue;

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.DEFAULT_HTML_TEXT_SPEC = exports.DEFAULT_STATES = exports.StateValue = exports.DEFAULT_TEXT_FONT_SIZE = exports.DEFAULT_TEXT_FONT_FAMILY = exports.POLAR_END_ANGLE = exports.POLAR_START_ANGLE = void 0, 
exports.POLAR_START_ANGLE = -.5 * Math.PI, exports.POLAR_END_ANGLE = 1.5 * Math.PI, 
exports.DEFAULT_TEXT_FONT_FAMILY = "PingFang SC,Microsoft Yahei,system-ui,-apple-system,segoe ui,Roboto,Helvetica,Arial,sans-serif, apple color emoji,segoe ui emoji,segoe ui symbol", 
exports.DEFAULT_TEXT_FONT_SIZE = 11, function(StateValue) {
    StateValue.selected = "selected", StateValue.selectedReverse = "selected_reverse", 
    StateValue.hover = "hover", StateValue.hoverReverse = "hover_reverse";
}(StateValue = exports.StateValue || (exports.StateValue = {})), exports.DEFAULT_STATES = {
    [StateValue.selectedReverse]: {},
    [StateValue.selected]: {},
    [StateValue.hover]: {},
    [StateValue.hoverReverse]: {}
}, exports.DEFAULT_HTML_TEXT_SPEC = {
    container: "",
    width: 30,
    height: 30,
    style: {}
};