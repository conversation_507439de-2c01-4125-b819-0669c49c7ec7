{"version": 3, "sources": ["../src/error.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,OAAe,EAAE,EAAE;IACvC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;IAEpC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxB,CAAC,CAAC", "file": "error.js", "sourcesContent": ["/* Adapted from vega by University of Washington Interactive Data Lab\n * https://vega.github.io/vega/\n * Licensed under the BSD-3-Clause\n\n * url: https://github.com/vega/vega/blob/main/packages/vega-util/src/error.js\n * License: https://github.com/vega/vega/blob/main/LICENSE\n * @license\n */\n\nimport { Logger } from '@visactor/vutils';\n\nexport const error = (message: string) => {\n  const logger = Logger.getInstance();\n\n  logger.error(message);\n};\n"]}