{"version": 3, "sources": ["../src/view-box.ts"], "names": [], "mappings": "AAEA,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,MAAqB,EAAE,EAAE;IACpD,OAAO,OAAO,IAAI,MAAM;QACtB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;QACnG,CAAC,CAAC;YACE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YAClC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YAClC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YAClC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YAClC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;SACxC,CAAC;AACR,CAAC,CAAC", "file": "view-box.js", "sourcesContent": ["import type { LayoutViewBox } from './types';\n\nexport const parseViewBox = (config: LayoutViewBox) => {\n  return 'width' in config\n    ? { x0: 0, x1: config.width, y0: 0, y1: config.height, width: config.width, height: config.height }\n    : {\n        x0: Math.min(config.x0, config.x1),\n        x1: Math.max(config.x0, config.x1),\n        y0: Math.min(config.y0, config.y1),\n        y1: Math.max(config.y0, config.y1),\n        width: Math.abs(config.x1 - config.x0),\n        height: Math.abs(config.y1 - config.y0)\n      };\n};\n"]}