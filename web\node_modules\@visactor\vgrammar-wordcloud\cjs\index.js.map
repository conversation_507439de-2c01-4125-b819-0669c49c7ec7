{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,2DAAkD;AAElD,2CAAwC;AAEjC,MAAM,2BAA2B,GAAG,GAAG,EAAE;IAC9C,uBAAO,CAAC,iBAAiB,CACvB,WAAW,EACX;QACE,SAAS,EAAT,qBAAS;QACT,SAAS,EAAE,YAAY;KACxB,EACD,IAAI,CACL,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,2BAA2B,+BAStC;AAEF,mCAAkC;AAAzB,gGAAA,MAAM,OAAA", "file": "index.js", "sourcesContent": ["import { Factory } from '@visactor/vgrammar-core';\n\nimport { transform } from './wordcloud';\n\nexport const registerWordCloudTransforms = () => {\n  Factory.registerTransform(\n    'wordcloud',\n    {\n      transform,\n      markPhase: 'beforeJoin'\n    },\n    true\n  );\n};\n\nexport { shapes } from './shapes';\n"]}