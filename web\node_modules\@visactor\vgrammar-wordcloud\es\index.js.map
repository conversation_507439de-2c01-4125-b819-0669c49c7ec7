{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,MAAM,CAAC,MAAM,2BAA2B,GAAG,GAAG,EAAE;IAC9C,OAAO,CAAC,iBAAiB,CACvB,WAAW,EACX;QACE,SAAS;QACT,SAAS,EAAE,YAAY;KACxB,EACD,IAAI,CACL,CAAC;AACJ,CAAC,CAAC;AAEF,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC", "file": "index.js", "sourcesContent": ["import { Factory } from '@visactor/vgrammar-core';\n\nimport { transform } from './wordcloud';\n\nexport const registerWordCloudTransforms = () => {\n  Factory.registerTransform(\n    'wordcloud',\n    {\n      transform,\n      markPhase: 'beforeJoin'\n    },\n    true\n  );\n};\n\nexport { shapes } from './shapes';\n"]}