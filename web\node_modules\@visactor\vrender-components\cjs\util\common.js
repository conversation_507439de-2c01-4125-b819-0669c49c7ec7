"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.getNoneGroupMarksByName = exports.getMarksByName = exports.isVisible = exports.traverseGroup = void 0;

const vutils_1 = require("@visactor/vutils");

function traverseGroup(group, cb) {
    group.forEachChildren((node => {
        const stopped = cb(node);
        node.isContainer && !stopped && traverseGroup(node, cb);
    }));
}

exports.traverseGroup = traverseGroup;

const isVisible = obj => !(0, vutils_1.isNil)(obj) && !1 !== obj.visible;

function getMarksByName(root, name) {
    if (!name) return [];
    const group = root.find((node => node.name === name), !0);
    return group ? group.getChildren() : [];
}

function getNoneGroupMarksByName(root, name) {
    if (!name) return [];
    const group = root.find((node => node.name === name), !0);
    return group ? group.findAll((node => "group" !== node.type), !0) : [];
}

exports.isVisible = isVisible, exports.getMarksByName = getMarksByName, exports.getNoneGroupMarksByName = getNoneGroupMarksByName;
//# sourceMappingURL=common.js.map
