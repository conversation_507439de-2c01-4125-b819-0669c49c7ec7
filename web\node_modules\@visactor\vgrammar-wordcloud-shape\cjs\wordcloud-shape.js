"use strict";

var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))((function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator.throw(value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            var value;
            result.done ? resolve(result.value) : (value = result.value, value instanceof P ? value : new P((function(resolve) {
                resolve(value);
            }))).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    }));
}, __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        default: mod
    };
};

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.transform = void 0;

const vutils_1 = require("@visactor/vutils"), vgrammar_util_1 = require("@visactor/vgrammar-util"), vrender_core_1 = require("@visactor/vrender-core"), segmentation_1 = require("./segmentation"), vscale_1 = require("@visactor/vscale"), cloud_shape_layout_1 = __importDefault(require("./cloud-shape-layout")), util_1 = require("./util"), OUTPUT = {
    x: "x",
    y: "y",
    fontFamily: "fontFamily",
    fontSize: "fontSize",
    fontStyle: "fontStyle",
    fontWeight: "fontWeight",
    angle: "angle",
    opacity: "opacity",
    visible: "visible",
    isFillingWord: "isFillingWord",
    color: "color"
}, transform = (options, upstreamData, parameters, view) => __awaiter(void 0, void 0, void 0, (function*() {
    var _a, _b, _c;
    if (!options.size || (0, vutils_1.isNil)(options.size[0]) || (0, vutils_1.isNil)(options.size[1]) || options.size[0] <= 0 || options.size[1] <= 0) {
        return vutils_1.Logger.getInstance().info("Wordcloud size dimensions must be greater than 0"), 
        [];
    }
    options.size = [ Math.ceil(options.size[0]), Math.ceil(options.size[1]) ], options.shape || (0, 
    vgrammar_util_1.error)("WordcloudShape shape must be specified."), options.text || (0, 
    vgrammar_util_1.error)("WordcloudShape text must be specified."), (null == view ? void 0 : view.emit) && view.emit(util_1.WORDCLOUD_SHAPE_HOOK_EVENT.BEFORE_WORDCLOUD_SHAPE_LAYOUT);
    const data = upstreamData, as = options.as || OUTPUT;
    if (!data || 0 === data.length) return [];
    const segmentationInput = {
        shapeUrl: options.shape,
        size: options.size,
        ratio: options.ratio || .8,
        tempCanvas: void 0,
        tempCtx: void 0,
        removeWhiteBorder: options.removeWhiteBorder || !1,
        boardSize: [ 0, 0 ],
        random: !1,
        randomGenerator: void 0
    }, tempCanvas = vrender_core_1.vglobal.createCanvas({
        width: options.size[0],
        height: options.size[1]
    }), tempCtx = tempCanvas.getContext("2d");
    tempCtx.textAlign = "center", tempCtx.textBaseline = "middle", segmentationInput.tempCanvas = tempCanvas, 
    segmentationInput.tempCtx = tempCtx;
    const boardW = options.size[0] + 31 >> 5 << 5;
    segmentationInput.boardSize = [ boardW, options.size[1] ], segmentationInput.random ? segmentationInput.randomGenerator = Math.random : segmentationInput.randomGenerator = (0, 
    util_1.fakeRandom)();
    const shapeImage = yield (0, segmentation_1.loadAndHandleImage)(segmentationInput);
    if (!shapeImage) return [];
    const segmentationOutput = (0, segmentation_1.segmentation)(shapeImage, segmentationInput), colorMode = options.colorMode || "ordinal", wordsConfig = {
        getText: field(options.text),
        getFontSize: field(options.fontSize),
        fontSizeRange: options.fontSizeRange,
        colorMode: colorMode,
        getColor: options.colorField ? field(options.colorField) : field(options.text),
        getFillingColor: field(options.fillingColorField),
        colorList: options.colorList || ("ordinal" === colorMode ? [ "#2E62F1" ] : [ "#537EF5", "#2E62F1", "#2358D8", "#184FBF", "#0C45A6", "#013B8E" ]),
        getColorHex: field(options.colorHexField),
        getFontFamily: field(options.fontFamily || "sans-serif"),
        rotateList: options.rotateList || [ 0 ],
        getPadding: field(options.padding || 1),
        getFontStyle: field(options.fontStyle || "normal"),
        getFontWeight: field(options.fontWeight || "normal"),
        getFontOpacity: options.fontOpacity ? field(options.fontOpacity) : () => 1
    };
    initFontSizeScale(data, wordsConfig, segmentationOutput);
    const layoutConfig = {
        size: options.size,
        ratio: options.ratio || .8,
        shapeUrl: options.shape,
        random: void 0 === options.random || options.random,
        textLayoutTimes: options.textLayoutTimes || 3,
        removeWhiteBorder: options.removeWhiteBorder || !1,
        layoutMode: options.layoutMode || "default",
        fontSizeShrinkFactor: options.fontSizeShrinkFactor || .8,
        stepFactor: options.stepFactor || 1,
        importantWordCount: options.importantWordCount || 10,
        globalShinkLimit: options.globalShinkLimit || .2,
        fontSizeEnlargeFactor: options.fontSizeEnlargeFactor || 1.5,
        fillingRatio: options.fillingRatio || .7,
        fillingTimes: options.fillingTimes || 4,
        fillingXStep: options.fillingXRatioStep ? Math.max(Math.floor(options.size[0] * options.fillingXRatioStep), 1) : options.fillingXStep || 4,
        fillingYStep: options.fillingYRatioStep ? Math.max(Math.floor(options.size[1] * options.fillingYRatioStep), 1) : options.fillingYStep || 4,
        fillingInitialFontSize: options.fillingInitialFontSize,
        fillingDeltaFontSize: options.fillingDeltaFontSize,
        fillingInitialOpacity: options.fillingInitialOpacity || .8,
        fillingDeltaOpacity: options.fillingDeltaOpacity || .05,
        getFillingFontFamily: field(options.fillingFontFamily || "sans-serif"),
        getFillingFontStyle: field(options.fillingFontStyle || "normal"),
        getFillingFontWeight: field(options.fillingFontWeight || "normal"),
        getFillingPadding: field(options.fillingPadding || .4),
        fillingRotateList: options.fillingRotateList || [ 0, 90 ],
        fillingDeltaFontSizeFactor: options.fillingDeltaFontSizeFactor || .2,
        fillingColorList: options.fillingColorList || [ "#537EF5" ],
        sameColorList: !1,
        minInitFontSize: options.minInitFontSize || 10,
        minFontSize: options.minFontSize || 4,
        minFillFoontSize: options.minFillFoontSize || 2
    }, sameColorList = (0, util_1.colorListEqual)(wordsConfig.colorList, layoutConfig.fillingColorList);
    layoutConfig.sameColorList = sameColorList, initColorScale(data, wordsConfig, layoutConfig, options), 
    initFillingWordsFontSize(data, wordsConfig, layoutConfig, segmentationOutput);
    const {getText: getText, getFontFamily: getFontFamily, getFontStyle: getFontStyle, getFontWeight: getFontWeight, getPadding: getPadding, getColor: getColor, getFillingColor: getFillingColor, getColorHex: getColorHex, fontSizeScale: fontSizeScale, colorScale: colorScale, fillingColorScale: fillingColorScale, getFontOpacity: getFontOpacity, rotateList: rotateList} = wordsConfig, words = data.map((datum => {
        var _a, _b;
        return {
            x: 0,
            y: 0,
            weight: 0,
            text: getText(datum),
            fontFamily: getFontFamily(datum),
            fontWeight: getFontWeight(datum),
            fontStyle: getFontStyle(datum),
            rotate: rotateList[~~(segmentationInput.randomGenerator() * rotateList.length)],
            fontSize: Math.max(layoutConfig.minInitFontSize, ~~fontSizeScale(datum)),
            opacity: getFontOpacity(datum),
            padding: getPadding(datum),
            color: getColorHex && getColorHex(datum) || colorScale && colorScale(getColor(datum)) || "black",
            fillingColor: !getFillingColor || (null === (_a = options.colorField) || void 0 === _a ? void 0 : _a.field) === (null === (_b = options.fillingColorField) || void 0 === _b ? void 0 : _b.field) && sameColorList ? void 0 : getColorHex && getColorHex(datum) || fillingColorScale && fillingColorScale(getFillingColor(datum)) || "black",
            datum: datum,
            visible: !0,
            hasPlaced: !1
        };
    })), wordsMaxFontSize = (0, vutils_1.maxInArray)(words.map((word => word.fontSize)));
    words.forEach((word => word.weight = word.fontSize / wordsMaxFontSize)), words.sort(((a, b) => b.weight - a.weight));
    const {fillingWords: fillingWords, successedWords: successedWords, failedWords: failedWords} = (0, 
    cloud_shape_layout_1.default)(words, layoutConfig, segmentationOutput);
    let w, t;
    const modKeywords = [];
    for (let i = 0; i < words.length; ++i) w = words[i], t = w.datum, t[as.x] = w.x, 
    t[as.y] = w.y, t[as.fontFamily] = w.fontFamily, t[as.fontSize] = w.fontSize, t[as.fontStyle] = w.fontStyle, 
    t[as.fontWeight] = w.fontWeight, t[as.angle] = (0, vutils_1.degreeToRadian)(w.rotate), 
    t[as.opacity] = w.opacity, t[as.visible] = w.visible, t[as.isFillingWord] = !1, 
    t[as.color] = w.color, modKeywords.push(t);
    const textKey = null !== (_b = null === (_a = options.text) || void 0 === _a ? void 0 : _a.field) && void 0 !== _b ? _b : "textKey", dataIndexKey = null !== (_c = options.dataIndexKey) && void 0 !== _c ? _c : "defaultDataIndexKey", fillingWordsData = [];
    return fillingWords.forEach(((word, index) => {
        var _a, _b;
        const t = {};
        t[as.x] = word.x, t[as.y] = word.y, t[as.fontFamily] = word.fontFamily, t[as.fontSize] = word.fontSize, 
        t[as.fontStyle] = word.fontStyle, t[as.fontWeight] = word.fontWeight, t[as.angle] = (0, 
        vutils_1.degreeToRadian)(word.rotate), t[as.opacity] = word.opacity, t[as.visible] = word.visible, 
        t[as.isFillingWord] = !0, t[as.color] = getFillingColor ? (null === (_a = options.colorField) || void 0 === _a ? void 0 : _a.field) === (null === (_b = options.fillingColorField) || void 0 === _b ? void 0 : _b.field) && sameColorList ? word.color : word.fillingColor : layoutConfig.fillingColorList[~~(segmentationInput.randomGenerator() * layoutConfig.fillingColorList.length)], 
        t[textKey] = word.text, t[dataIndexKey] = `${word.text}_${index}_fillingWords`, 
        fillingWordsData.push(t);
    })), (null == view ? void 0 : view.emit) && view.emit(util_1.WORDCLOUD_SHAPE_HOOK_EVENT.AFTER_WORDCLOUD_SHAPE_LAYOUT, {
        successedWords: successedWords,
        failedWords: failedWords
    }), modKeywords.concat(fillingWordsData);
}));

exports.transform = transform;

const initColorScale = (data, wordsConfig, layoutConfig, options) => {
    var _a, _b, _c, _d;
    const {colorMode: colorMode, getColor: getColor, getFillingColor: getFillingColor} = wordsConfig, {sameColorList: sameColorList} = layoutConfig;
    let colorScale, fillingColorScale, colorList = wordsConfig.colorList, fillingColorList = layoutConfig.fillingColorList;
    if ("ordinal" === colorMode) {
        const uniqueColorField = data.map((word => getColor(word)));
        if (colorScale = datum => (new vscale_1.OrdinalScale).domain(uniqueColorField).range(colorList).scale(datum), 
        getFillingColor && ((null === (_a = options.colorField) || void 0 === _a ? void 0 : _a.field) !== (null === (_b = options.fillingColorField) || void 0 === _b ? void 0 : _b.field) || !sameColorList)) {
            const uniquefillingColorField = data.map((datum => getFillingColor(datum)));
            fillingColorScale = datum => (new vscale_1.OrdinalScale).domain(uniquefillingColorField).range(fillingColorList).scale(datum);
        }
    } else {
        1 === colorList.length && (colorList = [ colorList[0], colorList[0] ]);
        const valueScale = (new vscale_1.LinearScale).domain(extent(getColor, data)).range(colorList);
        if (colorScale = i => valueScale.scale(i), getFillingColor && ((null === (_c = options.colorField) || void 0 === _c ? void 0 : _c.field) !== (null === (_d = options.fillingColorField) || void 0 === _d ? void 0 : _d.field) || !sameColorList)) {
            1 === fillingColorList.length && (fillingColorList = [ fillingColorList[0], fillingColorList[0] ]);
            const fillingValueScale = (new vscale_1.LinearScale).domain(extent(getFillingColor, data)).range(fillingColorList);
            fillingColorScale = i => fillingValueScale.scale(i);
        }
    }
    Object.assign(wordsConfig, {
        colorScale: colorScale,
        fillingColorScale: fillingColorScale
    });
}, initFontSizeScale = (data, wordsConfig, segmentationOutput) => {
    let {fontSizeRange: range} = wordsConfig;
    const {getFontSize: getFontSize, getText: getText} = wordsConfig;
    let fontSizeScale;
    if (getFontSize) {
        if (getFontSize && range) {
            const sizeScale = (new vscale_1.SqrtScale).domain(extent(getFontSize, data)).range(range);
            fontSizeScale = datum => sizeScale.scale(getFontSize(datum));
        } else if (getFontSize && (0, vutils_1.isFunction)(getFontSize) && !range) {
            const a = .5, [min, max] = extent(getFontSize, data), words = data.map((datum => ({
                text: getText(datum),
                value: getFontSize(datum),
                weight: max === min ? 1 : (getFontSize(datum) - min) / (max - min)
            }))), x = getInitialFontSize(words, segmentationOutput, !0);
            range = [ ~~(a * x), ~~x ];
            const sizeScale = (new vscale_1.SqrtScale).domain(extent(getFontSize, data)).range(range);
            fontSizeScale = datum => sizeScale.scale(getFontSize(datum));
        }
    } else {
        const words = data.map((word => ({
            text: getText(word)
        }))), x = getInitialFontSize(words, segmentationOutput, !1);
        fontSizeScale = (0, util_1.functor)(x);
    }
    Object.assign(wordsConfig, {
        getFontSize: getFontSize,
        fontSizeRange: range,
        fontSizeScale: fontSizeScale
    });
}, getInitialFontSize = (words, segmentationOutput, weight) => {
    const shapeArea = segmentationOutput.shapeArea, ratio = segmentationOutput.ratio, regions = segmentationOutput.segmentation.regions, shapeSizeLimitTextLength = Math.ceil(Math.sqrt(shapeArea) / 12), wordArea = words.reduce(((acc, word) => {
        const textLength = (0, util_1.calTextLength)(word.text);
        return textLength < shapeSizeLimitTextLength ? acc + textLength * (weight ? (.5 + .5 * word.weight) ** 2 : 1) : acc;
    }), 0);
    if (0 === wordArea) return 12;
    let x = 20;
    if (1 === regions.length) x = Math.sqrt(ratio * (shapeArea / (1.7 * wordArea))); else {
        const xArr = [];
        for (let i = 0; i < regions.length; i++) {
            const regionArea = regions[i].area, regionAspect = regions[i].ratio, regionRatio = regionArea / shapeArea;
            if (regionRatio < .1) continue;
            const regionWordArea = regionRatio * (wordArea * (regionAspect < 1 ? 2.7 - regionAspect : 1.7)), x = Math.sqrt(ratio * (regionArea / regionWordArea));
            xArr.push(x);
        }
        x = xArr.length ? Math.min(...xArr) : Math.sqrt(ratio * (shapeArea / (1.7 * wordArea)));
    }
    return x;
};

function initFillingWordsFontSize(data, wordsConfig, layoutConfig, segmentationOutput) {
    const {getText: getText} = wordsConfig;
    let {fillingInitialFontSize: fillingInitialFontSize, fillingDeltaFontSize: fillingDeltaFontSize} = layoutConfig;
    const {fillingRatio: fillingRatio} = layoutConfig, shapeSizeLimitTextLength = Math.ceil(Math.sqrt(segmentationOutput.shapeArea) / 4);
    if (!fillingInitialFontSize || !fillingDeltaFontSize) {
        const a = fillingRatio / 100, averageLength = data.reduce(((acc, word) => {
            const length = (0, util_1.calTextLength)(getText(word));
            return length > shapeSizeLimitTextLength ? acc : acc + length;
        }), 0) / data.length;
        let fontSize;
        if (0 === averageLength) fontSize = 8; else {
            const area = .2 * segmentationOutput.shapeArea;
            fontSize = Math.sqrt(a * (area / averageLength));
        }
        fillingInitialFontSize = ~~fontSize, fillingDeltaFontSize = fontSize * layoutConfig.fillingDeltaFontSizeFactor, 
        Object.assign(layoutConfig, {
            fillingInitialFontSize: fillingInitialFontSize,
            fillingDeltaFontSize: fillingDeltaFontSize
        });
    }
}

const extent = (field, data) => {
    let min = 1 / 0, max = -1 / 0;
    const n = data.length;
    let v;
    for (let i = 0; i < n; ++i) v = (0, vutils_1.toNumber)(field(data[i])), v < min && (min = v), 
    v > max && (max = v);
    return 1 === data.length && min === max && (min -= 1e4), [ min, max ];
}, field = option => option ? "string" == typeof option || "number" == typeof option ? () => option : (0, 
vutils_1.isFunction)(option) ? option : datum => datum[option.field] : null;
//# sourceMappingURL=wordcloud-shape.js.map