import { merge, isEmpty, get } from "@visactor/vutils";

import { LinearScale } from "@visactor/vscale";

import { LegendBase } from "../base";

import { Slider } from "../../slider";

import { DEFAULT_TITLE_SPACE } from "../constant";

import { loadColorContinuousLegendComponent } from "../register";

loadColorContinuousLegendComponent();

export class ColorContinuousLegend extends LegendBase {
    constructor(attributes, options) {
        super((null == options ? void 0 : options.skipDefault) ? attributes : merge({}, ColorContinuousLegend.defaultAttributes, attributes)), 
        this.name = "colorLegend", this._onSliderChange = e => {
            this._updateColor(), this.dispatchEvent(e);
        };
    }
    setSelected(value) {
        this._slider && (this._slider.setValue(value), this._updateColor());
    }
    _renderContent() {
        const {colors: colors, slidable: slidable, layout: layout, align: align, min: min, max: max, value: value, railWidth: railWidth, railHeight: railHeight, showHandler: showHandler = !0, handlerSize: handlerSize, handlerStyle: handlerStyle, railStyle: railStyle, trackStyle: trackStyle, startText: startText, endText: endText, handlerText: handlerText, showTooltip: showTooltip, tooltip: tooltip, disableTriggerEvent: disableTriggerEvent} = this.attribute, domain = [], step = (max - min) / (colors.length - 1);
        for (let i = 0; i < colors.length; i++) domain.push(min + step * i);
        this._colorScale = (new LinearScale).domain(domain, !0).range(colors), this._color = this._getTrackColor();
        const slider = new Slider({
            x: 0,
            y: 0,
            range: {
                draggableTrack: !0
            },
            slidable: slidable,
            layout: layout,
            align: align,
            min: min,
            max: max,
            value: value,
            railWidth: railWidth,
            railHeight: railHeight,
            showHandler: showHandler,
            handlerSize: handlerSize,
            handlerStyle: handlerStyle,
            railStyle: railStyle,
            trackStyle: Object.assign({
                fill: this._color
            }, trackStyle),
            startText: startText,
            endText: endText,
            handlerText: handlerText,
            showTooltip: showTooltip,
            tooltip: tooltip,
            disableTriggerEvent: disableTriggerEvent
        });
        this._innerView.add(slider), this._slider = slider, slider.translateTo(0 - slider.AABBBounds.x1, (this._title ? this._title.AABBBounds.height() + get(this.attribute, "title.space", DEFAULT_TITLE_SPACE) : 0) - slider.AABBBounds.y1), 
        this._updateColor();
    }
    _bindEvents() {
        this.attribute.disableTriggerEvent || this._slider && this._slider.addEventListener("change", this._onSliderChange);
    }
    _getTrackColor() {
        const {colors: colors, layout: layout} = this.attribute;
        if (isEmpty(colors)) return;
        const count = colors.length;
        if (1 === count) return colors[0];
        const stops = [];
        for (let i = 0; i < count; i++) {
            const percent = i / (count - 1);
            stops.push({
                offset: percent,
                color: colors[i]
            });
        }
        const isHorizontal = "horizontal" === layout;
        return {
            gradient: "linear",
            stops: stops,
            x0: 0,
            y0: 0,
            x1: isHorizontal ? 1 : 0,
            y1: isHorizontal ? 0 : 1
        };
    }
    _updateColor() {
        const {layout: layout = "horizontal", colors: colors, railWidth: railWidth, railHeight: railHeight} = this.attribute, {startHandler: startHandler, endHandler: endHandler, track: track} = this._slider, {startValue: startValue, endValue: endValue, startPos: startPos, endPos: endPos} = this._slider.currentValue, startHandlerColor = this._colorScale.scale(startValue), endHandlerColor = this._colorScale.scale(endValue);
        null == startHandler || startHandler.setAttribute("fill", startHandlerColor), null == endHandler || endHandler.setAttribute("fill", endHandlerColor);
        const railLen = "horizontal" === layout ? railWidth : railHeight;
        if (Math.abs(startPos - endPos) !== railLen && colors && colors.length > 1) {
            const stops = this._color.stops, start = Math.min(startPos, endPos), end = Math.max(startPos, endPos), startRatio = start / railLen, endRatio = end / railLen, range = endRatio - startRatio, betweenStops = stops.filter((stop => stop.offset > startRatio && stop.offset < endRatio)), minValue = Math.min(startValue, endValue), maxValue = Math.max(startValue, endValue), startColor = this._colorScale.scale(minValue), endColor = this._colorScale.scale(maxValue), newStops = [ {
                offset: 0,
                color: startColor
            } ];
            betweenStops.forEach((stop => {
                newStops.push({
                    offset: (stop.offset - startRatio) / range,
                    color: stop.color
                });
            })), newStops.push({
                offset: 1,
                color: endColor
            }), track.setAttribute("fill", Object.assign(Object.assign({}, this._color), {
                stops: newStops
            }));
        }
    }
}

ColorContinuousLegend.defaultAttributes = {
    layout: "horizontal",
    title: {
        align: "start",
        space: DEFAULT_TITLE_SPACE,
        textStyle: {
            fontSize: 12,
            fontWeight: "bold",
            fill: "rgba(46, 47, 50, 1)"
        }
    },
    handlerSize: 10,
    handlerStyle: {
        lineWidth: 4,
        stroke: "#fff",
        outerBorder: {
            distance: 2,
            lineWidth: 1,
            stroke: "#ccc"
        }
    }
};
//# sourceMappingURL=color.js.map
