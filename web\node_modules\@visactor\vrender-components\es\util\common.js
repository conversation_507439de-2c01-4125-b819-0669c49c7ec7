import { isNil } from "@visactor/vutils";

export function traverseGroup(group, cb) {
    group.forEachChildren((node => {
        const stopped = cb(node);
        node.isContainer && !stopped && traverseGroup(node, cb);
    }));
}

export const isVisible = obj => !isNil(obj) && !1 !== obj.visible;

export function getMarksByName(root, name) {
    if (!name) return [];
    const group = root.find((node => node.name === name), !0);
    return group ? group.getChildren() : [];
}

export function getNoneGroupMarksByName(root, name) {
    if (!name) return [];
    const group = root.find((node => node.name === name), !0);
    return group ? group.findAll((node => "group" !== node.type), !0) : [];
}
//# sourceMappingURL=common.js.map
