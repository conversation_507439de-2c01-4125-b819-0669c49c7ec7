import { measureSprite, isCollideWithBoard, placeWordOnBoard } from "./wordle";

export function filling(words, layoutConfig, segmentationOutput) {
    const {size: size, fillingTimes: fillingTimes, fillingXStep: fillingXStep, fillingYStep: fillingYStep, getFillingFontStyle: getFillingFontStyle, getFillingFontWeight: getFillingFontWeight, getFillingFontFamily: getFillingFontFamily, fillingInitialFontSize: fillingInitialFontSize, fillingDeltaFontSize: fillingDeltaFontSize, fillingInitialOpacity: fillingInitialOpacity, fillingDeltaOpacity: fillingDeltaOpacity, fillingRotateList: fillingRotateList, getFillingPadding: getFillingPadding, random: random, board: board, minFillFoontSize: minFillFoontSize} = layoutConfig, {boardSize: boardSize, shapeBounds: shapeBounds, tempCtx: ctx, tempCanvas: canvas, randomGenerator: randomGenerator} = segmentationOutput;
    let fontSize = fillingInitialFontSize, opacity = fillingInitialOpacity;
    const placedFillingWords = [];
    for (let i = 0; i < fillingTimes; i++) filling1Time(fontSize, opacity), fontSize = Math.max(fontSize > fillingDeltaFontSize ? fontSize - fillingDeltaFontSize : fillingDeltaFontSize, minFillFoontSize), 
    opacity = opacity > fillingDeltaOpacity ? opacity - fillingDeltaOpacity : fillingDeltaOpacity;
    return placedFillingWords;
    function filling1Time(fontSize, opacity) {
        const fillingWords = words.map((word => {
            const {text: text, color: color, fillingColor: fillingColor, hasPlaced: hasPlaced, datum: datum} = word;
            return {
                x: 0,
                y: 0,
                weight: 0,
                text: text,
                fontFamily: getFillingFontFamily(datum),
                fontStyle: getFillingFontStyle(datum),
                fontWeight: getFillingFontWeight(datum),
                fontSize: fontSize,
                rotate: fillingRotateList[~~(randomGenerator() * fillingRotateList.length)],
                padding: getFillingPadding(datum),
                opacity: opacity,
                visible: !0,
                color: color,
                fillingColor: fillingColor,
                hasPlaced: hasPlaced,
                datum: datum
            };
        }));
        randomArray(fillingWords);
        let wi = 0;
        const {x1: x1, y1: y1, x2: x2, y2: y2} = shapeBounds, [startX, startY] = [ x1 + ~~(randomGenerator() * fillingXStep * 2), y1 + ~~(randomGenerator() * fillingYStep * 2) ];
        for (let y = startY; y <= y2; y += fillingYStep) for (let x = startX; x <= x2; x += fillingXStep) {
            measureSprite(canvas, ctx, fillingWords, wi);
            const word = fillingWords[wi];
            word.x = x, word.y = y;
            const {wordSize: wordSize, bounds: bounds, hasPlaced: hasPlaced} = word;
            if (!hasPlaced || !bounds) {
                ++wi === fillingWords.length && (wi = 0, random && randomArray(fillingWords));
                continue;
            }
            const {dTop: dTop, dBottom: dBottom, dLeft: dLeft, dRight: dRight} = bounds;
            word.x - dLeft < 0 || word.x + dRight > size[0] || word.y - dTop < 0 || word.y + dBottom > size[1] || word.hasText && word.sprite && !isCollideWithBoard(word, board, boardSize) && (placeWordOnBoard(word, board, boardSize), 
            placedFillingWords.push(Object.assign({}, word)), ++wi === fillingWords.length && (wi = 0, 
            random && randomArray(fillingWords)));
        }
    }
    function randomArray(words) {
        return words.sort((() => randomGenerator() - .5));
    }
}
//# sourceMappingURL=filling.js.map