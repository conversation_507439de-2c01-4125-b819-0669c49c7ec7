#!/bin/bash

# Nodeloc OAuth 调试脚本
# 适用于Docker环境

echo "=== Nodeloc OAuth 调试工具 ==="
echo ""

# 检查参数
if [ -z "$1" ]; then
    echo "用法: $0 <你的域名>"
    echo "例如: $0 http://localhost:3000"
    echo "或者: $0 https://yourdomain.com"
    exit 1
fi

DOMAIN=$1
echo "测试域名: $DOMAIN"
echo ""

echo "1. 检查OAuth配置..."
echo "================================"
curl -s "$DOMAIN/api/debug/oauth/config" | jq '.' || curl -s "$DOMAIN/api/debug/oauth/config"
echo ""

echo "2. 生成OAuth调试URL..."
echo "================================"
RESPONSE=$(curl -s "$DOMAIN/api/debug/oauth/nodeloc")
echo "$RESPONSE" | jq '.' || echo "$RESPONSE"
echo ""

echo "3. 提取OAuth URL..."
echo "================================"
OAUTH_URL=$(echo "$RESPONSE" | jq -r '.data.oauth_url' 2>/dev/null)

if [ "$OAUTH_URL" != "null" ] && [ "$OAUTH_URL" != "" ]; then
    echo "生成的OAuth URL:"
    echo "$OAUTH_URL"
    echo ""
    echo "请复制上面的URL到浏览器中测试"
    echo ""
    echo "如果URL正常，应该会跳转到Nodeloc授权页面"
    echo "如果出现错误，请检查以下配置："
    echo "- Nodeloc应用管理中的Client ID"
    echo "- 回调地址设置"
    echo "- 权限范围设置"
else
    echo "❌ 无法生成OAuth URL，请检查配置"
fi

echo ""
echo "=== 调试完成 ==="
