"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.BasePlayer = void 0;

const vutils_1 = require("@visactor/vutils"), base_1 = require("../core/base"), slider_1 = require("../slider"), controller_1 = require("./controller"), constant_1 = require("./constant"), utils_1 = require("./utils");

class BasePlayer extends base_1.AbstractComponent {
    constructor(attributes, options) {
        super((null == options ? void 0 : options.skipDefault) ? attributes : (0, vutils_1.merge)({}, BasePlayer.defaultAttributes, attributes)), 
        this._data = [], this._layoutInfo = {}, this._updateSliderAttrs = () => {
            var _a, _b, _c;
            let handlerSize;
            (0, vutils_1.isValidNumber)(this._handlerStyle.size) ? handlerSize = this._handlerStyle.size : this._handlerStyle.size && this._handlerStyle.size.length && (handlerSize = (0, 
            vutils_1.max)(this._handlerStyle.size[0], this._handlerStyle.size[1]));
            const attrs = {
                visible: this._sliderVisible,
                min: this._minIndex,
                max: this._maxIndex,
                value: null !== (_a = this.attribute.dataIndex) && void 0 !== _a ? _a : 0,
                railWidth: this._railStyle.width,
                railHeight: this._railStyle.height,
                railStyle: this._railStyle,
                trackStyle: this._trackStyle,
                handlerSize: (0, vutils_1.isValidNumber)(handlerSize) ? handlerSize : void 0,
                handlerStyle: this._handlerStyle,
                dy: this.attribute.slider.dy,
                dx: this.attribute.slider.dx,
                slidable: !0,
                range: !1,
                handlerText: {
                    visible: !1
                },
                startText: {
                    visible: !1
                },
                endText: {
                    visible: !1
                },
                disableTriggerEvent: this.attribute.disableTriggerEvent
            };
            if ((0, utils_1.isHorizontal)(this._orient)) {
                const railWidth = Math.max(0, this._layoutInfo.slider.size), railHeight = null !== (_b = this._railStyle.height) && void 0 !== _b ? _b : constant_1.RailDefaultSize[1];
                attrs.layout = "horizontal", attrs.railHeight = railHeight, attrs.railWidth = railWidth, 
                attrs.x = this._layoutInfo.slider.x, attrs.y = this._layoutInfo.slider.y;
            } else {
                const railHeight = Math.max(0, this._layoutInfo.slider.size), railWidth = null !== (_c = this._railStyle.width) && void 0 !== _c ? _c : constant_1.RailDefaultSize[1];
                attrs.layout = "vertical", attrs.railWidth = railWidth, attrs.railHeight = railHeight, 
                attrs.x = this._layoutInfo.slider.x, attrs.y = this._layoutInfo.slider.y;
            }
            return attrs;
        }, this._initSlider = () => {
            const attrs = this._updateSliderAttrs();
            this._slider = new slider_1.Slider(attrs), this._sliderVisible && this.add(this._slider);
        }, this._updateControllerAttrs = () => {
            const attrs = {
                start: this._start,
                pause: this._pause,
                forward: this._forward,
                backward: this._backward,
                disableTriggerEvent: this.attribute.disableTriggerEvent
            };
            return (0, utils_1.isHorizontal)(this._orient) ? (attrs.layout = "horizontal", attrs.start = Object.assign(Object.assign({}, attrs.start), {
                style: Object.assign(Object.assign({}, attrs.start.style), {
                    x: this._layoutInfo.start.x,
                    y: this._layoutInfo.start.y
                })
            }), attrs.pause = Object.assign(Object.assign({}, attrs.pause), {
                style: Object.assign(Object.assign({}, attrs.pause.style), {
                    x: this._layoutInfo.start.x,
                    y: this._layoutInfo.start.y
                })
            }), attrs.backward = Object.assign(Object.assign({}, attrs.backward), {
                style: Object.assign(Object.assign({}, attrs.backward.style), {
                    x: this._layoutInfo.backward.x,
                    y: this._layoutInfo.backward.y
                })
            }), attrs.forward = Object.assign(Object.assign({}, attrs.forward), {
                style: Object.assign(Object.assign({}, attrs.forward.style), {
                    x: this._layoutInfo.forward.x,
                    y: this._layoutInfo.forward.y
                })
            })) : (attrs.layout = "vertical", attrs.start = Object.assign(Object.assign({}, attrs.start), {
                style: Object.assign(Object.assign({}, attrs.start.style), {
                    x: this._layoutInfo.start.x,
                    y: this._layoutInfo.start.y
                })
            }), attrs.pause = Object.assign(Object.assign({}, attrs.pause), {
                style: Object.assign(Object.assign({}, attrs.pause.style), {
                    x: this._layoutInfo.start.x,
                    y: this._layoutInfo.start.y
                })
            }), attrs.backward = Object.assign(Object.assign({}, attrs.backward), {
                style: Object.assign(Object.assign({}, attrs.backward.style), {
                    x: this._layoutInfo.backward.x,
                    y: this._layoutInfo.backward.y
                })
            }), attrs.forward = Object.assign(Object.assign({}, attrs.forward), {
                style: Object.assign(Object.assign({}, attrs.forward.style), {
                    x: this._layoutInfo.forward.x,
                    y: this._layoutInfo.forward.y
                })
            })), attrs;
        }, this._initController = () => {
            const attrs = this._updateControllerAttrs();
            this._controller = new controller_1.Controller(attrs), this._controllerVisible && this.add(this._controller);
        }, this._initAttributes(), this._initLayoutInfo(), this._initController(), this._initSlider();
    }
    _initAttributes() {
        this._size = this.attribute.size, this._orient = this.attribute.orient, this._data = this.attribute.data, 
        this._minIndex = 0, this._maxIndex = this._data.length - 1;
        const {slider: slider = {}, controller: controller = {}} = this.attribute;
        this._sliderVisible = slider.visible, this._railStyle = Object.assign({}, slider.railStyle), 
        this._trackStyle = Object.assign({}, slider.trackStyle), this._handlerStyle = Object.assign({}, slider.handlerStyle), 
        this._controllerVisible = controller.visible, this._start = Object.assign({}, controller.start), 
        this._pause = Object.assign({}, controller.pause), this._forward = Object.assign({}, controller.forward), 
        this._backward = Object.assign({}, controller.backward);
    }
    _initLayoutInfo() {
        var _a, _b, _c;
        const controllers = [ this._start, this._backward, this._forward ].sort(((a, b) => a.order - b.order)), startControllers = controllers.filter((d => "end" !== d.position)), endControllers = controllers.filter((d => "end" === d.position)), railSectionLength = null !== (_a = (0, 
        utils_1.isHorizontal)(this._orient) ? this._railStyle.height : this._railStyle.width) && void 0 !== _a ? _a : constant_1.RailDefaultSize[1], controllerPx = controllers.reduce(((len, controller) => {
            const size = controller.style.size, maxSize = (0, vutils_1.isNumber)(size) ? size : (0, 
            vutils_1.max)(size[0], size[1]);
            return len + controller.space + maxSize;
        }), 0), sliderPx = this._sliderVisible ? ((0, utils_1.isHorizontal)(this._orient) ? null === (_b = this._size) || void 0 === _b ? void 0 : _b.width : null === (_c = this._size) || void 0 === _c ? void 0 : _c.height) - controllerPx : 0, railPx = sliderPx - this.attribute.slider.space, startPx = startControllers.reduce(((px, controller) => {
            const {key: key, space: space, style: {size: size}} = controller, maxSize = (0, 
            vutils_1.isNumber)(size) ? size : (0, vutils_1.max)(size[0], size[1]);
            return this._layoutInfo[key] = Object.assign(Object.assign({}, this._layoutInfo[key]), {
                size: maxSize,
                x: (0, utils_1.isHorizontal)(this._orient) ? px + space : (this._size.width - maxSize) / 2,
                y: (0, utils_1.isHorizontal)(this._orient) ? (this._size.height - maxSize) / 2 : px + space
            }), px + space + maxSize;
        }), 0);
        this._layoutInfo.slider = Object.assign(Object.assign({}, this._layoutInfo.slider), {
            size: railPx,
            x: (0, utils_1.isHorizontal)(this._orient) ? startPx + this.attribute.slider.space : (this._size.width - railSectionLength) / 2,
            y: (0, utils_1.isHorizontal)(this._orient) ? (this._size.height - railSectionLength) / 2 : startPx + this.attribute.slider.space
        }), endControllers.reduce(((px, controller) => {
            const {key: key, space: space, style: {size: size}} = controller, maxSize = (0, 
            vutils_1.isNumber)(size) ? size : (0, vutils_1.max)(size[0], size[1]);
            return this._layoutInfo[key] = Object.assign(Object.assign({}, this._layoutInfo[key]), {
                size: maxSize,
                x: (0, utils_1.isHorizontal)(this._orient) ? px + space : (this._size.width - maxSize) / 2,
                y: (0, utils_1.isHorizontal)(this._orient) ? (this._size.height - maxSize) / 2 : px + space
            }), px + space + maxSize;
        }), startPx + sliderPx);
    }
    render() {
        this._initLayoutInfo(), this.renderSlider(), this.renderController();
    }
    renderSlider() {
        const attrs = this._updateSliderAttrs();
        this._slider.setAttributes(attrs);
    }
    renderController() {
        const attrs = this._updateControllerAttrs();
        this._controller.setAttributes(attrs);
    }
    dispatchCustomEvent(eventType, dataIndex) {
        this._dispatchEvent(eventType, {
            eventType: eventType,
            index: dataIndex,
            value: this._data[dataIndex]
        });
    }
}

exports.BasePlayer = BasePlayer, BasePlayer.defaultAttributes = {
    visible: !0,
    data: [],
    interval: 1e3,
    orient: "bottom",
    align: "center",
    size: {
        height: 20,
        width: 300
    },
    slider: {
        visible: !0,
        space: 10,
        dx: 0,
        dy: 0,
        railStyle: {
            cornerRadius: 5
        },
        trackStyle: {},
        handlerStyle: {}
    },
    controller: {
        visible: !0,
        start: Object.assign(Object.assign({}, constant_1.defaultControllerAttributes), {
            key: "start",
            position: "start",
            space: 0
        }),
        pause: Object.assign(Object.assign({}, constant_1.defaultControllerAttributes), {
            key: "pause",
            position: "start"
        }),
        forward: Object.assign(Object.assign({}, constant_1.defaultControllerAttributes), {
            key: "forward",
            position: "end"
        }),
        backward: Object.assign(Object.assign({}, constant_1.defaultControllerAttributes), {
            key: "backward",
            position: "start"
        })
    }
};
//# sourceMappingURL=base-player.js.map
