# Nodeloc OAuth 回调地址配置指南

## 回调地址说明

### 我们项目的回调地址
```
https://yourdomain.com/oauth/nodeloc
```

**重要说明**：这是前端路由地址，用户授权后会重定向到这个页面，然后前端会调用后端API `/api/oauth/nodeloc` 来完成OAuth流程。

### 与 PHP SDK 的区别

**PHP SDK (oauth2文件夹)**：
- 回调地址：`https://yourdomain.com/oauth2/return.php`
- 处理文件：`oauth2/return.php`

**我们的项目**：
- 回调地址：`https://yourdomain.com/oauth/nodeloc`
- 处理函数：`controller/nodeloc.go` 中的 `NodelocOAuth`

## 为什么使用不同的回调地址？

1. **统一性**：与其他OAuth提供商保持一致
   - GitHub: `/oauth/github`
   - OIDC: `/oauth/oidc`
   - Nodeloc: `/oauth/nodeloc`

2. **简洁性**：RESTful API 设计风格

3. **集成性**：与现有的路由系统完美集成

## 配置步骤

### 1. 在 Nodeloc 创建 OAuth 应用

1. 访问 [https://conn.nodeloc.cc/apps](https://conn.nodeloc.cc/apps)
2. 点击"创建新应用"
3. 填写应用信息：
   - **应用名称**：你的应用名称
   - **应用描述**：简短描述
   - **回调 URL**：`https://yourdomain.com/oauth/nodeloc`
   - **权限范围**：选择 `openid profile`

4. 保存后获取：
   - **Client ID**
   - **Client Secret**

### 2. 在系统设置中配置

1. 登录管理后台
2. 进入"系统设置"
3. 找到"Nodeloc OAuth 设置"
4. 填入：
   - **Nodeloc Client ID**：从步骤1获取的Client ID
   - **Nodeloc Client Secret**：从步骤1获取的Client Secret
5. 启用"允许通过 Nodeloc 账户登录 & 注册"
6. 保存设置

### 3. 验证配置

1. 访问登录页面
2. 应该能看到"使用 Nodeloc 继续"按钮
3. 点击按钮测试OAuth流程

## 回调地址的工作流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端页面
    participant Backend as 后端API
    participant Nodeloc as Nodeloc服务器

    User->>Frontend: 点击"使用 Nodeloc 继续"
    Frontend->>Nodeloc: 重定向到授权页面
    Note over Nodeloc: https://conn.nodeloc.cc/oauth2/auth<br/>redirect_uri=/oauth/nodeloc
    User->>Nodeloc: 用户授权
    Nodeloc->>Frontend: 回调到 /oauth/nodeloc (前端路由)
    Frontend->>Backend: 调用 /api/oauth/nodeloc
    Backend->>Nodeloc: 使用code换取token
    Nodeloc->>Backend: 返回access_token
    Backend->>Nodeloc: 使用token获取用户信息
    Nodeloc->>Backend: 返回用户信息
    Backend->>Frontend: 返回登录结果
    Frontend->>User: 登录成功，跳转到主页
```

## 常见问题

### Q: 为什么不直接使用 PHP SDK 的回调地址？
A: 因为我们的项目是 Go + React 架构，不是纯 PHP 项目。我们需要与现有的路由系统集成。

### Q: 可以同时支持两种回调地址吗？
A: 技术上可以，但不推荐。这会增加复杂性且没有必要。

### Q: 如果我想使用 PHP SDK 怎么办？
A: 你可以：
1. 单独部署 PHP SDK 到 `/oauth2/` 路径
2. 在 Nodeloc 创建另一个OAuth应用，使用 `/oauth2/return.php` 作为回调地址
3. 但这样会有两套登录系统，不推荐

### Q: 回调地址配置错误会怎样？
A: 会出现以下错误：
- `invalid_redirect_uri` 错误
- 用户授权后无法正确跳转
- OAuth流程中断

## 测试回调地址

### 本地开发测试
```
http://localhost:3000/oauth/nodeloc
```

### 生产环境
```
https://yourdomain.com/oauth/nodeloc
```

**重要提醒**：
- 回调地址必须与在 Nodeloc 应用管理中注册的地址完全一致
- 包括协议（http/https）、域名、端口、路径都必须完全匹配
- 不支持通配符或多个回调地址

## 安全注意事项

1. **HTTPS**：生产环境必须使用 HTTPS
2. **域名验证**：确保回调地址指向你控制的域名
3. **Client Secret**：妥善保管，不要泄露到前端代码中
4. **State 参数**：我们的实现已包含 CSRF 防护

配置完成后，用户就可以使用 Nodeloc 账户登录你的应用了！
