{"version": 3, "sources": ["../src/grid-layout.ts"], "names": [], "mappings": ";;;AAUA,qCAAiD;AACjD,6CAAuD;AACvD,iCAAoC;AA6BpC,MAAa,UAAW,SAAQ,iBAA8B;IAgB5D,YAAY,OAA2B;QACrC,KAAK,CAAC,IAAA,cAAK,EAAC,EAAE,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QA2L/C,eAAU,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,EAAE;YAC5C,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACpD,OAAO;aACR;YAED,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QAC1B,CAAC,CAAC;QAIM,eAAU,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,IAAc,EAAE,EAAE;YACtF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/B,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YACxB,OAAO,CAAC,EAAE,EAAE;gBACV,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE/B,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;oBACxD,SAAS;iBACV;gBAED,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;aACzB;QACH,CAAC,CAAC;QAjNA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACpC;QAGD,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;QAGrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,MAAM,MAAM,GAA+B,EAAE,CAAC;QAE9C,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAClD;QAED,OAAO,CAAC,EAAE,EAAE;YAEV,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YAG7C,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;gBAC/D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;gBAC1F,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE;aACtB,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACrC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,IAAS,EAAE,cAAsB,CAAC;;QAIpD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9G,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI,CAAC;SACb;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,MAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mCAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAGhD,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjD,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,UAAU,CAAC;QAG5F,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;QAI7F,IAAI,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3B,IAAI,SAAS,GAAG,EAAE,GAAG,CAAC,CAAC;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAMhC,MAAM,eAAe,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAIhC,MAAM,eAAe,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;QAGlC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CACnB,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAC/F,CAAC;QACF,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CACnB,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAC/F,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAClC,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEnC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;QAC1C,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC;QAG5C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QAIpB,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,UAAU,CAAC;QAQ5F,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAGtD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;QAE9D,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QAGD,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,EAAE,GAAG,GAAG,CAAC;QACb,IAAI,EAAE,CAAC;QACP,MAAM,MAAM,GAAqC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAEtF,MAAM,cAAc,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,GAAuB,EAAE,EAAE;YACzE,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YACtB,OAAO,CAAC,EAAE,EAAE;gBACV,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACtB,OAAO,CAAC,EAAE,EAAE;oBACV,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;wBACpF,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;wBAEnB,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;4BAClB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;yBAChB;wBACD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;4BAClB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;yBAChB;wBACD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;4BAClB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;yBAChB;wBACD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;4BAClB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;yBAChB;wBAED,OAAO;qBACR;iBACF;aACF;QACH,CAAC,CAAC;QACF,OAAO,EAAE,EAAE,EAAE;YACX,EAAE,GAAG,GAAG,CAAC;YACT,OAAO,EAAE,EAAE,EAAE;gBACX,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;aAClC;SACF;QAGD,OAAO;YACL,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,MAAM;YACd,EAAE,EAAE,GAAG;YACP,EAAE,EAAE,GAAG;YACP,eAAe,EAAE,eAAe;YAChC,eAAe,EAAE,eAAe;YAChC,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,QAAQ;YACR,SAAS;YACT,UAAU;YACV,UAAU;YACV,KAAK;YACL,IAAI,EAAE,IAAI;SACX,CAAC;IACJ,CAAC;IA8BO,UAAU,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,QAAoB;QAGrF,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QACxB,OAAO,CAAC,EAAE,EAAE;YACV,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;oBAChC,OAAO,KAAK,CAAC;iBACd;gBACD,SAAS;aACV;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,UAAU,CAAC,KAAa,EAAE,cAAsB,CAAC;QAE/C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAGjD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,OAAO,KAAK,CAAC;SACd;QAKD,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;YAC5B,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YACnE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAClB;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE;gBAChF,OAAO,KAAK,CAAC;aACd;SACF;QAID,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAE3B,MAAM,mBAAmB,GAAG,CAAC,GAA6B,EAAE,EAAE;YAC5D,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YAInB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACnD,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAGpB,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAG9B,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAGtC,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,EAAE;YACV,IAAI,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAExD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvB,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC3B,IAAA,qBAAY,EAAC,MAAM,CAAC,CAAC;aACtB;YAMD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAE/C,IAAI,KAAK,EAAE;gBAET,OAAO,IAAI,CAAC;aACb;SACF;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;SACnD;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1E,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;SACnD;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,UAAU,CAAC,EAAU,EAAE,EAAU,EAAE,IAAc;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,aAAa;YACzB,MAAM,EAAE,IAAI,CAAC,cAAc;YAC3B,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;YACrC,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG;YAClF,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACrC;IACH,CAAC;IAEO,QAAQ,CAAC,MAA0B;QAGzC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QAEf,IAAI,CAAC,CAAC;QAEN,IAAI,CAAC,MAAM,EAAE;YAEX,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;YAClB,OAAO,EAAE,EAAE,EAAE;gBACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACnB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;gBAClB,OAAO,EAAE,EAAE,EAAE;oBACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;iBAC1B;aACF;SACF;aAAM;YAIL,IAAI,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE7D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1B,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAKjD,IAAI,SAAS,GAAG,MAAM;iBACnB,UAAU,CAAC,IAAI,CAAC;iBAChB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YAE/E,MAAM,cAAc,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAE;gBAChD,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACtB,OAAO,CAAC,EAAE,EAAE;oBACV,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtB,OAAO,CAAC,EAAE,EAAE;wBACV,CAAC,GAAG,CAAC,CAAC;wBACN,OAAO,CAAC,EAAE,EAAE;4BACV,IACE,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gCACnG,OAAO,CAAC,CAAC,CAAC,EACV;gCACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gCAC1B,OAAO;6BACR;yBACF;qBACF;iBACF;YACH,CAAC,CAAC;YAEF,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;YAClB,OAAO,EAAE,EAAE,EAAE;gBACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACnB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;gBAClB,OAAO,EAAE,EAAE,EAAE;oBAEX,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBACvB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE;wBAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;qBAC1B;iBACF;aACF;YAED,SAAS,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,CAAC;SACxC;IACH,CAAC;IAED,MAAM,CACJ,IAAW,EACX,MAAgG;QAEhG,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,8BAAqB,EAAC,IAAI,CAAC,OAAO,CAAC,KAAe,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAGjH,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;YACzB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YACtE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAG3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEjC,CAAC,EAAE,CAAC;YACJ,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAE1B,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM;aACP;SACF;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;;AAtdH,gCAudC;AAtdQ,yBAAc,GAAgC;IACnD,QAAQ,EAAE,CAAC;IACX,WAAW,EAAE,CAAC;CACf,CAAC", "file": "grid-layout.js", "sourcesContent": ["import type { IProgressiveTransformResult } from '@visactor/vgrammar-core';\n/*!\n * wordcloud2.js\n * http://timdream.org/wordcloud2.js/\n *\n * Copyright 2011 - 2019 Tim <PERSON> and contributors.\n * Released under the MIT license\n */\n\nimport type { IBaseLayoutOptions } from './interface';\nimport { getMaxRadiusAndCenter } from './shapes';\nimport { merge, shuffleArray } from '@visactor/vutils';\nimport { BaseLayout } from './base';\n\ninterface IGridLayoutOptions extends IBaseLayoutOptions {\n  gridSize?: number;\n\n  ellipticity?: number;\n}\n\ninterface TextInfo {\n  datum: any;\n  /** Read the pixels and save the information to the occupied array */\n  occupied: [number, number][];\n  bounds: [number, number, number, number];\n  gw: number;\n  gh: number;\n  fillTextOffsetX: number;\n  fillTextOffsetY: number;\n  fillTextWidth: number;\n  fillTextHeight: number;\n  fontSize: number;\n  fontWeight: string;\n  fontStyle: string;\n  fontFamily: string;\n  angle: number;\n  text: string;\n  distance?: number;\n  theta?: number;\n}\n\nexport class GridLayout extends BaseLayout<IGridLayoutOptions> implements IProgressiveTransformResult {\n  static defaultOptions: Partial<IGridLayoutOptions> = {\n    gridSize: 8,\n    ellipticity: 1\n  };\n\n  private gridSize: number;\n\n  /* ================== runtime vars ================== */\n  private center: [number, number];\n  private pointsAtRadius: [number, number, number][][];\n  private ngx: number;\n  private ngy: number;\n  private grid: boolean[][];\n  private maxRadius: number;\n\n  constructor(options: IGridLayoutOptions) {\n    super(merge({}, GridLayout.defaultOptions, options));\n\n    this.gridSize = Math.max(Math.floor(this.options.gridSize), 4);\n  }\n\n  private getPointsAtRadius(radius: number) {\n    if (this.pointsAtRadius[radius]) {\n      return this.pointsAtRadius[radius];\n    }\n\n    // Look for these number of points on each radius\n    const T = radius * 8;\n\n    // Getting all the points at this radius\n    let t = T;\n    const points: [number, number, number][] = [];\n\n    if (radius === 0) {\n      points.push([this.center[0], this.center[1], 0]);\n    }\n\n    while (t--) {\n      // distort the radius to put the cloud in shape\n      const rx = this.shape((t / T) * 2 * Math.PI); // 0 to 1\n\n      // Push [x, y, t] t is used solely for getTextColor()\n      points.push([\n        this.center[0] + radius * rx * Math.cos((-t / T) * 2 * Math.PI),\n        this.center[1] + radius * rx * Math.sin((-t / T) * 2 * Math.PI) * this.options.ellipticity,\n        (t / T) * 2 * Math.PI\n      ]);\n    }\n\n    this.pointsAtRadius[radius] = points;\n    return points;\n  }\n\n  private getTextInfo(item: any, shrinkRatio: number = 1): TextInfo {\n    // calculate the acutal font size\n    // fontSize === 0 means weightFactor function wants the text skipped,\n    // and size < minSize means we cannot draw the text.\n    const sizeShrinkRatio = this.options.clip ? 1 : shrinkRatio;\n    const fontSize = Math.max(Math.floor(this.getTextFontSize(item) * sizeShrinkRatio), this.options.minFontSize);\n    let word = this.getText(item) + '';\n\n    if (this.options.clip) {\n      word = word.slice(0, Math.ceil(word.length * shrinkRatio));\n    }\n\n    if (!word) {\n      return null;\n    }\n\n    // Get fontWeight that will be used to set fctx.font\n    const fontWeight = this.getTextFontWeight(item);\n    const fontStyle = this.getTextFontStyle(item);\n    const angle = this.getTextRotate ? this.getTextRotate(item) ?? 0 : 0;\n    const fontFamily = this.getTextFontFamily(item);\n\n    // eslint-disable-next-line no-undef\n    const fcanvas = document.createElement('canvas');\n    const fctx = fcanvas.getContext('2d', { willReadFrequently: true });\n\n    fctx.font = fontStyle + ' ' + fontWeight + ' ' + fontSize.toString(10) + 'px ' + fontFamily;\n\n    // Estimate the dimension of the text with measureText().\n    const fw = fctx.measureText(word).width;\n    const fh = Math.max(fontSize, fctx.measureText('m').width, fctx.measureText('\\uFF37').width);\n\n    // Create a boundary box that is larger than our estimates,\n    // so text don't get cut of (it sill might)\n    let boxWidth = fw + fh * 2;\n    let boxHeight = fh * 3;\n    const fgw = Math.ceil(boxWidth / this.gridSize);\n    const fgh = Math.ceil(boxHeight / this.gridSize);\n    boxWidth = fgw * this.gridSize;\n    boxHeight = fgh * this.gridSize;\n\n    // Calculate the proper offsets to make the text centered at\n    // the preferred position.\n\n    // This is simply half of the width.\n    const fillTextOffsetX = -fw / 2;\n    // Instead of moving the box to the exact middle of the preferred\n    // position, for Y-offset we move 0.4 instead, so Latin alphabets look\n    // vertical centered.\n    const fillTextOffsetY = -fh * 0.4;\n\n    // Calculate the actual dimension of the canvas, considering the rotation.\n    const cgh = Math.ceil(\n      (boxWidth * Math.abs(Math.sin(angle)) + boxHeight * Math.abs(Math.cos(angle))) / this.gridSize\n    );\n    const cgw = Math.ceil(\n      (boxWidth * Math.abs(Math.cos(angle)) + boxHeight * Math.abs(Math.sin(angle))) / this.gridSize\n    );\n    const width = cgw * this.gridSize;\n    const height = cgh * this.gridSize;\n\n    fcanvas.setAttribute('width', '' + width);\n    fcanvas.setAttribute('height', '' + height);\n\n    // Scale the canvas with |mu|.\n    fctx.scale(1, 1);\n    fctx.translate(width / 2, height / 2);\n    fctx.rotate(-angle);\n\n    // Once the width/height is set, ctx info will be reset.\n    // Set it again here.\n    fctx.font = fontStyle + ' ' + fontWeight + ' ' + fontSize.toString(10) + 'px ' + fontFamily;\n\n    // Fill the text into the fcanvas.\n    // XXX: We cannot because textBaseline = 'top' here because\n    // Firefox and Chrome uses different default line-height for canvas.\n    // Please read https://bugzil.la/737852#c6.\n    // Here, we use textBaseline = 'middle' and draw the text at exactly\n    // 0.5 * fontSize lower.\n    fctx.fillStyle = '#000';\n    fctx.textBaseline = 'middle';\n    fctx.fillText(word, fillTextOffsetX, fillTextOffsetY);\n\n    // Get the pixels of the text\n    const imageData = fctx.getImageData(0, 0, width, height).data;\n\n    if (this.exceedTime()) {\n      return null;\n    }\n\n    // Read the pixels and save the information to the occupied array\n    const occupied: [number, number][] = [];\n    let gx = cgw;\n    let gy;\n    const bounds: [number, number, number, number] = [cgh / 2, cgw / 2, cgh / 2, cgw / 2];\n\n    const singleGridLoop = (gx: number, gy: number, out: [number, number][]) => {\n      let y = this.gridSize;\n      while (y--) {\n        let x = this.gridSize;\n        while (x--) {\n          if (imageData[((gy * this.gridSize + y) * width + (gx * this.gridSize + x)) * 4 + 3]) {\n            out.push([gx, gy]);\n\n            if (gx < bounds[3]) {\n              bounds[3] = gx;\n            }\n            if (gx > bounds[1]) {\n              bounds[1] = gx;\n            }\n            if (gy < bounds[0]) {\n              bounds[0] = gy;\n            }\n            if (gy > bounds[2]) {\n              bounds[2] = gy;\n            }\n\n            return;\n          }\n        }\n      }\n    };\n    while (gx--) {\n      gy = cgh;\n      while (gy--) {\n        singleGridLoop(gx, gy, occupied);\n      }\n    }\n\n    // Return information needed to create the text on the real canvas\n    return {\n      datum: item,\n      occupied: occupied,\n      bounds: bounds,\n      gw: cgw,\n      gh: cgh,\n      fillTextOffsetX: fillTextOffsetX,\n      fillTextOffsetY: fillTextOffsetY,\n      fillTextWidth: fw,\n      fillTextHeight: fh,\n      fontSize,\n      fontStyle,\n      fontWeight,\n      fontFamily,\n      angle,\n      text: word\n    };\n  }\n\n  /* Help function to updateGrid */\n  private fillGridAt = (x: number, y: number) => {\n    if (x >= this.ngx || y >= this.ngy || x < 0 || y < 0) {\n      return;\n    }\n\n    this.grid[x][y] = false;\n  };\n\n  /* Update the filling information of the given space with occupied points.\n         Draw the mask on the canvas if necessary. */\n  private updateGrid = (gx: number, gy: number, gw: number, gh: number, info: TextInfo) => {\n    const occupied = info.occupied;\n\n    let i = occupied.length;\n    while (i--) {\n      const px = gx + occupied[i][0];\n      const py = gy + occupied[i][1];\n\n      if (px >= this.ngx || py >= this.ngy || px < 0 || py < 0) {\n        continue;\n      }\n\n      this.fillGridAt(px, py);\n    }\n  };\n\n  /* Determine if there is room available in the given dimension */\n  private canFitText(gx: number, gy: number, gw: number, gh: number, occupied: number[][]) {\n    // Go through the occupied points,\n    // return false if the space is not available.\n    let i = occupied.length;\n    while (i--) {\n      const px = gx + occupied[i][0];\n      const py = gy + occupied[i][1];\n\n      if (px >= this.ngx || py >= this.ngy || px < 0 || py < 0) {\n        if (!this.options.drawOutOfBound) {\n          return false;\n        }\n        continue;\n      }\n\n      if (!this.grid[px][py]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /* putWord() processes each item on the list,\n         calculate it's size and determine it's position, and actually\n         put it on the canvas. */\n  layoutWord(index: number, shrinkRatio: number = 1): boolean {\n    // get info needed to put the text onto the canvas\n    const item = this.data[index];\n    const info = this.getTextInfo(item, shrinkRatio);\n\n    // not getting the info means we shouldn't be drawing this one.\n    if (!info) {\n      return false;\n    }\n\n    if (this.exceedTime()) {\n      return false;\n    }\n\n    // If drawOutOfBound is set to false,\n    // skip the loop if we have already know the bounding box of\n    // word is larger than the canvas.\n    if (\n      !this.options.drawOutOfBound &&\n      (!this.options.shrink || info.fontSize <= this.options.minFontSize) &&\n      !this.options.clip\n    ) {\n      const bounds = info.bounds;\n      if (bounds[1] - bounds[3] + 1 > this.ngx || bounds[2] - bounds[0] + 1 > this.ngy) {\n        return false;\n      }\n    }\n\n    // Determine the position to put the text by\n    // start looking for the nearest points\n    let r = this.maxRadius + 1;\n\n    const tryToPutWordAtPoint = (gxy: [number, number, number]) => {\n      const gx = Math.floor(gxy[0] - info.gw / 2);\n      const gy = Math.floor(gxy[1] - info.gh / 2);\n      const gw = info.gw;\n      const gh = info.gh;\n\n      // If we cannot fit the text at this position, return false\n      // and go to the next position.\n      if (!this.canFitText(gx, gy, gw, gh, info.occupied)) {\n        return false;\n      }\n\n      info.distance = this.maxRadius - r;\n      info.theta = gxy[2];\n\n      // Actually put the text on the canvas\n      this.outputText(gx, gy, info);\n\n      // Mark the spaces on the grid as filled\n      this.updateGrid(gx, gy, gw, gh, info);\n\n      // Return true so some() will stop and also return true.\n      return true;\n    };\n\n    while (r--) {\n      let points = this.getPointsAtRadius(this.maxRadius - r);\n\n      if (this.options.random) {\n        points = [].concat(points);\n        shuffleArray(points);\n      }\n\n      // Try to fit the words by looking at each point.\n      // array.some() will stop and return true\n      // when putWordAtPoint() returns true.\n      // If all the points returns false, array.some() returns false.\n      const drawn = points.some(tryToPutWordAtPoint);\n\n      if (drawn) {\n        // leave putWord() and return true\n        return true;\n      }\n    }\n    if (this.options.clip) {\n      return this.layoutWord(index, shrinkRatio * 0.75);\n    } else if (this.options.shrink && info.fontSize > this.options.minFontSize) {\n      return this.layoutWord(index, shrinkRatio * 0.75);\n    }\n    // we tried all distances but text won't fit, return false\n    return false;\n  }\n\n  private outputText(gx: number, gy: number, info: TextInfo) {\n    const color = this.getTextColor(info);\n    const output = {\n      text: info.text,\n      datum: info.datum,\n      color,\n      fontStyle: info.fontStyle,\n      fontWeight: info.fontWeight,\n      fontFamily: info.fontFamily,\n      angle: info.angle,\n      width: info.fillTextWidth,\n      height: info.fillTextHeight,\n      x: (gx + info.gw / 2) * this.gridSize,\n      y: (gy + info.gh / 2) * this.gridSize + info.fillTextOffsetY + info.fontSize * 0.5,\n      fontSize: info.fontSize\n    };\n\n    this.result.push(output);\n    if (this.progressiveResult) {\n      this.progressiveResult.push(output);\n    }\n  }\n\n  private initGrid(canvas?: HTMLCanvasElement) {\n    /* Clear the canvas only if the clearCanvas is set,\n         if not, update the grid to the current canvas state */\n    this.grid = [];\n\n    let i;\n\n    if (!canvas) {\n      /* fill the grid with empty state */\n      let gx = this.ngx;\n      while (gx--) {\n        this.grid[gx] = [];\n        let gy = this.ngy;\n        while (gy--) {\n          this.grid[gx][gy] = true;\n        }\n      }\n    } else {\n      /* Determine bgPixel by creating\n                another canvas and fill the specified background color. */\n      // eslint-disable-next-line no-undef\n      let bctx = document.createElement('canvas').getContext('2d');\n\n      bctx.fillStyle = this.options.backgroundColor;\n      bctx.fillRect(0, 0, 1, 1);\n      let bgPixel = bctx.getImageData(0, 0, 1, 1).data;\n\n      /* Read back the pixels of the canvas we got to tell which part of the\n                canvas is empty.\n                (no clearCanvas only works with a canvas, not divs) */\n      let imageData = canvas\n        .getContext('2d')\n        .getImageData(0, 0, this.ngx * this.gridSize, this.ngy * this.gridSize).data;\n\n      const singleGridLoop = (gx: number, gy: number) => {\n        let y = this.gridSize;\n        while (y--) {\n          let x = this.gridSize;\n          while (x--) {\n            i = 4;\n            while (i--) {\n              if (\n                imageData[((gy * this.gridSize + y) * this.ngx * this.gridSize + (gx * this.gridSize + x)) * 4 + i] !==\n                bgPixel[i]\n              ) {\n                this.grid[gx][gy] = false;\n                return;\n              }\n            }\n          }\n        }\n      };\n\n      let gx = this.ngx;\n      while (gx--) {\n        this.grid[gx] = [];\n        let gy = this.ngy;\n        while (gy--) {\n          /* eslint no-labels: [\"error\", { \"allowLoop\": true }] */\n          singleGridLoop(gx, gy);\n          if (this.grid[gx][gy] !== false) {\n            this.grid[gx][gy] = true;\n          }\n        }\n      }\n\n      imageData = bctx = bgPixel = undefined;\n    }\n  }\n\n  layout(\n    data: any[],\n    config: { width: number; height: number; origin?: [number, number]; canvas?: HTMLCanvasElement }\n  ) {\n    this.initProgressive();\n    this.data = data;\n    this.pointsAtRadius = [];\n    this.ngx = Math.floor(config.width / this.gridSize);\n    this.ngy = Math.floor(config.height / this.gridSize);\n    const { center, maxRadius } = getMaxRadiusAndCenter(this.options.shape as string, [config.width, config.height]);\n\n    // Determine the center of the word cloud\n    this.center = config.origin\n      ? [config.origin[0] / this.gridSize, config.origin[1] / this.gridSize]\n      : [center[0] / this.gridSize, center[1] / this.gridSize];\n\n    // Maxium radius to look for space\n    this.maxRadius = Math.floor(maxRadius / this.gridSize);\n\n    this.initGrid(config.canvas);\n    this.result = [];\n\n    let i = 0;\n\n    while (i < data.length) {\n      const drawn = this.layoutWord(i);\n\n      i++;\n      this.progressiveIndex = i;\n\n      if (this.exceedTime()) {\n        break;\n      }\n    }\n\n    return this.result;\n  }\n}\n"]}