<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth URL 调试</title>
</head>
<body>
    <h1>Nodeloc OAuth URL 调试</h1>
    
    <div>
        <label>Client ID: <input type="text" id="clientId" placeholder="输入你的Client ID"></label><br><br>
        <label>域名: <input type="text" id="domain" placeholder="例如: https://yourdomain.com" value="https://yourdomain.com"></label><br><br>
        <button onclick="generateURL()">生成OAuth URL</button>
    </div>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        function generateURL() {
            const clientId = document.getElementById('clientId').value;
            const domain = document.getElementById('domain').value;
            
            if (!clientId) {
                alert('请输入Client ID');
                return;
            }
            
            // 生成随机state
            const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            
            // 构建redirect_uri
            const redirect_uri = `${domain}/oauth/nodeloc`;
            
            // 构建完整的OAuth URL
            const params = new URLSearchParams({
                'response_type': 'code',
                'client_id': clientId,
                'redirect_uri': redirect_uri,
                'scope': 'openid profile',
                'state': state
            });
            
            const oauthUrl = `https://conn.nodeloc.cc/oauth2/auth?${params.toString()}`;
            
            // 显示结果
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <h3>生成的OAuth URL:</h3>
                <p><strong>完整URL:</strong></p>
                <textarea style="width: 100%; height: 100px;">${oauthUrl}</textarea>
                
                <h3>参数分解:</h3>
                <ul>
                    <li><strong>response_type:</strong> code</li>
                    <li><strong>client_id:</strong> ${clientId}</li>
                    <li><strong>redirect_uri:</strong> ${redirect_uri}</li>
                    <li><strong>scope:</strong> openid profile</li>
                    <li><strong>state:</strong> ${state}</li>
                </ul>
                
                <p><a href="${oauthUrl}" target="_blank">点击测试此URL</a></p>
            `;
        }
    </script>
</body>
</html>
