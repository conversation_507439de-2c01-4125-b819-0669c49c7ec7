{"version": 3, "sources": ["../src/fast-layout.ts"], "names": [], "mappings": ";;;AAIA,yDAAuD;AAGvD,iCAAoC;AACpC,6CAAyC;AAsBzC,MAAa,UAAW,SAAQ,iBAA8B;IAc5D,YAAY,OAA2B;QACrC,KAAK,CAAC,IAAA,cAAK,EAAC,EAAE,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACvB,CAAC;IAGO,GAAG,CAAC,IAAa;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACtD,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAY,CAAC,EAAE;gBAC9C,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,MAAM,IAAI,GAAqB;YAC7B,KAAK;YACL,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YACrC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YACzC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YACvC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YACzC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;YAChC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;YAC9B,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SAClB,CAAC;QAEF,MAAM,MAAM,GAAG,IAAA,4BAAa,EAAC,IAAsB,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAC9B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAC7D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAE5C,OAAO,IAAe,CAAC;IACzB,CAAC;IAED,UAAU,CAAC,KAAa;QACtB,MAAM,IAAI,GAAG,GAAG,CAAC;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAEhD,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACxB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE3B,OAAO,CAAC,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;YAExC,MAAM,IAAI,IAAI,CAAC;YACf,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YACzD,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAElG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;YACrF,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAElE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACf,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC;YAE1B,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACxB;QAED,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,KAAK,CAAC;SACd;QAED,IACE,IAAI,CAAC,OAAO,CAAC,IAAI;YACjB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,EAClH;YACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,CAAC,IAAW,EAAE,MAAyC;QAC3D,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,EAAE;YACjB,OAAO,EAAE,CAAC;SACX;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5F,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE;YACvC,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAKH,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEjC,CAAC,EAAE,CAAC;YACJ,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAE1B,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,MAAM;aACP;SACF;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;;AAnIH,gCAoIC;AA1HQ,yBAAc,GAAgC;IACnD,OAAO,EAAE,KAAK;CACf,CAAC;AA2HJ,SAAS,SAAS,CAAC,CAAU,EAAE,CAAU;IACvC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE;QAClH,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "file": "fast-layout.js", "sourcesContent": ["/**\n * 主要用于小程序环境的快速布局算法\n */\nimport type { ITextAttribute } from '@visactor/vrender-core';\nimport { getTextBounds } from '@visactor/vrender-core';\nimport type { IProgressiveTransformResult } from '@visactor/vgrammar-core';\nimport type { IBaseLayoutOptions, TagItemAttribute } from './interface';\nimport { BaseLayout } from './base';\nimport { merge } from '@visactor/vutils';\nexport interface TagItem {\n  datum: any;\n  width: number;\n  height: number;\n  text: string;\n  fontSize: number;\n  fontWeight: string;\n  fontStyle: string;\n  fontFamily: string;\n  angle: number;\n  x: number;\n  y: number;\n  top: number;\n  left: number;\n}\n\ninterface IFastLayoutOptions extends IBaseLayoutOptions {\n  padding?: TagItemAttribute<number>;\n  enlarge?: boolean;\n}\n\nexport class FastLayout extends BaseLayout<IFastLayoutOptions> implements IProgressiveTransformResult {\n  private random: () => number;\n\n  /* ==================== runtime vars ======================== */\n  private center: [number, number];\n  private aspectRatio: number;\n  private maxRadius: number;\n  private width: number;\n  private height: number;\n\n  static defaultOptions: Partial<IFastLayoutOptions> = {\n    enlarge: false\n  };\n\n  constructor(options: IFastLayoutOptions) {\n    super(merge({}, FastLayout.defaultOptions, options));\n    this.random = this.options.random ? Math.random : () => 0;\n    this.aspectRatio = 1;\n  }\n\n  // 新词是否与目前layout结果重叠\n  private fit(word: TagItem) {\n    for (let i = 0, len = this.result.length; i < len; i++) {\n      if (isOverlap(word, this.result[i] as TagItem)) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  private getTextInfo(datum: any) {\n    const info: Partial<TagItem> = {\n      datum,\n      fontSize: this.getTextFontSize(datum),\n      fontWeight: this.getTextFontWeight(datum),\n      fontStyle: this.getTextFontStyle(datum),\n      fontFamily: this.getTextFontFamily(datum),\n      angle: this.getTextRotate(datum),\n      text: this.getText(datum) + '',\n      x: this.center[0],\n      y: this.center[1]\n    };\n\n    const bounds = getTextBounds(info as ITextAttribute);\n\n    info.width = bounds.width();\n    info.height = bounds.height();\n    info.top = this.center[1] - info.height + info.height * 0.21;\n    info.left = this.center[0] - info.width / 2;\n\n    return info as TagItem;\n  }\n\n  layoutWord(index: number) {\n    const step = 0.5; // 步长决定布局时间，也决定布局结果\n    const info = this.getTextInfo(this.data[index]);\n\n    let angle = 2 * Math.PI;\n    let radius = 0;\n    let left = info.left;\n    let top = info.top;\n    const width = info.width;\n    const height = info.height;\n    let rx = 1;\n    let isFit = this.fit(info);\n\n    while (!isFit && radius < this.maxRadius) {\n      // elliptic shape\n      radius += step; // spiral radius\n      rx = this.shape((radius / this.maxRadius) * 2 * Math.PI); // 0 to 1\n      angle += (this.options.random ? (this.random() > 0.5 ? 1 : -1) : index % 2 === 0 ? 1 : -1) * step;\n\n      left = this.center[0] - width / 2 + radius * rx * Math.cos(angle) * this.aspectRatio;\n      top = this.center[1] - height / 2 + radius * rx * Math.sin(angle);\n\n      info.left = left;\n      info.top = top;\n      info.x = left + width / 2;\n      info.y = top + height / 2;\n\n      isFit = this.fit(info);\n    }\n\n    if (!isFit) {\n      return false;\n    }\n\n    if (\n      this.options.clip ||\n      (info.left >= 0 && info.left + info.width <= this.width && info.top >= 0 && info.top + info.height <= this.height)\n    ) {\n      this.result.push(info);\n\n      return true;\n    }\n\n    return false;\n  }\n\n  layout(data: any[], config: { width: number; height: number }) {\n    if (!data?.length) {\n      return [];\n    }\n\n    this.initProgressive();\n    this.result = [];\n    this.maxRadius = Math.sqrt(config.width * config.width + config.height * config.height) / 2;\n    this.center = [config.width / 2, config.height / 2];\n    this.width = config.width;\n    this.height = config.height;\n    this.data = data.sort((a: any, b: any) => {\n      return this.getTextFontSize(b) - this.getTextFontSize(a);\n    });\n\n    // 将words按照fontSize排序，结果更美观\n    // words.sort((a, b) => b.fontSize - a.fontSize);\n\n    let i = 0;\n\n    while (i < data.length) {\n      const drawn = this.layoutWord(i);\n\n      i++;\n      this.progressiveIndex = i;\n\n      if (this.exceedTime()) {\n        break;\n      }\n    }\n\n    return this.result;\n  }\n}\n\n// 判断矩形是否重叠\nfunction isOverlap(a: TagItem, b: TagItem) {\n  if (a.left + a.width < b.left || a.top + a.height < b.top || a.left > b.left + b.width || a.top > b.top + b.height) {\n    return false;\n  }\n  return true;\n}\n"]}