"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.registerWordCloudShapeTransforms = exports.WORDCLOUD_SHAPE_HOOK_EVENT = void 0;

const vgrammar_core_1 = require("@visactor/vgrammar-core"), wordcloud_shape_1 = require("./wordcloud-shape");

var util_1 = require("./util");

Object.defineProperty(exports, "WORDCLOUD_SHAPE_HOOK_EVENT", {
    enumerable: !0,
    get: function() {
        return util_1.WORDCLOUD_SHAPE_HOOK_EVENT;
    }
});

const registerWordCloudShapeTransforms = () => {
    vgrammar_core_1.Factory.registerTransform("wordcloudShape", {
        transform: wordcloud_shape_1.transform,
        markPhase: "beforeJoin"
    }, !0);
};

exports.registerWordCloudShapeTransforms = registerWordCloudShapeTransforms;
//# sourceMappingURL=index.js.map