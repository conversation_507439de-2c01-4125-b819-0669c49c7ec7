{"version": 3, "sources": ["../src/filling.ts"], "names": [], "mappings": ";;;AACA,qCAA+E;AAE/E,SAAgB,OAAO,CACrB,KAAsB,EACtB,YAA8B,EAC9B,kBAA0C;IAE1C,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB,EACpB,qBAAqB,EACrB,mBAAmB,EACnB,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,EACN,KAAK,EACL,gBAAgB,EACjB,GAAG,YAAY,CAAC;IAEjB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,kBAAkB,CAAC;IAIzG,IAAI,QAAQ,GAAG,sBAAsB,CAAC;IACtC,IAAI,OAAO,GAAG,qBAAqB,CAAC;IACpC,MAAM,kBAAkB,GAAoB,EAAE,CAAC;IAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;QACrC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAGhC,QAAQ,GAAG,IAAI,CAAC,GAAG,CACjB,QAAQ,GAAG,oBAAoB,CAAC,CAAC,CAAC,QAAQ,GAAG,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,EACxF,gBAAgB,CACjB,CAAC;QACF,OAAO,GAAG,OAAO,GAAG,mBAAmB,CAAC,CAAC,CAAC,OAAO,GAAG,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC;KAC/F;IAED,OAAO,kBAAkB,CAAC;IAE1B,SAAS,YAAY,CAAC,QAAgB,EAAE,OAAe;QACrD,MAAM,YAAY,GAAoB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;YAC7D,OAAO;gBACL,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,MAAM,EAAE,CAAC;gBACT,IAAI;gBACJ,UAAU,EAAE,oBAAoB,CAAC,KAAK,CAAC;gBACvC,SAAS,EAAE,mBAAmB,CAAC,KAAK,CAAC;gBACrC,UAAU,EAAE,oBAAoB,CAAC,KAAK,CAAC;gBACvC,QAAQ;gBACR,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAC3E,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAC;gBACjC,OAAO;gBACP,OAAO,EAAE,IAAI;gBACb,KAAK;gBACL,YAAY;gBACZ,SAAS;gBACT,KAAK;aACN,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,YAAY,CAAC,CAAC;QAC1B,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,WAAW,CAAC;QAEvC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG;YACvB,EAAE,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,GAAG,YAAY,GAAG,CAAC,CAAC;YAC7C,EAAE,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,GAAG,YAAY,GAAG,CAAC,CAAC;SAC9C,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,YAAY,EAAE;YAC/C,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,YAAY,EAAE;gBAE/C,IAAA,sBAAa,EAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;gBAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC;gBAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACX,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;gBAO7C,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE;oBAEzB,IAAI,EAAE,EAAE,KAAK,YAAY,CAAC,MAAM,EAAE;wBAChC,EAAE,GAAG,CAAC,CAAC;wBACP,IAAI,MAAM,EAAE;4BACV,WAAW,CAAC,YAAY,CAAC,CAAC;yBAC3B;qBACF;oBACD,SAAS;iBACV;gBAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;gBAEhD,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;oBACtG,SAAS;iBACV;gBAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAA,2BAAkB,EAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;oBAC9E,IAAA,yBAAgB,EAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;oBACzC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;oBAEjD,IAAI,EAAE,EAAE,KAAK,YAAY,CAAC,MAAM,EAAE;wBAChC,EAAE,GAAG,CAAC,CAAC;wBACP,IAAI,MAAM,EAAE;4BACV,WAAW,CAAC,YAAY,CAAC,CAAC;yBAC3B;qBACF;iBACF;aACF;SACF;IACH,CAAC;IAED,SAAS,WAAW,CAAC,KAAsB;QACzC,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC,CAAC;IACnD,CAAC;AACH,CAAC;AA7HD,0BA6HC", "file": "filling.js", "sourcesContent": ["import type { CloudWordType, LayoutConfigType, SegmentationOutputType } from './interface';\nimport { measureSprite, isCollideWithBoard, placeWordOnBoard } from './wordle';\n\nexport function filling(\n  words: CloudWordType[],\n  layoutConfig: LayoutConfigType,\n  segmentationOutput: SegmentationOutputType\n) {\n  const {\n    size,\n    fillingTimes,\n    fillingXStep,\n    fillingYStep,\n    getFillingFontStyle,\n    getFillingFontWeight,\n    getFillingFontFamily,\n    fillingInitialFontSize,\n    fillingDeltaFontSize,\n    fillingInitialOpacity,\n    fillingDeltaOpacity,\n    fillingRotateList,\n    getFillingPadding,\n    random,\n    board,\n    minFillFoontSize\n  } = layoutConfig;\n\n  const { boardSize, shapeBounds, tempCtx: ctx, tempCanvas: canvas, randomGenerator } = segmentationOutput;\n\n  // const padding = fillingPadding\n\n  let fontSize = fillingInitialFontSize;\n  let opacity = fillingInitialOpacity;\n  const placedFillingWords: CloudWordType[] = [];\n\n  for (let i = 0; i < fillingTimes; i++) {\n    filling1Time(fontSize, opacity);\n\n    // 完成一次填充，则更新一下填充词的属性，继续下一次填充\n    fontSize = Math.max(\n      fontSize > fillingDeltaFontSize ? fontSize - fillingDeltaFontSize : fillingDeltaFontSize,\n      minFillFoontSize\n    ); // 填充词最小字号4px\n    opacity = opacity > fillingDeltaOpacity ? opacity - fillingDeltaOpacity : fillingDeltaOpacity;\n  }\n\n  return placedFillingWords;\n\n  function filling1Time(fontSize: number, opacity: number) {\n    const fillingWords: CloudWordType[] = words.map(word => {\n      const { text, color, fillingColor, hasPlaced, datum } = word;\n      return {\n        x: 0,\n        y: 0,\n        weight: 0,\n        text,\n        fontFamily: getFillingFontFamily(datum),\n        fontStyle: getFillingFontStyle(datum),\n        fontWeight: getFillingFontWeight(datum),\n        fontSize,\n        rotate: fillingRotateList[~~(randomGenerator() * fillingRotateList.length)],\n        padding: getFillingPadding(datum),\n        opacity,\n        visible: true,\n        color,\n        fillingColor,\n        hasPlaced,\n        datum\n      };\n    });\n    randomArray(fillingWords);\n    let wi = 0;\n    const { x1, y1, x2, y2 } = shapeBounds;\n    // 小范围随机一个起点\n    const [startX, startY] = [\n      x1 + ~~(randomGenerator() * fillingXStep * 2),\n      y1 + ~~(randomGenerator() * fillingYStep * 2)\n    ];\n\n    for (let y = startY; y <= y2; y += fillingYStep) {\n      for (let x = startX; x <= x2; x += fillingXStep) {\n        // 测量填充词的 bounds\n        measureSprite(canvas, ctx, fillingWords, wi);\n        const word = fillingWords[wi];\n        word.x = x;\n        word.y = y;\n        const { wordSize, bounds, hasPlaced } = word;\n\n        /*\n         * 这里有一个问题，如果一个词语一直布局不通过，就会在一次filling1Time中一直尝试布局\n         * 导致fill次数达到上限后也无法填满空隙\n         * 因此在此处跳过无法布局的核心词（一般为超场词），避免出现问题\n         */\n        if (!hasPlaced || !bounds) {\n          // 跳过未成功布局的核心词\n          if (++wi === fillingWords.length) {\n            wi = 0;\n            if (random) {\n              randomArray(fillingWords);\n            }\n          }\n          continue;\n        }\n\n        const { dTop, dBottom, dLeft, dRight } = bounds;\n        // 检测根据单词的 bounds 检测是否超出范围\n        if (word.x - dLeft < 0 || word.x + dRight > size[0] || word.y - dTop < 0 || word.y + dBottom > size[1]) {\n          continue;\n        }\n\n        if (word.hasText && word.sprite && !isCollideWithBoard(word, board, boardSize)) {\n          placeWordOnBoard(word, board, boardSize);\n          placedFillingWords.push(Object.assign({}, word));\n          // 所有单词放置完后，随机排序一下填充词\n          if (++wi === fillingWords.length) {\n            wi = 0;\n            if (random) {\n              randomArray(fillingWords);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  function randomArray(words: CloudWordType[]) {\n    return words.sort(() => randomGenerator() - 0.5);\n  }\n}\n"]}